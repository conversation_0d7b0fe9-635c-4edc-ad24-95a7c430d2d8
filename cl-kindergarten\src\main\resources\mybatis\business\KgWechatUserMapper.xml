<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgWechatUserMapper">
    
    <resultMap type="KgWechatUser" id="KgWechatUserResult">
        <result property="wechatUserId"    column="wechat_user_id"    />
        <result property="openid"    column="openid"    />
        <result property="unionid"    column="unionid"    />
        <result property="nickname"    column="nickname"    />
        <result property="avatarUrl"    column="avatar_url"    />
        <result property="gender"    column="gender"    />
        <result property="city"    column="city"    />
        <result property="province"    column="province"    />
        <result property="country"    column="country"    />
        <result property="language"    column="language"    />
        <result property="userType"    column="user_type"    />
        <result property="bindUserId"    column="bind_user_id"    />
        <result property="studentId"    column="student_id"    />
        <result property="phone"    column="phone"    />
        <result property="bindStatus"    column="bind_status"    />
        <result property="bindTime"    column="bind_time"    />
        <result property="approvedBy"    column="approved_by"    />
        <result property="approvalTime"    column="approval_time"    />
        <result property="isSubscribed"    column="is_subscribed"    />
        <result property="subscribeTime"    column="subscribe_time"    />
        <result property="unsubscribeTime"    column="unsubscribe_time"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgWechatUserVo">
        select wechat_user_id, openid, unionid, nickname, avatar_url, gender, city, province, country, language, user_type, bind_user_id, student_id, phone, bind_status, bind_time, approved_by, approval_time, is_subscribed, subscribe_time, unsubscribe_time, com_id, create_by, create_time, update_by, update_time, remark from kg_wechat_user
    </sql>

    <select id="selectKgWechatUserList" parameterType="KgWechatUser" resultMap="KgWechatUserResult">
        <include refid="selectKgWechatUserVo"/>
        <where>  
            <if test="openid != null  and openid != ''"> and openid = #{openid}</if>
            <if test="unionid != null  and unionid != ''"> and unionid = #{unionid}</if>
            <if test="nickname != null  and nickname != ''"> and nickname like concat('%', #{nickname}, '%')</if>
            <if test="avatarUrl != null  and avatarUrl != ''"> and avatar_url = #{avatarUrl}</if>
            <if test="gender != null "> and gender = #{gender}</if>
            <if test="city != null  and city != ''"> and city = #{city}</if>
            <if test="province != null  and province != ''"> and province = #{province}</if>
            <if test="country != null  and country != ''"> and country = #{country}</if>
            <if test="language != null  and language != ''"> and language = #{language}</if>
            <if test="userType != null  and userType != ''"> and user_type = #{userType}</if>
            <if test="bindUserId != null "> and bind_user_id = #{bindUserId}</if>
            <if test="studentId != null "> and student_id = #{studentId}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="bindStatus != null  and bindStatus != ''"> and bind_status = #{bindStatus}</if>
            <if test="bindTime != null "> and bind_time = #{bindTime}</if>
            <if test="approvedBy != null "> and approved_by = #{approvedBy}</if>
            <if test="approvalTime != null "> and approval_time = #{approvalTime}</if>
            <if test="isSubscribed != null "> and is_subscribed = #{isSubscribed}</if>
            <if test="subscribeTime != null "> and subscribe_time = #{subscribeTime}</if>
            <if test="unsubscribeTime != null "> and unsubscribe_time = #{unsubscribeTime}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgWechatUserById" parameterType="Long" resultMap="KgWechatUserResult">
        <include refid="selectKgWechatUserVo"/>
        where wechat_user_id = #{wechatUserId}
    </select>
        
    <insert id="insertKgWechatUser" parameterType="KgWechatUser" useGeneratedKeys="true" keyProperty="wechatUserId">
        insert into kg_wechat_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openid != null and openid != ''">openid,</if>
            <if test="unionid != null">unionid,</if>
            <if test="nickname != null">nickname,</if>
            <if test="avatarUrl != null">avatar_url,</if>
            <if test="gender != null">gender,</if>
            <if test="city != null">city,</if>
            <if test="province != null">province,</if>
            <if test="country != null">country,</if>
            <if test="language != null">language,</if>
            <if test="userType != null">user_type,</if>
            <if test="bindUserId != null">bind_user_id,</if>
            <if test="studentId != null">student_id,</if>
            <if test="phone != null">phone,</if>
            <if test="bindStatus != null">bind_status,</if>
            <if test="bindTime != null">bind_time,</if>
            <if test="approvedBy != null">approved_by,</if>
            <if test="approvalTime != null">approval_time,</if>
            <if test="isSubscribed != null">is_subscribed,</if>
            <if test="subscribeTime != null">subscribe_time,</if>
            <if test="unsubscribeTime != null">unsubscribe_time,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openid != null and openid != ''">#{openid},</if>
            <if test="unionid != null">#{unionid},</if>
            <if test="nickname != null">#{nickname},</if>
            <if test="avatarUrl != null">#{avatarUrl},</if>
            <if test="gender != null">#{gender},</if>
            <if test="city != null">#{city},</if>
            <if test="province != null">#{province},</if>
            <if test="country != null">#{country},</if>
            <if test="language != null">#{language},</if>
            <if test="userType != null">#{userType},</if>
            <if test="bindUserId != null">#{bindUserId},</if>
            <if test="studentId != null">#{studentId},</if>
            <if test="phone != null">#{phone},</if>
            <if test="bindStatus != null">#{bindStatus},</if>
            <if test="bindTime != null">#{bindTime},</if>
            <if test="approvedBy != null">#{approvedBy},</if>
            <if test="approvalTime != null">#{approvalTime},</if>
            <if test="isSubscribed != null">#{isSubscribed},</if>
            <if test="subscribeTime != null">#{subscribeTime},</if>
            <if test="unsubscribeTime != null">#{unsubscribeTime},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgWechatUser" parameterType="KgWechatUser">
        update kg_wechat_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="openid != null and openid != ''">openid = #{openid},</if>
            <if test="unionid != null">unionid = #{unionid},</if>
            <if test="nickname != null">nickname = #{nickname},</if>
            <if test="avatarUrl != null">avatar_url = #{avatarUrl},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="city != null">city = #{city},</if>
            <if test="province != null">province = #{province},</if>
            <if test="country != null">country = #{country},</if>
            <if test="language != null">language = #{language},</if>
            <if test="userType != null">user_type = #{userType},</if>
            <if test="bindUserId != null">bind_user_id = #{bindUserId},</if>
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="bindStatus != null">bind_status = #{bindStatus},</if>
            <if test="bindTime != null">bind_time = #{bindTime},</if>
            <if test="approvedBy != null">approved_by = #{approvedBy},</if>
            <if test="approvalTime != null">approval_time = #{approvalTime},</if>
            <if test="isSubscribed != null">is_subscribed = #{isSubscribed},</if>
            <if test="subscribeTime != null">subscribe_time = #{subscribeTime},</if>
            <if test="unsubscribeTime != null">unsubscribe_time = #{unsubscribeTime},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where wechat_user_id = #{wechatUserId}
    </update>

    <delete id="deleteKgWechatUserById" parameterType="Long">
        delete from kg_wechat_user where wechat_user_id = #{wechatUserId}
    </delete>

    <delete id="deleteKgWechatUserByIds" parameterType="String">
        delete from kg_wechat_user where wechat_user_id in 
        <foreach item="wechatUserId" collection="array" open="(" separator="," close=")">
            #{wechatUserId}
        </foreach>
    </delete>
    
</mapper>
<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgWechatLoginLogMapper">
    
    <resultMap type="KgWechatLoginLog" id="KgWechatLoginLogResult">
        <result property="logId"    column="log_id"    />
        <result property="openid"    column="openid"    />
        <result property="userType"    column="user_type"    />
        <result property="bindUserId"    column="bind_user_id"    />
        <result property="loginTime"    column="login_time"    />
        <result property="loginIp"    column="login_ip"    />
        <result property="loginLocation"    column="login_location"    />
        <result property="deviceInfo"    column="device_info"    />
        <result property="loginStatus"    column="login_status"    />
        <result property="errorMessage"    column="error_message"    />
        <result property="sessionKey"    column="session_key"    />
        <result property="comId"    column="com_id"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectKgWechatLoginLogVo">
        select log_id, openid, user_type, bind_user_id, login_time, login_ip, login_location, device_info, login_status, error_message, session_key, com_id, create_time from kg_wechat_login_log
    </sql>

    <select id="selectKgWechatLoginLogList" parameterType="KgWechatLoginLog" resultMap="KgWechatLoginLogResult">
        <include refid="selectKgWechatLoginLogVo"/>
        <where>  
            <if test="openid != null  and openid != ''"> and openid = #{openid}</if>
            <if test="userType != null  and userType != ''"> and user_type = #{userType}</if>
            <if test="bindUserId != null "> and bind_user_id = #{bindUserId}</if>
            <if test="loginTime != null "> and login_time = #{loginTime}</if>
            <if test="loginIp != null  and loginIp != ''"> and login_ip = #{loginIp}</if>
            <if test="loginLocation != null  and loginLocation != ''"> and login_location = #{loginLocation}</if>
            <if test="deviceInfo != null  and deviceInfo != ''"> and device_info = #{deviceInfo}</if>
            <if test="loginStatus != null  and loginStatus != ''"> and login_status = #{loginStatus}</if>
            <if test="errorMessage != null  and errorMessage != ''"> and error_message = #{errorMessage}</if>
            <if test="sessionKey != null  and sessionKey != ''"> and session_key = #{sessionKey}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgWechatLoginLogById" parameterType="Long" resultMap="KgWechatLoginLogResult">
        <include refid="selectKgWechatLoginLogVo"/>
        where log_id = #{logId}
    </select>
        
    <insert id="insertKgWechatLoginLog" parameterType="KgWechatLoginLog" useGeneratedKeys="true" keyProperty="logId">
        insert into kg_wechat_login_log
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openid != null and openid != ''">openid,</if>
            <if test="userType != null">user_type,</if>
            <if test="bindUserId != null">bind_user_id,</if>
            <if test="loginTime != null">login_time,</if>
            <if test="loginIp != null">login_ip,</if>
            <if test="loginLocation != null">login_location,</if>
            <if test="deviceInfo != null">device_info,</if>
            <if test="loginStatus != null">login_status,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="sessionKey != null">session_key,</if>
            <if test="comId != null">com_id,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openid != null and openid != ''">#{openid},</if>
            <if test="userType != null">#{userType},</if>
            <if test="bindUserId != null">#{bindUserId},</if>
            <if test="loginTime != null">#{loginTime},</if>
            <if test="loginIp != null">#{loginIp},</if>
            <if test="loginLocation != null">#{loginLocation},</if>
            <if test="deviceInfo != null">#{deviceInfo},</if>
            <if test="loginStatus != null">#{loginStatus},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="sessionKey != null">#{sessionKey},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateKgWechatLoginLog" parameterType="KgWechatLoginLog">
        update kg_wechat_login_log
        <trim prefix="SET" suffixOverrides=",">
            <if test="openid != null and openid != ''">openid = #{openid},</if>
            <if test="userType != null">user_type = #{userType},</if>
            <if test="bindUserId != null">bind_user_id = #{bindUserId},</if>
            <if test="loginTime != null">login_time = #{loginTime},</if>
            <if test="loginIp != null">login_ip = #{loginIp},</if>
            <if test="loginLocation != null">login_location = #{loginLocation},</if>
            <if test="deviceInfo != null">device_info = #{deviceInfo},</if>
            <if test="loginStatus != null">login_status = #{loginStatus},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="sessionKey != null">session_key = #{sessionKey},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where log_id = #{logId}
    </update>

    <delete id="deleteKgWechatLoginLogById" parameterType="Long">
        delete from kg_wechat_login_log where log_id = #{logId}
    </delete>

    <delete id="deleteKgWechatLoginLogByIds" parameterType="String">
        delete from kg_wechat_login_log where log_id in 
        <foreach item="logId" collection="array" open="(" separator="," close=")">
            #{logId}
        </foreach>
    </delete>
    
</mapper>
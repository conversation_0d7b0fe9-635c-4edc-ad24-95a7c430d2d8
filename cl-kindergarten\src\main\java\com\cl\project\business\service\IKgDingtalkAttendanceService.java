package com.cl.project.business.service;

import java.util.List;
import com.cl.project.business.domain.KgDingtalkAttendance;

/**
 * 钉钉打卡记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IKgDingtalkAttendanceService 
{
    /**
     * 查询钉钉打卡记录
     * 
     * @param recordId 钉钉打卡记录ID
     * @return 钉钉打卡记录
     */
    public KgDingtalkAttendance selectKgDingtalkAttendanceById(Long recordId);

    /**
     * 查询钉钉打卡记录列表
     * 
     * @param kgDingtalkAttendance 钉钉打卡记录
     * @return 钉钉打卡记录集合
     */
    public List<KgDingtalkAttendance> selectKgDingtalkAttendanceList(KgDingtalkAttendance kgDingtalkAttendance);

    /**
     * 新增钉钉打卡记录
     * 
     * @param kgDingtalkAttendance 钉钉打卡记录
     * @return 结果
     */
    public int insertKgDingtalkAttendance(KgDingtalkAttendance kgDingtalkAttendance);

    /**
     * 修改钉钉打卡记录
     * 
     * @param kgDingtalkAttendance 钉钉打卡记录
     * @return 结果
     */
    public int updateKgDingtalkAttendance(KgDingtalkAttendance kgDingtalkAttendance);

    /**
     * 批量删除钉钉打卡记录
     * 
     * @param recordIds 需要删除的钉钉打卡记录ID
     * @return 结果
     */
    public int deleteKgDingtalkAttendanceByIds(Long[] recordIds);

    /**
     * 删除钉钉打卡记录信息
     * 
     * @param recordId 钉钉打卡记录ID
     * @return 结果
     */
    public int deleteKgDingtalkAttendanceById(Long recordId);
}

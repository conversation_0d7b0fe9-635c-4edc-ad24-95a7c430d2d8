package com.cl.project.business.mapper;

import java.util.List;
import com.cl.project.business.domain.KgSalaryConfig;

/**
 * 工资配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface KgSalaryConfigMapper 
{
    /**
     * 查询工资配置
     * 
     * @param configId 工资配置ID
     * @return 工资配置
     */
    public KgSalaryConfig selectKgSalaryConfigById(Long configId);

    /**
     * 查询工资配置列表
     * 
     * @param kgSalaryConfig 工资配置
     * @return 工资配置集合
     */
    public List<KgSalaryConfig> selectKgSalaryConfigList(KgSalaryConfig kgSalaryConfig);

    /**
     * 新增工资配置
     * 
     * @param kgSalaryConfig 工资配置
     * @return 结果
     */
    public int insertKgSalaryConfig(KgSalaryConfig kgSalaryConfig);

    /**
     * 修改工资配置
     * 
     * @param kgSalaryConfig 工资配置
     * @return 结果
     */
    public int updateKgSalaryConfig(KgSalaryConfig kgSalaryConfig);

    /**
     * 删除工资配置
     * 
     * @param configId 工资配置ID
     * @return 结果
     */
    public int deleteKgSalaryConfigById(Long configId);

    /**
     * 批量删除工资配置
     * 
     * @param configIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteKgSalaryConfigByIds(Long[] configIds);
}

package com.cl.project.business.domain.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.cl.project.business.domain.KgDingtalkAttendance;
import com.cl.project.business.domain.KgStudentAttendance;

/**
 * 学生考勤概览DTO
 * 用于展示所有学生的基本信息和当日考勤状态
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public class StudentAttendanceOverviewDto {
    /** 钉钉打卡记录列表 */
    private List<KgDingtalkAttendance> dingtalkRecords;

    /**
     * 手动签到记录
     */
    private List<KgStudentAttendance> manualRecords;

    /** 学生ID */
    private Long studentId;

    /** 钉钉用户ID */
    private String dingtalkUserId;
    
    /** 学生姓名 */
    private String studentName;
    
    /** 班级ID */
    private Long classId;
    
    /** 班级名称 */
    private String className;
    
    /** 考勤日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date attendanceDate;
    
    /** 签到时间 */
    @JsonFormat(pattern = "HH:mm:ss")
    private Date checkInTime;
    
    /** 考勤状态 */
    private String attendanceStatus;
    
    /** 在校时长 */
    private BigDecimal schoolHours;
    
    /** 是否确认 */
    private Long isConfirmed;
    
    /** 是否有打卡记录 */
    private Boolean hasAttendance;
    
    /** 打卡状态描述 */
    private String attendanceStatusText;
    
    /** 数据来源 */
    private String dataSource;
    
    // 构造方法
    public StudentAttendanceOverviewDto() {
        this.hasAttendance = false;
    }
    
    // Getter和Setter方法
    public List<KgDingtalkAttendance> getDingtalkRecords() {
        return dingtalkRecords;
    }
    
    public void setDingtalkRecords(List<KgDingtalkAttendance> dingtalkRecords) {
        this.dingtalkRecords = dingtalkRecords;
    }
    
    public List<KgStudentAttendance> getManualRecords() {
        return manualRecords;
    }
    
    public void setManualRecords(List<KgStudentAttendance> manualRecords) {
        this.manualRecords = manualRecords;
    }
    
    public Long getStudentId() {
        return studentId;
    }
    
    public void setStudentId(Long studentId) {
        this.studentId = studentId;
    }
    
    public String getDingtalkUserId() {
        return dingtalkUserId;
    }
    
    public void setDingtalkUserId(String dingtalkUserId) {
        this.dingtalkUserId = dingtalkUserId;
    }
    
    public String getStudentName() {
        return studentName;
    }
    
    public void setStudentName(String studentName) {
        this.studentName = studentName;
    }
    
    public Long getClassId() {
        return classId;
    }
    
    public void setClassId(Long classId) {
        this.classId = classId;
    }
    
    public String getClassName() {
        return className;
    }
    
    public void setClassName(String className) {
        this.className = className;
    }
    
    public Date getAttendanceDate() {
        return attendanceDate;
    }
    
    public void setAttendanceDate(Date attendanceDate) {
        this.attendanceDate = attendanceDate;
    }
    
    public Date getCheckInTime() {
        return checkInTime;
    }
    
    public void setCheckInTime(Date checkInTime) {
        this.checkInTime = checkInTime;
        this.hasAttendance = checkInTime != null;
    }
    
    
    public String getAttendanceStatus() {
        return attendanceStatus;
    }
    
    public void setAttendanceStatus(String attendanceStatus) {
        this.attendanceStatus = attendanceStatus;
    }
    
    public BigDecimal getSchoolHours() {
        return schoolHours;
    }
    
    public void setSchoolHours(BigDecimal schoolHours) {
        this.schoolHours = schoolHours;
    }
    
    public Long getIsConfirmed() {
        return isConfirmed;
    }
    
    public void setIsConfirmed(Long isConfirmed) {
        this.isConfirmed = isConfirmed;
    }
    
    public Boolean getHasAttendance() {
        return hasAttendance;
    }
    
    public void setHasAttendance(Boolean hasAttendance) {
        this.hasAttendance = hasAttendance;
    }
    
    public String getAttendanceStatusText() {
        return attendanceStatusText;
    }
    
    public void setAttendanceStatusText(String attendanceStatusText) {
        this.attendanceStatusText = attendanceStatusText;
    }
    
    public String getDataSource() {
        return dataSource;
    }
    
    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }
}

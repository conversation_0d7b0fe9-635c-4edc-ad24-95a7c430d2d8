import request from '@/utils/request'

// 查询支出记录列表
export function listExpense(query) {
  return request({
    url: '/business/expense/list',
    method: 'get',
    params: query
  })
}

// 查询支出记录详细
export function getExpense(expenseId) {
  return request({
    url: '/business/expense/' + expenseId,
    method: 'get'
  })
}

// 新增支出记录
export function addExpense(data) {
  return request({
    url: '/business/expense',
    method: 'post',
    data: data
  })
}

// 修改支出记录
export function updateExpense(data) {
  return request({
    url: '/business/expense',
    method: 'put',
    data: data
  })
}

// 删除支出记录
export function delExpense(expenseId) {
  return request({
    url: '/business/expense/' + expenseId,
    method: 'delete'
  })
}

// 导出支出记录
export function exportExpense(query) {
  return request({
    url: '/business/expense/export',
    method: 'get',
    params: query
  })
}

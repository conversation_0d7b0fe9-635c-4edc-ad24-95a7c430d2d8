<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgStudentMapper">

    <resultMap type="KgStudent" id="KgStudentResult">
        <result property="studentId"    column="student_id"    />
        <result property="studentCode"    column="student_code"    />
        <result property="studentName"    column="student_name"    />
        <result property="gender"    column="gender"    />
        <result property="birthDate"    column="birth_date"    />
        <result property="idCard"    column="id_card"    />
        <result property="phone"    column="phone"    />
        <result property="parentName"    column="parent_name"    />
        <result property="parentPhone"    column="parent_phone"    />
        <result property="emergencyContact"    column="emergency_contact"    />
        <result property="emergencyPhone"    column="emergency_phone"    />
        <result property="address"    column="address"    />
        <result property="classId"    column="class_id"    />
        <result property="enrollmentDate"    column="enrollment_date"    />
        <result property="status"    column="status"    />
        <result property="faceId"    column="face_id"    />
        <result property="wechatOpenid"    column="wechat_openid"    />
        <result property="dingtalkUserId"    column="dingtalk_user_id"    />
        <result property="avatar"    column="avatar"    />
        <result property="email"    column="email"    />
        <result property="studentNumber"    column="student_number"    />
        <result property="comId"    column="com_id"    />
        <result property="className"    column="class_name"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgStudentVo">
        select s.student_id, s.student_code, s.student_name, s.gender, s.birth_date, s.id_card, s.phone, s.parent_name, s.parent_phone, s.emergency_contact, s.emergency_phone, s.address, s.class_id, s.enrollment_date, s.status, s.face_id, s.wechat_openid, s.dingtalk_user_id, s.avatar, s.email, s.student_number, s.com_id, c.class_name, s.create_by, s.create_time, s.update_by, s.update_time, s.remark
        from kg_student s
        left join kg_class c on s.class_id = c.class_id
    </sql>

    <select id="selectKgStudentList" parameterType="KgStudent" resultMap="KgStudentResult">
        <include refid="selectKgStudentVo"/>
        <where>
            <if test="studentCode != null  and studentCode != ''"> and s.student_code = #{studentCode}</if>
            <if test="studentName != null  and studentName != ''"> and s.student_name like concat('%', #{studentName}, '%')</if>
            <if test="gender != null  and gender != ''"> and s.gender = #{gender}</if>
            <if test="birthDate != null "> and s.birth_date = #{birthDate}</if>
            <if test="idCard != null  and idCard != ''"> and s.id_card = #{idCard}</if>
            <if test="phone != null  and phone != ''"> and s.phone = #{phone}</if>
            <if test="parentName != null  and parentName != ''"> and s.parent_name like concat('%', #{parentName}, '%')</if>
            <if test="parentPhone != null  and parentPhone != ''"> and s.parent_phone = #{parentPhone}</if>
            <if test="emergencyContact != null  and emergencyContact != ''"> and s.emergency_contact = #{emergencyContact}</if>
            <if test="emergencyPhone != null  and emergencyPhone != ''"> and s.emergency_phone = #{emergencyPhone}</if>
            <if test="address != null  and address != ''"> and s.address = #{address}</if>
            <if test="classId != null "> and s.class_id = #{classId}</if>
            <if test="enrollmentDate != null "> and s.enrollment_date = #{enrollmentDate}</if>
            <if test="status != null  and status != ''">
                <choose>
                    <when test="status.indexOf(',') > 0">
                        and s.status in
                        <foreach collection="status.split(',')" item="statusValue" open="(" separator="," close=")">
                            #{statusValue}
                        </foreach>
                    </when>
                    <otherwise>
                        and s.status = #{status}
                    </otherwise>
                </choose>
            </if>
            <if test="faceId != null  and faceId != ''"> and s.face_id = #{faceId}</if>
            <if test="wechatOpenid != null  and wechatOpenid != ''"> and s.wechat_openid = #{wechatOpenid}</if>
            <if test="comId != null  and comId != ''"> and s.com_id = #{comId}</if>
        </where>
    </select>

    <select id="selectKgStudentById" parameterType="Long" resultMap="KgStudentResult">
        <include refid="selectKgStudentVo"/>
        where s.student_id = #{studentId}
    </select>

    <select id="selectKgStudentByDingtalkUserId" parameterType="String" resultMap="KgStudentResult">
        <include refid="selectKgStudentVo"/>
        where s.dingtalk_user_id = #{dingtalkUserId}
    </select>

    <insert id="insertKgStudent" parameterType="KgStudent" useGeneratedKeys="true" keyProperty="studentId">
        insert into kg_student
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="studentCode != null and studentCode != ''">student_code,</if>
            <if test="studentName != null and studentName != ''">student_name,</if>
            <if test="gender != null">gender,</if>
            <if test="birthDate != null">birth_date,</if>
            <if test="idCard != null">id_card,</if>
            <if test="phone != null">phone,</if>
            <if test="parentName != null">parent_name,</if>
            <if test="parentPhone != null">parent_phone,</if>
            <if test="emergencyContact != null">emergency_contact,</if>
            <if test="emergencyPhone != null">emergency_phone,</if>
            <if test="address != null">address,</if>
            <if test="classId != null">class_id,</if>
            <if test="enrollmentDate != null">enrollment_date,</if>
            <if test="status != null">status,</if>
            <if test="faceId != null">face_id,</if>
            <if test="wechatOpenid != null">wechat_openid,</if>
            <if test="dingtalkUserId != null">dingtalk_user_id,</if>
            <if test="avatar != null">avatar,</if>
            <if test="email != null">email,</if>
            <if test="studentNumber != null">student_number,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="studentCode != null and studentCode != ''">#{studentCode},</if>
            <if test="studentName != null and studentName != ''">#{studentName},</if>
            <if test="gender != null">#{gender},</if>
            <if test="birthDate != null">#{birthDate},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="phone != null">#{phone},</if>
            <if test="parentName != null">#{parentName},</if>
            <if test="parentPhone != null">#{parentPhone},</if>
            <if test="emergencyContact != null">#{emergencyContact},</if>
            <if test="emergencyPhone != null">#{emergencyPhone},</if>
            <if test="address != null">#{address},</if>
            <if test="classId != null">#{classId},</if>
            <if test="enrollmentDate != null">#{enrollmentDate},</if>
            <if test="status != null">#{status},</if>
            <if test="faceId != null">#{faceId},</if>
            <if test="wechatOpenid != null">#{wechatOpenid},</if>
            <if test="dingtalkUserId != null">#{dingtalkUserId},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="email != null">#{email},</if>
            <if test="studentNumber != null">#{studentNumber},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgStudent" parameterType="KgStudent">
        update kg_student
        <trim prefix="SET" suffixOverrides=",">
            <if test="studentCode != null and studentCode != ''">student_code = #{studentCode},</if>
            <if test="studentName != null and studentName != ''">student_name = #{studentName},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="birthDate != null">birth_date = #{birthDate},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="parentName != null">parent_name = #{parentName},</if>
            <if test="parentPhone != null">parent_phone = #{parentPhone},</if>
            <if test="emergencyContact != null">emergency_contact = #{emergencyContact},</if>
            <if test="emergencyPhone != null">emergency_phone = #{emergencyPhone},</if>
            <if test="address != null">address = #{address},</if>
            <if test="classId != null">class_id = #{classId},</if>
            <if test="enrollmentDate != null">enrollment_date = #{enrollmentDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="faceId != null">face_id = #{faceId},</if>
            <if test="wechatOpenid != null">wechat_openid = #{wechatOpenid},</if>
            <if test="dingtalkUserId != null">dingtalk_user_id = #{dingtalkUserId},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="email != null">email = #{email},</if>
            <if test="studentNumber != null">student_number = #{studentNumber},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where student_id = #{studentId}
    </update>

    <delete id="deleteKgStudentById" parameterType="Long">
        delete from kg_student where student_id = #{studentId}
    </delete>

    <delete id="deleteKgStudentByIds" parameterType="String">
        delete from kg_student where student_id in
        <foreach item="studentId" collection="array" open="(" separator="," close=")">
            #{studentId}
        </foreach>
    </delete>

</mapper>

/* 管理员端样式 */

/* 头部信息栏 */
.header {
    background: linear-gradient(135deg, #673AB7 0%, #512DA8 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.admin-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.avatar {
    font-size: 40px;
    width: 60px;
    height: 60px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.info h2 {
    font-size: 18px;
    margin-bottom: 5px;
}

.info p {
    font-size: 14px;
    opacity: 0.9;
}

.header-actions {
    display: flex;
    gap: 10px;
}

.header-btn {
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    font-size: 18px;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    cursor: pointer;
    transition: background 0.3s ease;
}

.header-btn:hover {
    background: rgba(255,255,255,0.3);
}

/* 时间筛选 */
.time-filter {
    background: white;
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
}

.filter-tabs {
    display: flex;
    gap: 10px;
    margin-bottom: 10px;
}

.filter-tab {
    background: #f5f5f5;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-tab.active {
    background: #673AB7;
    color: white;
}

.date-range {
    text-align: center;
    color: #666;
    font-size: 14px;
}

/* 财务概览 */
.finance-overview {
    padding: 20px;
    background: #f8f9fa;
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.overview-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.card-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.card-header h3 {
    font-size: 16px;
    color: #333;
}

.trend {
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 12px;
    background: #f0f0f0;
}

.trend.up {
    background: #e8f5e8;
    color: #4CAF50;
}

.trend.down {
    background: #ffebee;
    color: #f44336;
}

.card-amount {
    font-size: 28px;
    font-weight: 600;
    margin-bottom: 15px;
}

.overview-card.income .card-amount {
    color: #4CAF50;
}

.overview-card.expense .card-amount {
    color: #f44336;
}

.overview-card.profit .card-amount {
    color: #2196F3;
}

.card-detail {
    display: flex;
    justify-content: space-between;
    font-size: 12px;
    color: #666;
}

/* 快捷操作 */
.quick-actions {
    padding: 20px;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.action-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    text-decoration: none;
    color: #333;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.action-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.action-icon {
    font-size: 32px;
    margin-bottom: 10px;
}

.action-item span {
    font-size: 14px;
    font-weight: 500;
}

/* 缴费统计 */
.payment-stats {
    padding: 20px;
    background: #f8f9fa;
}

.stats-list {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.stats-item {
    display: flex;
    align-items: center;
    padding: 20px;
    border-bottom: 1px solid #f0f0f0;
    gap: 15px;
}

.stats-item:last-child {
    border-bottom: none;
}

.class-info h4 {
    font-size: 16px;
    margin-bottom: 5px;
    color: #333;
}

.class-info p {
    font-size: 12px;
    color: #666;
}

.payment-progress {
    flex: 1;
    margin: 0 15px;
}

.progress-bar {
    width: 100%;
    height: 8px;
    background: #e0e0e0;
    border-radius: 4px;
    overflow: hidden;
    margin-bottom: 5px;
}

.progress-fill {
    height: 100%;
    background: linear-gradient(90deg, #4CAF50, #45a049);
    transition: width 0.3s ease;
}

.progress-text {
    font-size: 12px;
    color: #666;
}

.payment-amount {
    font-size: 16px;
    font-weight: 600;
    color: #4CAF50;
    white-space: nowrap;
}

/* 最新动态 */
.recent-activities {
    padding: 20px;
}

.activity-list {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.activity-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    gap: 15px;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    font-size: 20px;
    width: 40px;
    height: 40px;
    background: #f8f9fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.activity-content h4 {
    font-size: 14px;
    margin-bottom: 5px;
    color: #333;
}

.activity-content p {
    font-size: 12px;
    color: #666;
}

/* 图表区域 */
.chart-section {
    padding: 20px;
    background: #f8f9fa;
}

.chart-container {
    background: white;
    border-radius: 12px;
    padding: 40px 20px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.chart-placeholder p {
    margin-bottom: 10px;
    color: #666;
}

.chart-placeholder p:first-child {
    font-size: 24px;
    margin-bottom: 15px;
}

/* 响应式调整 */
@media (max-width: 375px) {
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .action-grid {
        grid-template-columns: 1fr;
    }
    
    .stats-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }
    
    .payment-progress {
        width: 100%;
        margin: 0;
    }
}

package com.cl.project.business.domain;

import com.cl.framework.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 移动端权限配置对象 kg_mobile_permission_config
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgMobilePermissionConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 配置ID */
    private Long configId;

    /** 角色标识 */
    @Excel(name = "角色标识")
    private String roleKey;

    /** 权限类型（attendance考勤、course课程、finance财务、student学生） */
    @Excel(name = "权限类型", readConverterExp = "a=ttendance考勤、course课程、finance财务、student学生")
    private String permissionType;

    /** 数据范围（all全部、class班级、course课程、self本人） */
    @Excel(name = "数据范围", readConverterExp = "a=ll全部、class班级、course课程、self本人")
    private String dataScope;

    /** 时间限制配置 */
    @Excel(name = "时间限制配置")
    private String timeRestriction;

    /** 是否启用位置限制（0否 1是） */
    @Excel(name = "是否启用位置限制", readConverterExp = "0=否,1=是")
    private Long locationRestriction;

    /** 是否需要审批（0否 1是） */
    @Excel(name = "是否需要审批", readConverterExp = "0=否,1=是")
    private Long approvalRequired;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    public void setConfigId(Long configId) 
    {
        this.configId = configId;
    }

    public Long getConfigId() 
    {
        return configId;
    }
    public void setRoleKey(String roleKey) 
    {
        this.roleKey = roleKey;
    }

    public String getRoleKey() 
    {
        return roleKey;
    }
    public void setPermissionType(String permissionType) 
    {
        this.permissionType = permissionType;
    }

    public String getPermissionType() 
    {
        return permissionType;
    }
    public void setDataScope(String dataScope) 
    {
        this.dataScope = dataScope;
    }

    public String getDataScope() 
    {
        return dataScope;
    }
    public void setTimeRestriction(String timeRestriction) 
    {
        this.timeRestriction = timeRestriction;
    }

    public String getTimeRestriction() 
    {
        return timeRestriction;
    }
    public void setLocationRestriction(Long locationRestriction) 
    {
        this.locationRestriction = locationRestriction;
    }

    public Long getLocationRestriction() 
    {
        return locationRestriction;
    }
    public void setApprovalRequired(Long approvalRequired) 
    {
        this.approvalRequired = approvalRequired;
    }

    public Long getApprovalRequired() 
    {
        return approvalRequired;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("configId", getConfigId())
            .append("roleKey", getRoleKey())
            .append("permissionType", getPermissionType())
            .append("dataScope", getDataScope())
            .append("timeRestriction", getTimeRestriction())
            .append("locationRestriction", getLocationRestriction())
            .append("approvalRequired", getApprovalRequired())
            .append("status", getStatus())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

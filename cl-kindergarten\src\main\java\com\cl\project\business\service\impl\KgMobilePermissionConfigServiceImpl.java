package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgMobilePermissionConfigMapper;
import com.cl.project.business.domain.KgMobilePermissionConfig;
import com.cl.project.business.service.IKgMobilePermissionConfigService;

/**
 * 移动端权限配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgMobilePermissionConfigServiceImpl implements IKgMobilePermissionConfigService 
{
    @Autowired
    private KgMobilePermissionConfigMapper kgMobilePermissionConfigMapper;

    /**
     * 查询移动端权限配置
     * 
     * @param configId 移动端权限配置ID
     * @return 移动端权限配置
     */
    @Override
    public KgMobilePermissionConfig selectKgMobilePermissionConfigById(Long configId)
    {
        return kgMobilePermissionConfigMapper.selectKgMobilePermissionConfigById(configId);
    }

    /**
     * 查询移动端权限配置列表
     * 
     * @param kgMobilePermissionConfig 移动端权限配置
     * @return 移动端权限配置
     */
    @Override
    public List<KgMobilePermissionConfig> selectKgMobilePermissionConfigList(KgMobilePermissionConfig kgMobilePermissionConfig)
    {
        return kgMobilePermissionConfigMapper.selectKgMobilePermissionConfigList(kgMobilePermissionConfig);
    }

    /**
     * 新增移动端权限配置
     * 
     * @param kgMobilePermissionConfig 移动端权限配置
     * @return 结果
     */
    @Override
    public int insertKgMobilePermissionConfig(KgMobilePermissionConfig kgMobilePermissionConfig)
    {
        kgMobilePermissionConfig.setCreateTime(DateUtils.getNowDate());
        return kgMobilePermissionConfigMapper.insertKgMobilePermissionConfig(kgMobilePermissionConfig);
    }

    /**
     * 修改移动端权限配置
     * 
     * @param kgMobilePermissionConfig 移动端权限配置
     * @return 结果
     */
    @Override
    public int updateKgMobilePermissionConfig(KgMobilePermissionConfig kgMobilePermissionConfig)
    {
        kgMobilePermissionConfig.setUpdateTime(DateUtils.getNowDate());
        return kgMobilePermissionConfigMapper.updateKgMobilePermissionConfig(kgMobilePermissionConfig);
    }

    /**
     * 批量删除移动端权限配置
     * 
     * @param configIds 需要删除的移动端权限配置ID
     * @return 结果
     */
    @Override
    public int deleteKgMobilePermissionConfigByIds(Long[] configIds)
    {
        return kgMobilePermissionConfigMapper.deleteKgMobilePermissionConfigByIds(configIds);
    }

    /**
     * 删除移动端权限配置信息
     * 
     * @param configId 移动端权限配置ID
     * @return 结果
     */
    @Override
    public int deleteKgMobilePermissionConfigById(Long configId)
    {
        return kgMobilePermissionConfigMapper.deleteKgMobilePermissionConfigById(configId);
    }
}

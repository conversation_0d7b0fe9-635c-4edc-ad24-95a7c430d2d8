package com.cl.project.business.domain;

import java.math.BigDecimal;

import com.cl.framework.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 工资配置对象 kg_salary_config
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgSalaryConfig extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 配置ID */
    private Long configId;

    /** 配置名称 */
    @Excel(name = "配置名称")
    private String configName;

    /** 配置类型（attendance_bonus出勤奖励、course_bonus课时奖励、enrollment_bonus报名奖励等） */
    @Excel(name = "配置类型", readConverterExp = "a=ttendance_bonus出勤奖励、course_bonus课时奖励、enrollment_bonus报名奖励等")
    private String configType;

    /** 班级类型（托班、小班、中班、大班） */
    @Excel(name = "班级类型", readConverterExp = "托=班、小班、中班、大班")
    private String classType;

    /** 阈值 */
    @Excel(name = "阈值")
    private BigDecimal thresholdValue;

    /** 奖励金额 */
    @Excel(name = "奖励金额")
    private BigDecimal bonusAmount;

    /** 扣除金额 */
    @Excel(name = "扣除金额")
    private BigDecimal penaltyAmount;

    /** 计算规则说明 */
    @Excel(name = "计算规则说明")
    private String calculationRule;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    public void setConfigId(Long configId) 
    {
        this.configId = configId;
    }

    public Long getConfigId() 
    {
        return configId;
    }
    public void setConfigName(String configName) 
    {
        this.configName = configName;
    }

    public String getConfigName() 
    {
        return configName;
    }
    public void setConfigType(String configType) 
    {
        this.configType = configType;
    }

    public String getConfigType() 
    {
        return configType;
    }
    public void setClassType(String classType) 
    {
        this.classType = classType;
    }

    public String getClassType() 
    {
        return classType;
    }
    public void setThresholdValue(BigDecimal thresholdValue) 
    {
        this.thresholdValue = thresholdValue;
    }

    public BigDecimal getThresholdValue() 
    {
        return thresholdValue;
    }
    public void setBonusAmount(BigDecimal bonusAmount) 
    {
        this.bonusAmount = bonusAmount;
    }

    public BigDecimal getBonusAmount() 
    {
        return bonusAmount;
    }
    public void setPenaltyAmount(BigDecimal penaltyAmount) 
    {
        this.penaltyAmount = penaltyAmount;
    }

    public BigDecimal getPenaltyAmount() 
    {
        return penaltyAmount;
    }
    public void setCalculationRule(String calculationRule) 
    {
        this.calculationRule = calculationRule;
    }

    public String getCalculationRule() 
    {
        return calculationRule;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("configId", getConfigId())
            .append("configName", getConfigName())
            .append("configType", getConfigType())
            .append("classType", getClassType())
            .append("thresholdValue", getThresholdValue())
            .append("bonusAmount", getBonusAmount())
            .append("penaltyAmount", getPenaltyAmount())
            .append("calculationRule", getCalculationRule())
            .append("status", getStatus())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px">
      <el-form-item label="统计类型" prop="reportType">
        <el-select v-model="queryParams.reportType" placeholder="请选择统计类型" clearable size="small">
          <el-option label="学生考勤统计" value="student" />
          <el-option label="教师考勤统计" value="teacher" />
          <el-option label="综合考勤统计" value="comprehensive" />
        </el-select>
      </el-form-item>
      <el-form-item label="统计日期" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          size="small">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="班级" prop="classId" v-if="queryParams.reportType === 'student'">
        <el-select v-model="queryParams.classId" placeholder="请选择班级" clearable size="small">
          <el-option
            v-for="item in classList"
            :key="item.classId"
            :label="item.className"
            :value="item.classId"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">统计查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-search"
          size="mini"
          @click="handleQuery"
          v-hasPermi="['kg:report:attendance:list']"
        >搜索</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          plain
          icon="el-icon-refresh"
          size="mini"
          @click="resetQuery"
        >重置</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['kg:report:attendance:export']"
        >导出</el-button>
      </el-col>
    </el-row>

    <!-- 统计图表区域 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="12">
        <el-card title="考勤率趋势">
          <div ref="attendanceChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
      <el-col :span="12">
        <el-card title="考勤状态分布">
          <div ref="statusChart" style="height: 300px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 统计概览卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="summary-card">
          <div class="summary-item">
            <div class="summary-value">{{ summary.totalDays }}</div>
            <div class="summary-label">总统计天数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="summary-card">
          <div class="summary-item">
            <div class="summary-value" style="color: #67C23A;">{{ summary.attendanceRate }}%</div>
            <div class="summary-label">平均出勤率</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="summary-card">
          <div class="summary-item">
            <div class="summary-value" style="color: #E6A23C;">{{ summary.lateCount }}</div>
            <div class="summary-label">迟到次数</div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="summary-card">
          <div class="summary-item">
            <div class="summary-value" style="color: #F56C6C;">{{ summary.absentCount }}</div>
            <div class="summary-label">缺勤次数</div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细统计表格 -->
    <el-card title="详细统计数据">
      <el-table v-loading="loading" :data="reportList" border>
        <el-table-column label="姓名" align="center" prop="name" />
        <el-table-column label="类型" align="center" prop="type" v-if="queryParams.reportType === 'comprehensive'" />
        <el-table-column label="班级" align="center" prop="className" v-if="queryParams.reportType === 'student'" />
        <el-table-column label="应出勤天数" align="center" prop="shouldAttendDays" />
        <el-table-column label="实际出勤天数" align="center" prop="actualAttendDays" />
        <el-table-column label="出勤率" align="center" prop="attendanceRate">
          <template slot-scope="scope">
            <el-tag :type="getAttendanceRateType(scope.row.attendanceRate)">
              {{ scope.row.attendanceRate }}%
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="迟到次数" align="center" prop="lateCount" />
        <el-table-column label="早退次数" align="center" prop="earlyLeaveCount" />
        <el-table-column label="缺勤次数" align="center" prop="absentCount" />
        <el-table-column label="请假次数" align="center" prop="leaveCount" />
        <el-table-column label="平均在园时长" align="center" prop="avgWorkTime" />
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleViewDetail(scope.row)"
            >查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 个人考勤详情对话框 -->
    <el-dialog title="个人考勤详情" :visible.sync="detailDialog" width="80%" append-to-body>
      <el-table :data="detailList" border>
        <el-table-column label="日期" align="center" prop="attendanceDate" />
        <el-table-column label="签到时间" align="center" prop="checkinTime" />
        <el-table-column label="签退时间" align="center" prop="checkoutTime" />
        <el-table-column label="在园时长" align="center" prop="workTime" />
        <el-table-column label="考勤状态" align="center" prop="attendanceStatus">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.kg_attendance_status" :value="scope.row.attendanceStatus"/>
          </template>
        </el-table-column>
        <el-table-column label="备注" align="center" prop="remark" />
      </el-table>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getAttendanceReport, getAttendanceDetail, exportAttendanceReport } from "@/api/kg/report/attendance";
import { listClass } from "@/api/kg/student/class";
import * as echarts from 'echarts';

export default {
  name: "AttendanceReport",
  dicts: ['kg_attendance_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 班级列表
      classList: [],
      // 报表数据
      reportList: [],
      // 个人详情数据
      detailList: [],
      // 详情对话框
      detailDialog: false,
      // 统计概览
      summary: {
        totalDays: 0,
        attendanceRate: 0,
        lateCount: 0,
        absentCount: 0
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        reportType: 'student',
        dateRange: [],
        classId: null
      },
      // 图表实例
      attendanceChart: null,
      statusChart: null
    };
  },
  created() {
    this.getClassList();
    this.setDefaultDateRange();
    this.getList();
  },
  mounted() {
    this.initCharts();
  },
  beforeDestroy() {
    if (this.attendanceChart) {
      this.attendanceChart.dispose();
    }
    if (this.statusChart) {
      this.statusChart.dispose();
    }
  },
  methods: {
    /** 查询班级列表 */
    getClassList() {
      listClass().then(response => {
        this.classList = response.rows;
      });
    },
    /** 设置默认日期范围（最近30天） */
    setDefaultDateRange() {
      const end = new Date();
      const start = new Date();
      start.setTime(start.getTime() - 30 * 24 * 60 * 60 * 1000);
      this.queryParams.dateRange = [
        start.toISOString().split('T')[0],
        end.toISOString().split('T')[0]
      ];
    },
    /** 查询报表数据 */
    getList() {
      this.loading = true;
      getAttendanceReport(this.queryParams).then(response => {
        this.reportList = response.rows;
        this.total = response.total;
        this.summary = response.summary;
        this.loading = false;
        this.updateCharts(response.chartData);
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.setDefaultDateRange();
      this.handleQuery();
    },
    /** 查看详情 */
    handleViewDetail(row) {
      const params = {
        id: row.id,
        type: this.queryParams.reportType,
        dateRange: this.queryParams.dateRange
      };
      getAttendanceDetail(params).then(response => {
        this.detailList = response.data;
        this.detailDialog = true;
      });
    },
    /** 导出报表 */
    handleExport() {
      this.$confirm('是否确认导出考勤统计报表?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return exportAttendanceReport(this.queryParams);
      }).then(response => {
        this.download(response.msg);
      }).catch(() => {});
    },
    /** 获取出勤率标签类型 */
    getAttendanceRateType(rate) {
      if (rate >= 95) return 'success';
      if (rate >= 80) return '';
      if (rate >= 60) return 'warning';
      return 'danger';
    },
    /** 初始化图表 */
    initCharts() {
      this.attendanceChart = echarts.init(this.$refs.attendanceChart);
      this.statusChart = echarts.init(this.$refs.statusChart);
    },
    /** 更新图表数据 */
    updateCharts(chartData) {
      if (!chartData) return;
      
      // 出勤率趋势图
      const attendanceOption = {
        title: { text: '出勤率趋势' },
        tooltip: { trigger: 'axis' },
        xAxis: {
          type: 'category',
          data: chartData.dates
        },
        yAxis: {
          type: 'value',
          max: 100,
          axisLabel: { formatter: '{value}%' }
        },
        series: [{
          name: '出勤率',
          data: chartData.attendanceRates,
          type: 'line',
          smooth: true
        }]
      };
      
      // 考勤状态分布饼图
      const statusOption = {
        title: { text: '考勤状态分布' },
        tooltip: { trigger: 'item' },
        series: [{
          name: '考勤状态',
          type: 'pie',
          radius: '50%',
          data: chartData.statusDistribution,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      };
      
      this.attendanceChart.setOption(attendanceOption);
      this.statusChart.setOption(statusOption);
    }
  }
};
</script>

<style scoped>
.summary-card {
  text-align: center;
}

.summary-item {
  padding: 20px;
}

.summary-value {
  font-size: 28px;
  font-weight: bold;
  color: #409EFF;
}

.summary-label {
  font-size: 14px;
  color: #666;
  margin-top: 8px;
}

.mb20 {
  margin-bottom: 20px;
}
</style>

package com.cl.project.business.service.impl;

import java.time.LocalDate;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.domain.KgStudentAttendance;
import com.cl.project.business.domain.KgTeacherAttendance;
import com.cl.project.business.domain.KgStudent;
import com.cl.project.business.domain.KgClass;
import com.cl.project.business.mapper.KgStudentAttendanceMapper;
import com.cl.project.business.mapper.KgTeacherAttendanceMapper;
import com.cl.project.business.mapper.KgStudentMapper;
import com.cl.project.business.mapper.KgClassMapper;
import com.cl.project.business.service.IKgAttendanceStatisticsService;
import com.cl.common.utils.DateUtils;
import com.cl.common.utils.SecurityUtils;

/**
 * 考勤统计Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class KgAttendanceStatisticsServiceImpl implements IKgAttendanceStatisticsService 
{
    @Autowired
    private KgStudentAttendanceMapper studentAttendanceMapper;
    
    @Autowired
    private KgTeacherAttendanceMapper teacherAttendanceMapper;
    
    @Autowired
    private KgStudentMapper studentMapper;
    
    @Autowired
    private KgClassMapper classMapper;

    /**
     * 获取学生考勤统计
     */
    @Override
    public Map<String, Object> getStudentAttendanceStatistics(Long classId, Long studentId, LocalDate startDate, LocalDate endDate) 
    {
        Map<String, Object> result = new HashMap<>();
        
        // 构建查询条件
        KgStudentAttendance queryParams = new KgStudentAttendance();
        if (classId != null) {
            queryParams.setClassId(classId);
        }
        if (studentId != null) {
            queryParams.setStudentId(studentId);
        }
        
        // 查询考勤记录
        List<KgStudentAttendance> attendanceList = studentAttendanceMapper.selectKgStudentAttendanceList(queryParams);
        
        // 按日期过滤
        List<KgStudentAttendance> filteredList = attendanceList.stream()
                .filter(attendance -> {
                    LocalDate attendanceDate = attendance.getAttendanceDate().toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDate();
                    return !attendanceDate.isBefore(startDate) && !attendanceDate.isAfter(endDate);
                })
                .collect(Collectors.toList());
        
        // 统计数据
        int totalDays = (int) (endDate.toEpochDay() - startDate.toEpochDay() + 1);
        int attendanceDays = filteredList.size();
        int absentDays = totalDays - attendanceDays;
        
        // 按状态分组统计
        Map<String, Long> statusCount = filteredList.stream()
                .collect(Collectors.groupingBy(
                    attendance -> attendance.getAttendanceStatus(),
                    Collectors.counting()
                ));
        
        // 计算出勤率
        double attendanceRate = totalDays > 0 ? (double) attendanceDays / totalDays * 100 : 0;
        
        result.put("totalDays", totalDays);
        result.put("attendanceDays", attendanceDays);
        result.put("absentDays", absentDays);
        result.put("attendanceRate", Math.round(attendanceRate * 100.0) / 100.0);
        result.put("statusStatistics", statusCount);
        result.put("attendanceDetails", filteredList);
        
        return result;
    }

    /**
     * 获取教师考勤统计
     */
    @Override
    public Map<String, Object> getTeacherAttendanceStatistics(Long teacherId, LocalDate startDate, LocalDate endDate) 
    {
        Map<String, Object> result = new HashMap<>();
        
        KgTeacherAttendance queryParams = new KgTeacherAttendance();
        if (teacherId != null) {
            queryParams.setTeacherId(teacherId);
        }
        
        List<KgTeacherAttendance> attendanceList = teacherAttendanceMapper.selectKgTeacherAttendanceList(queryParams);
        
        // 按日期过滤
        List<KgTeacherAttendance> filteredList = attendanceList.stream()
                .filter(attendance -> {
                    LocalDate attendanceDate = attendance.getAttendanceDate().toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDate();
                    return !attendanceDate.isBefore(startDate) && !attendanceDate.isAfter(endDate);
                })
                .collect(Collectors.toList());
        
        // 统计工作日数（排除周末）
        int workDays = calculateWorkDays(startDate, endDate);
        int attendanceDays = filteredList.size();
        int absentDays = workDays - attendanceDays;
        
        // 按状态分组统计
        Map<String, Long> statusCount = filteredList.stream()
                .collect(Collectors.groupingBy(
                    attendance -> attendance.getAttendanceStatus(),
                    Collectors.counting()
                ));
        
        // 计算出勤率
        double attendanceRate = workDays > 0 ? (double) attendanceDays / workDays * 100 : 0;
        
        result.put("workDays", workDays);
        result.put("attendanceDays", attendanceDays);
        result.put("absentDays", absentDays);
        result.put("attendanceRate", Math.round(attendanceRate * 100.0) / 100.0);
        result.put("statusStatistics", statusCount);
        result.put("attendanceDetails", filteredList);
        
        return result;
    }

    /**
     * 获取班级考勤统计
     */
    @Override
    public Map<String, Object> getClassAttendanceStatistics(Long classId, LocalDate startDate, LocalDate endDate) 
    {
        Map<String, Object> result = new HashMap<>();
        
        // 获取班级信息
        KgClass classInfo = classMapper.selectKgClassById(classId);
        if (classInfo == null) {
            throw new RuntimeException("班级不存在");
        }
        
        // 获取班级学生列表
        KgStudent studentQuery = new KgStudent();
        studentQuery.setClassId(classId);
        List<KgStudent> students = studentMapper.selectKgStudentList(studentQuery);
        
        // 获取班级考勤统计
        List<Map<String, Object>> studentStatistics = new ArrayList<>();
        double totalAttendanceRate = 0;
        
        for (KgStudent student : students) {
            Map<String, Object> studentStat = getStudentAttendanceStatistics(classId, student.getStudentId(), startDate, endDate);
            studentStat.put("studentName", student.getStudentName());
            studentStat.put("studentId", student.getStudentId());
            studentStatistics.add(studentStat);
            
            totalAttendanceRate += (Double) studentStat.get("attendanceRate");
        }
        
        // 计算班级平均出勤率
        double averageAttendanceRate = students.size() > 0 ? totalAttendanceRate / students.size() : 0;
        
        result.put("classInfo", classInfo);
        result.put("studentCount", students.size());
        result.put("averageAttendanceRate", Math.round(averageAttendanceRate * 100.0) / 100.0);
        result.put("studentStatistics", studentStatistics);
        
        return result;
    }

    /**
     * 获取考勤汇总报表
     */
    @Override
    public Map<String, Object> getAttendanceSummary(LocalDate startDate, LocalDate endDate) 
    {
        Map<String, Object> result = new HashMap<>();
        
        // 获取所有班级
        List<KgClass> classes = classMapper.selectKgClassList(new KgClass());
        
        List<Map<String, Object>> classStatistics = new ArrayList<>();
        double totalAttendanceRate = 0;
        int totalStudents = 0;
        
        for (KgClass classInfo : classes) {
            Map<String, Object> classStat = getClassAttendanceStatistics(classInfo.getClassId(), startDate, endDate);
            classStatistics.add(classStat);
            
            totalAttendanceRate += (Double) classStat.get("averageAttendanceRate") * (Integer) classStat.get("studentCount");
            totalStudents += (Integer) classStat.get("studentCount");
        }
        
        // 计算全园平均出勤率
        double overallAttendanceRate = totalStudents > 0 ? totalAttendanceRate / totalStudents : 0;
        
        result.put("startDate", startDate.toString());
        result.put("endDate", endDate.toString());
        result.put("classCount", classes.size());
        result.put("totalStudents", totalStudents);
        result.put("overallAttendanceRate", Math.round(overallAttendanceRate * 100.0) / 100.0);
        result.put("classStatistics", classStatistics);
        
        return result;
    }

    /**
     * 批量确认考勤记录
     */
    @Override
    public int confirmAttendanceBatch(List<Long> attendanceIds) 
    {
        int count = 0;
        String currentUser = SecurityUtils.getUsername();
        
        for (Long attendanceId : attendanceIds) {
            KgStudentAttendance attendance = studentAttendanceMapper.selectKgStudentAttendanceById(attendanceId);
            if (attendance != null && !"confirmed".equals(attendance.getAttendanceStatus())) {
                attendance.setAttendanceStatus("confirmed");
                // confirmedBy字段是Long类型，这里暂时设为null，实际应用中需要传入确认人的ID
                attendance.setConfirmedBy(null);
                attendance.setConfirmedTime(DateUtils.getNowDate());
                attendance.setUpdateBy(currentUser);
                attendance.setUpdateTime(DateUtils.getNowDate());
                
                studentAttendanceMapper.updateKgStudentAttendance(attendance);
                count++;
            }
        }
        
        return count;
    }

    /**
     * 生成月度考勤报表
     */
    @Override
    public String generateMonthlyReport(Integer year, Integer month) 
    {
        LocalDate startDate = LocalDate.of(year, month, 1);
        LocalDate endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());
        
        Map<String, Object> summary = getAttendanceSummary(startDate, endDate);
        
        // 这里可以集成报表生成工具（如JasperReports、POI等）
        // 现在简单返回一个文件路径
        String fileName = String.format("attendance_report_%d_%02d.xlsx", year, month);
        String filePath = "/reports/" + fileName;
        
        // TODO: 实际的报表生成逻辑
        
        return filePath;
    }

    /**
     * 考勤异常处理
     */
    @Override
    public int handleAttendanceException(Long attendanceId, String exceptionType, String reason) 
    {
        KgStudentAttendance attendance = studentAttendanceMapper.selectKgStudentAttendanceById(attendanceId);
        if (attendance == null) {
            throw new RuntimeException("考勤记录不存在");
        }
        
        // 更新考勤状态和异常信息
        attendance.setAttendanceStatus(exceptionType);
        attendance.setRemark(reason);
        attendance.setUpdateBy(SecurityUtils.getUsername());
        attendance.setUpdateTime(DateUtils.getNowDate());
        
        return studentAttendanceMapper.updateKgStudentAttendance(attendance);
    }

    /**
     * 计算学生出勤率
     */
    @Override
    public Double calculateStudentAttendanceRate(Long studentId, LocalDate startDate, LocalDate endDate) 
    {
        Map<String, Object> statistics = getStudentAttendanceStatistics(null, studentId, startDate, endDate);
        return (Double) statistics.get("attendanceRate");
    }

    /**
     * 计算班级平均出勤率
     */
    @Override
    public Double calculateClassAverageAttendanceRate(Long classId, LocalDate startDate, LocalDate endDate) 
    {
        Map<String, Object> statistics = getClassAttendanceStatistics(classId, startDate, endDate);
        return (Double) statistics.get("averageAttendanceRate");
    }

    /**
     * 获取考勤异常记录
     */
    @Override
    public List<Map<String, Object>> getAttendanceExceptions(LocalDate startDate, LocalDate endDate) 
    {
        // 查询异常考勤记录
        KgStudentAttendance queryParams = new KgStudentAttendance();
        List<KgStudentAttendance> attendanceList = studentAttendanceMapper.selectKgStudentAttendanceList(queryParams);
        
        return attendanceList.stream()
                .filter(attendance -> {
                    LocalDate attendanceDate = attendance.getAttendanceDate().toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDate();
                    return !attendanceDate.isBefore(startDate) && !attendanceDate.isAfter(endDate)
                            && ("late".equals(attendance.getAttendanceStatus()) 
                                || "early_leave".equals(attendance.getAttendanceStatus())
                                || "absent".equals(attendance.getAttendanceStatus()));
                })
                .map(attendance -> {
                    Map<String, Object> exception = new HashMap<>();
                    exception.put("attendanceId", attendance.getAttendanceId());
                    exception.put("studentId", attendance.getStudentId());
                    exception.put("studentName", attendance.getStudentName());
                    exception.put("className", attendance.getClassName());
                    exception.put("attendanceDate", attendance.getAttendanceDate());
                    exception.put("status", attendance.getAttendanceStatus());
                    exception.put("remark", attendance.getRemark());
                    return exception;
                })
                .collect(Collectors.toList());
    }

    /**
     * 获取学生考勤详情
     */
    @Override
    public Map<String, Object> getStudentAttendanceDetail(Long studentId, String month) 
    {
        LocalDate startDate = LocalDate.parse(month + "-01");
        LocalDate endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());
        
        return getStudentAttendanceStatistics(null, studentId, startDate, endDate);
    }

    /**
     * 计算工作日数量（排除周末）
     */
    private int calculateWorkDays(LocalDate startDate, LocalDate endDate) 
    {
        int workDays = 0;
        LocalDate current = startDate;
        
        while (!current.isAfter(endDate)) {
            if (current.getDayOfWeek().getValue() < 6) { // 周一到周五
                workDays++;
            }
            current = current.plusDays(1);
        }
        
        return workDays;
    }
}

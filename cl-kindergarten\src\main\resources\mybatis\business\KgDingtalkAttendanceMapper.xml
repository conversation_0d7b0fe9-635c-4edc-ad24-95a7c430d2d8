<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgDingtalkAttendanceMapper">
    
    <resultMap type="KgDingtalkAttendance" id="KgDingtalkAttendanceResult">
        <result property="recordId"    column="record_id"    />
        <result property="userId"    column="user_id"    />
        <result property="employeeId"    column="employee_id"    />
        <result property="studentId"    column="student_id"    />
        <result property="checkTime"    column="check_time"    />
        <result property="checkType"    column="check_type"    />
        <result property="locationResult"    column="location_result"    />
        <result property="locationTitle"    column="location_title"    />
        <result property="locationDetail"    column="location_detail"    />
        <result property="planCheckTime"    column="plan_check_time"    />
        <result property="isProcessed"    column="is_processed"    />
        <result property="processedTime"    column="processed_time"    />
        <result property="feeType"    column="fee_type"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgDingtalkAttendanceVo">
        select record_id, user_id, employee_id, student_id, check_time, check_type, location_result, location_title, location_detail, plan_check_time, is_processed, processed_time, fee_type, com_id, create_by, create_time, update_by, update_time, remark from kg_dingtalk_attendance
    </sql>

    <select id="selectKgDingtalkAttendanceList" parameterType="KgDingtalkAttendance" resultMap="KgDingtalkAttendanceResult">
        <include refid="selectKgDingtalkAttendanceVo"/>
        <where>  
            <if test="userId != null  and userId != ''"> and user_id = #{userId}</if>
            <if test="employeeId != null "> and employee_id = #{employeeId}</if>
            <if test="studentId != null "> and student_id = #{studentId}</if>
            <if test="checkTime != null "> and check_time = #{checkTime}</if>
            <if test="checkType != null  and checkType != ''"> and check_type = #{checkType}</if>
            <if test="locationResult != null  and locationResult != ''"> and location_result = #{locationResult}</if>
            <if test="locationTitle != null  and locationTitle != ''"> and location_title = #{locationTitle}</if>
            <if test="locationDetail != null  and locationDetail != ''"> and location_detail = #{locationDetail}</if>
            <if test="planCheckTime != null "> and plan_check_time = #{planCheckTime}</if>
            <if test="isProcessed != null "> and is_processed = #{isProcessed}</if>
            <if test="processedTime != null "> and processed_time = #{processedTime}</if>
            <if test="feeType != null  and feeType != ''"> and fee_type = #{feeType}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgDingtalkAttendanceById" parameterType="Long" resultMap="KgDingtalkAttendanceResult">
        <include refid="selectKgDingtalkAttendanceVo"/>
        where record_id = #{recordId}
    </select>
        
    <insert id="insertKgDingtalkAttendance" parameterType="KgDingtalkAttendance" useGeneratedKeys="true" keyProperty="recordId">
        insert into kg_dingtalk_attendance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">user_id,</if>
            <if test="employeeId != null">employee_id,</if>
            <if test="studentId != null">student_id,</if>
            <if test="checkTime != null">check_time,</if>
            <if test="checkType != null and checkType != ''">check_type,</if>
            <if test="locationResult != null">location_result,</if>
            <if test="locationTitle != null">location_title,</if>
            <if test="locationDetail != null">location_detail,</if>
            <if test="planCheckTime != null">plan_check_time,</if>
            <if test="isProcessed != null">is_processed,</if>
            <if test="processedTime != null">processed_time,</if>
            <if test="feeType != null">fee_type,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null and userId != ''">#{userId},</if>
            <if test="employeeId != null">#{employeeId},</if>
            <if test="studentId != null">#{studentId},</if>
            <if test="checkTime != null">#{checkTime},</if>
            <if test="checkType != null and checkType != ''">#{checkType},</if>
            <if test="locationResult != null">#{locationResult},</if>
            <if test="locationTitle != null">#{locationTitle},</if>
            <if test="locationDetail != null">#{locationDetail},</if>
            <if test="planCheckTime != null">#{planCheckTime},</if>
            <if test="isProcessed != null">#{isProcessed},</if>
            <if test="processedTime != null">#{processedTime},</if>
            <if test="feeType != null">#{feeType},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgDingtalkAttendance" parameterType="KgDingtalkAttendance">
        update kg_dingtalk_attendance
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null and userId != ''">user_id = #{userId},</if>
            <if test="employeeId != null">employee_id = #{employeeId},</if>
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="checkTime != null">check_time = #{checkTime},</if>
            <if test="checkType != null and checkType != ''">check_type = #{checkType},</if>
            <if test="locationResult != null">location_result = #{locationResult},</if>
            <if test="locationTitle != null">location_title = #{locationTitle},</if>
            <if test="locationDetail != null">location_detail = #{locationDetail},</if>
            <if test="planCheckTime != null">plan_check_time = #{planCheckTime},</if>
            <if test="isProcessed != null">is_processed = #{isProcessed},</if>
            <if test="processedTime != null">processed_time = #{processedTime},</if>
            <if test="feeType != null">fee_type = #{feeType},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where record_id = #{recordId}
    </update>

    <delete id="deleteKgDingtalkAttendanceById" parameterType="Long">
        delete from kg_dingtalk_attendance where record_id = #{recordId}
    </delete>

    <delete id="deleteKgDingtalkAttendanceByIds" parameterType="String">
        delete from kg_dingtalk_attendance where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>
    
    <!-- 查询指定员工某天的所有钉钉打卡记录 -->
    <select id="selectByEmployeeIdAndDate" resultMap="KgDingtalkAttendanceResult">
        <include refid="selectKgDingtalkAttendanceVo"/>
        where employee_id = #{employeeId}
          and check_time &gt;= #{dateFrom}
          and check_time &lt;= #{dateTo}
        order by check_time asc
    </select>
</mapper>
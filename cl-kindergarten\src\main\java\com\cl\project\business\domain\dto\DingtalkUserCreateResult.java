package com.cl.project.business.domain.dto;

/**
 * 钉钉用户创建结果
 */
public class DingtalkUserCreateResult {
    private boolean success;
    private String errMsg;
    private Integer errCode;

    public DingtalkUserCreateResult() {}

    public DingtalkUserCreateResult(boolean success, String errMsg, Integer errCode) {
        this.success = success;
        this.errMsg = errMsg;
        this.errCode = errCode;
    }

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getErrMsg() {
        return errMsg;
    }

    public void setErrMsg(String errMsg) {
        this.errMsg = errMsg;
    }

    public Integer getErrCode() {
        return errCode;
    }

    public void setErrCode(Integer errCode) {
        this.errCode = errCode;
    }
}

<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">教师考勤管理</text>
				</view>
			</view>
		</view>

		<!-- 日期和统计卡片 -->
		<view class="date-stats-card">
			<view class="date-section">
				<view class="date-nav">
					<view class="date-btn" @click="changeDate(-1)">
						<u-icon name="arrow-left" color="#667eea" size="16"></u-icon>
					</view>
					<view class="current-date" @click="showDatePickerDialog">
						<text class="date-text">{{ currentDate }}</text>
						<u-icon name="calendar" color="#667eea" size="16" class="calendar-icon"></u-icon>
					</view>
					<view class="date-btn" @click="changeDate(1)">
						<u-icon name="arrow-right" color="#667eea" size="16"></u-icon>
					</view>
				</view>
			</view>

			<view class="stats-section">
				<view class="stat-card total">
					<view class="stat-icon">👨‍🏫</view>
					<view class="stat-info">
						<text class="stat-number">{{ totalTeachers }}</text>
						<text class="stat-label">总人数</text>
					</view>
				</view>
				<view class="stat-card present">
					<view class="stat-icon">✅</view>
					<view class="stat-info">
						<text class="stat-number">{{ presentCount }}</text>
						<text class="stat-label">已签到</text>
					</view>
				</view>
				<view class="stat-card absent">
					<view class="stat-icon">❌</view>
					<view class="stat-info">
						<text class="stat-number">{{ absentCount }}</text>
						<text class="stat-label">未签到</text>
					</view>
				</view>
			</view>
		</view>

		<!-- 日期选择器 -->
		<CustomDatePicker
			:show="showDatePicker"
			:value="pickerValue"
			title="选择考勤日期"
			@confirm="onDateConfirm"
			@cancel="onDateCancel"
			@update:show="showDatePicker = $event">
		</CustomDatePicker>

		<!-- 教师考勤列表 -->
		<view class="attendance-list" v-if="!loading">
			<view class="teachers-grid">
				<view
					v-for="teacher in teacherAttendanceData"
					:key="teacher.id"
					class="teacher-card"
					:class="{
						'selection-mode': isSelectionMode,
						'selected': selectedTeachers.includes(teacher.id),
						'selectable': isSelectionMode && teacher.isConfirmed !== 1
					}"
					@click="handleTeacherClick(teacher)"
				>
					<view class="card-header">
						<view class="teacher-avatar" :class="[getStatusClass(teacher.attendanceStatus)]">
							<text class="avatar-emoji">{{ teacher.gender === '男' ? '👨‍🏫' : '👩‍🏫' }}</text>
							<view class="status-indicator" :class="[getStatusClass(teacher.attendanceStatus)]"></view>
							<!-- 选择模式下的复选框 -->
							<view v-if="isSelectionMode && teacher.isConfirmed !== 1" class="selection-checkbox" :class="{ checked: selectedTeachers.includes(teacher.id) }">
								<u-icon v-if="selectedTeachers.includes(teacher.id)" name="checkmark" color="#ffffff" size="12"></u-icon>
							</view>
						</view>
						<view class="teacher-basic">
							<text class="teacher-name">{{ teacher.name }}</text>
							<text class="teacher-position">{{ teacher.position }}</text>
						</view>
					</view>

					<view class="card-content">
						<view class="time-section">
							<view class="time-row" v-if="teacher.checkInTime">
								<view class="time-item checkin">
									<view class="time-icon">🌅</view>
									<view class="time-details">
										<text class="time-label">签到时间</text>
										<text class="time-value">{{ teacher.checkInTime }}</text>
									</view>
								</view>
							</view>

							<view class="time-row" v-if="!teacher.checkInTime">
								<view class="time-item absent">
									<view class="time-icon">⏰</view>
									<view class="time-details">
										<text class="time-label">打卡状态</text>
										<text class="time-value absent">未打卡</text>
									</view>
								</view>
							</view>
						</view>

						<view class="status-section">
							<view class="status-group">
								<!-- 考勤状态 -->
								<view class="status-badge" :class="[getStatusClass(teacher.attendanceStatus)]">
									<text class="status-text">{{ getStatusText(teacher.attendanceStatus) }}</text>
								</view>
								<!-- 确认状态 -->
								<view class="confirm-group">
									<text class="confirm-label" :class="{ confirmed: teacher.isConfirmed === 1 }">
										{{ teacher.isConfirmed === 1 ? '已确认' : '待确认' }}
									</text>
								</view>
							</view>
							<view class="work-hours" v-if="teacher.workHours">
								<text class="hours-label">工作时长</text>
								<text class="hours-value">{{ teacher.workHours }}h</text>
							</view>
						</view>
					</view>

					<!-- 教师操作按钮 -->
					<view class="teacher-actions">
						<view class="action-btn sync-btn" @click="syncSingleTeacherAttendance(teacher)">
							<view class="btn-icon">
								<u-icon name="refresh" color="#667eea" size="14"></u-icon>
							</view>
							<text class="action-text">同步考勤</text>
						</view>
						<view 
							v-if="teacher.isConfirmed !== 1"
							class="action-btn confirm-btn" 
							@click="confirmSingleTeacherAttendance(teacher)">
							<view class="btn-icon">
								<u-icon name="checkmark-circle" color="#28a745" size="14"></u-icon>
							</view>
							<text class="action-text">考勤确认</text>
						</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 加载状态 -->
		<view v-if="loading" class="loading-container">
			<text class="loading-text">加载中...</text>
		</view>

		<!-- 浮动操作按钮 -->
		<view class="floating-actions">
			<view class="fab-container">
				<view class="fab-btn sync-dingtalk-btn" @click="syncDingtalkAttendance">
					<u-icon name="sync" color="#ffffff" size="18"></u-icon>
					<text class="fab-text">同步钉钉</text>
				</view>
				<view class="fab-btn" :class="{ active: isSelectionMode }" @click="toggleSelectionMode">
					<u-icon :name="isSelectionMode ? 'checkmark' : 'checkbox'" color="#ffffff" size="18"></u-icon>
					<text class="fab-text">{{ isSelectionMode ? `批量确认(${selectedTeachers.length})` : '批量确认' }}</text>
				</view>
			</view>
		</view>

		<!-- 选择模式提示 -->
		<view v-if="isSelectionMode" class="selection-tip">
			<view class="tip-content">
				<u-icon name="info-circle" color="#667eea" size="16"></u-icon>
				<text class="tip-text">请选择需要确认的未确认教师</text>
				<view class="tip-actions">
					<text class="cancel-selection" @click="cancelSelection">取消</text>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { toast, useRouter } from '@/utils/utils.js'
import {
	getTeacherAttendanceOverview,
	batchTeacherCheckin,
	batchConfirmAttendance,
	getAllTeacherList,
	syncAttendanceByUsers
} from '@/api/teacherAttendance.js'
import CustomDatePicker from '@/components/CustomDatePicker/CustomDatePicker.vue'

export default {
	components: {
		CustomDatePicker
	},
	data() {
		return {
			currentDate: '',
			currentDateValue: '', // YYYY-MM-DD格式的日期
			isSelectionMode: false,
			selectedTeachers: [],
			totalTeachers: 0,
			presentCount: 0,
			absentCount: 0,
			teacherAttendanceData: [],
			loading: false,
			allTeacherList: [], // 所有教师列表
			// 日期选择器相关
			showDatePicker: false,
			pickerValue: '',
			minDate: '',
			maxDate: ''
		}
	},
	onLoad() {
		this.initCurrentDate()
		this.initDatePicker()
		this.loadTeacherAttendanceData()
		this.loadAllTeacherList()
	},
	methods: {
		// 格式化日期显示
		formatDateDisplay(date) {
			const year = date.getFullYear()
			const month = String(date.getMonth() + 1).padStart(2, '0')
			const day = String(date.getDate()).padStart(2, '0')
			const weekdays = ['日', '一', '二', '三', '四', '五', '六']
			const weekday = weekdays[date.getDay()]
			
			return {
				display: `${year}年${month}月${day}日 周${weekday}`,
				value: `${year}-${month}-${day}`
			}
		},

		initCurrentDate() {
			const now = new Date()
			const dateInfo = this.formatDateDisplay(now)
			this.currentDate = dateInfo.display
			this.currentDateValue = dateInfo.value
		},
		
		// 初始化日期选择器
		initDatePicker() {
			// 设置选择器默认值为当前日期
			this.pickerValue = new Date(this.currentDateValue).getTime()
		},
		
		// 显示日期选择器
		showDatePickerDialog() {
			this.showDatePicker = true
		},
		
		// 日期选择确认
		onDateConfirm(e) {
			const selectedDate = new Date(e.value)
			const dateInfo = this.formatDateDisplay(selectedDate)
			
			this.currentDate = dateInfo.display
			this.currentDateValue = dateInfo.value
			this.pickerValue = e.value
			this.showDatePicker = false
			
			// 显示加载提示
			uni.showLoading({ title: '加载考勤数据...' })
			
			// 重新加载数据
			this.loadTeacherAttendanceData().finally(() => {
				uni.hideLoading()
			})
		},
		
		// 日期选择取消
		onDateCancel() {
			this.showDatePicker = false
		},
		
		goBack() {
			uni.navigateBack()
		},
		
		changeDate(direction) {
			const currentDate = new Date(this.currentDateValue)
			currentDate.setDate(currentDate.getDate() + direction)
			
			const dateInfo = this.formatDateDisplay(currentDate)
			this.currentDate = dateInfo.display
			this.currentDateValue = dateInfo.value
			// 更新选择器的值
			this.pickerValue = currentDate.getTime()
			
			// 显示加载提示
			uni.showLoading({ title: '加载考勤数据...' })
			
			// 重新加载数据
			this.loadTeacherAttendanceData().finally(() => {
				uni.hideLoading()
			})
		},
		
		getStatusText(attendanceStatus) {
			// 根据web端的状态映射
			const statusMap = {
				'1': '出勤',
				'2': '迟到', 
				'3': '缺勤',
				'4': '请假',
				'5': '早退',
				'6': '病假',
				'8': '休假'
			}
			return statusMap[attendanceStatus] || '未知'
		},
		
		getStatusClass(attendanceStatus) {
			// 根据web端的样式映射
			const classMap = {
				'1': 'present',  // 出勤 - 绿色
				'2': 'warning',  // 迟到 - 橙色
				'3': 'absent',   // 缺勤 - 红色
				'4': 'leave',    // 请假 - 蓝色
				'5': 'warning',  // 早退 - 橙色
				'6': 'leave',    // 病假 - 蓝色
				'8': 'leave'     // 休假 - 蓝色
			}
			return classMap[attendanceStatus] || 'absent'
		},
		
		// 获取显示状态文本（已打卡/未打卡）
		getDisplayStatusText(status) {
			const statusMap = {
				present: '已打卡',
				absent: '未打卡',
				leave: '请假'
			}
			return statusMap[status] || '未打卡'
		},

		getWorkHours(teacher) {
			if (!teacher.workHours) return '--'
			return `${teacher.workHours}h`
		},
		
		editAttendance(teacher) {
			toast(`编辑 ${teacher.name} 的考勤记录`)
		},
		
		toggleSelectionMode() {
			if (this.isSelectionMode) {
				// 确认批量确认
				if (this.selectedTeachers.length === 0) {
					toast('请选择需要确认的教师')
					return
				}
				this.batchConfirm()
			} else {
				// 进入选择模式
				this.isSelectionMode = true
				this.selectedTeachers = []
			}
		},

		cancelSelection() {
			this.isSelectionMode = false
			this.selectedTeachers = []
		},

		handleTeacherClick(teacher) {
			if (this.isSelectionMode) {
				if (teacher.isConfirmed !== 1) {
					// 切换选择状态
					const index = this.selectedTeachers.indexOf(teacher.id)
					if (index > -1) {
						this.selectedTeachers.splice(index, 1)
					} else {
						this.selectedTeachers.push(teacher.id)
					}
				}
			}
			// 移除原来的非选择模式下显示详情的逻辑
		},
		
		// 单个教师同步考勤
		async syncSingleTeacherAttendance(teacher) {
			if (!teacher.dingtalkUserId) {
				toast('该教师未绑定钉钉账号，无法同步')
				return
			}
			
			uni.showModal({
				title: '同步考勤',
				content: `确定同步 ${teacher.name} 的考勤数据吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							uni.showLoading({ title: '同步中...' })
							
							const syncData = {
								workDateFrom: this.currentDateValue + ' 00:00:00',
								workDateTo: this.currentDateValue + ' 23:59:59',
								userIdList: [teacher.dingtalkUserId]
							}
							
							const response = await syncAttendanceByUsers(syncData)
							if (response.code === 200) {
								toast(`同步成功，共处理 ${response.data} 条记录`)
								// 重新加载数据
								await this.loadTeacherAttendanceData()
							} else {
								toast(response.msg || '同步失败')
							}
						} catch (error) {
							console.error('同步考勤失败:', error)
							toast('同步考勤失败，请重试')
						} finally {
							uni.hideLoading()
						}
					}
				}
			})
		},
		
		// 单个教师考勤确认
		async confirmSingleTeacherAttendance(teacher) {
			// 检查是否已确认
			if (teacher.isConfirmed === 1) {
				toast('该教师考勤已确认')
				return
			}
			
			uni.showModal({
				title: '考勤确认',
				content: `确定确认 ${teacher.name} 的考勤记录吗？${!teacher.manualRecords || teacher.manualRecords.length === 0 ? '（将先生成考勤记录）' : ''}`,
				success: async (res) => {
					if (res.confirm) {
						try {
							uni.showLoading({ title: '处理中...' })
							
							let attendanceIds = [];
							
							// 检查是否有手动考勤记录
							if (teacher.manualRecords && teacher.manualRecords.length > 0) {
								// 收集manualRecords中的attendanceId
								attendanceIds = teacher.manualRecords
									.map(record => record.attendanceId)
									.filter(id => id != null)
									.map(id => parseInt(id));
							}
							
							// 如果没有手动考勤记录，先生成考勤记录
							if (attendanceIds.length === 0) {
								const checkinData = {
									teacherIds: [parseInt(teacher.id)],
									attendanceDate: this.currentDateValue,
									attendanceStatus: '1', // 签到状态
									remark: '管理员生成考勤记录'
								}
								
								const checkinResponse = await batchTeacherCheckin(checkinData)
								if (checkinResponse.code !== 200) {
									toast(checkinResponse.msg || '生成考勤记录失败')
									return
								}
								
								// 重新获取教师数据以获取新生成的手动考勤记录
								await this.loadTeacherAttendanceData()
								
								// 从更新后的数据中找到对应教师的manualRecords
								const updatedTeacher = this.teacherAttendanceData.find(t => t.id === teacher.id)
								if (updatedTeacher && updatedTeacher.manualRecords && updatedTeacher.manualRecords.length > 0) {
									attendanceIds = updatedTeacher.manualRecords
										.map(record => record.attendanceId)
										.filter(id => id != null)
										.map(id => parseInt(id));
								} else {
									toast('无法获取生成的考勤记录ID')
									return
								}
							}
							
							// 调用批量确认，将手动考勤记录标记为"已确认"
							const confirmResponse = await batchConfirmAttendance({ attendanceIds })
							if (confirmResponse.code === 200) {
								toast('考勤确认成功')
								// 重新加载数据
								await this.loadTeacherAttendanceData()
							} else {
								toast(confirmResponse.msg || '考勤确认失败')
							}
						} catch (error) {
							console.error('考勤确认失败:', error)
							toast('考勤确认失败，请重试')
						} finally {
							uni.hideLoading()
						}
					}
				}
			})
		},
		
		// 同步钉钉考勤（所有教师）
		async syncDingtalkAttendance() {
			uni.showModal({
				title: '同步钉钉考勤',
				content: `确定同步当前日期(${this.currentDate})所有教师的钉钉考勤数据吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							uni.showLoading({ title: '同步中...' })
							
							const syncData = {
								workDateFrom: this.currentDateValue + ' 00:00:00',
								workDateTo: this.currentDateValue + ' 23:59:59',
								userIdList: [] // 空数组表示同步所有用户
							}
							
							const response = await syncAttendanceByUsers(syncData)
							if (response.code === 200) {
								toast(`同步成功，共处理 ${response.data} 条记录`)
								// 重新加载数据
								await this.loadTeacherAttendanceData()
							} else {
								toast(response.msg || '同步失败')
							}
						} catch (error) {
							console.error('同步钉钉考勤失败:', error)
							toast('同步钉钉考勤失败，请重试')
						} finally {
							uni.hideLoading()
						}
					}
				}
			})
		},

		async batchConfirm() {
			if (this.selectedTeachers.length === 0) {
				toast('请选择需要确认的教师')
				return
			}

			uni.showModal({
				title: '批量确认',
				content: `确定确认选中的 ${this.selectedTeachers.length} 名教师的考勤记录吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							uni.showLoading({ title: '确认中...' })
							
							// 收集所有选中教师的手动考勤记录ID
							const attendanceIds = []
							for (const teacherId of this.selectedTeachers) {
								const teacher = this.teacherAttendanceData.find(t => t.id === teacherId)
								if (teacher && teacher.manualRecords && teacher.manualRecords.length > 0) {
									// 收集该教师的手动考勤记录ID
									const ids = teacher.manualRecords
										.map(record => record.attendanceId)
										.filter(id => id != null)
										.map(id => parseInt(id))
									attendanceIds.push(...ids)
								} else {
									// 如果没有手动考勤记录，先生成一条
									const checkinData = {
										teacherIds: [parseInt(teacherId)],
										attendanceDate: this.currentDateValue,
										attendanceStatus: '1', // 签到状态
										remark: '小程序批量生成'
									}
									
									const checkinResponse = await batchTeacherCheckin(checkinData)
									if (checkinResponse.code === 200) {
										// 重新获取该教师的数据以获取新生成的考勤记录ID
										await this.loadTeacherAttendanceData()
										const updatedTeacher = this.teacherAttendanceData.find(t => t.id === teacherId)
										if (updatedTeacher && updatedTeacher.manualRecords && updatedTeacher.manualRecords.length > 0) {
											const newIds = updatedTeacher.manualRecords
												.map(record => record.attendanceId)
												.filter(id => id != null)
												.map(id => parseInt(id))
											attendanceIds.push(...newIds)
										}
									}
								}
							}
							
							// 批量确认考勤记录
							if (attendanceIds.length > 0) {
								const confirmResponse = await batchConfirmAttendance({ attendanceIds })
								if (confirmResponse.code === 200) {
									toast(`批量确认成功，共处理 ${attendanceIds.length} 条记录`)
									// 重新加载数据
									await this.loadTeacherAttendanceData()
								} else {
									toast(confirmResponse.msg || '批量确认失败')
								}
							} else {
								toast('没有找到可确认的考勤记录')
							}
						} catch (error) {
							console.error('批量确认失败:', error)
							toast('批量确认失败，请重试')
						} finally {
							uni.hideLoading()
							// 退出选择模式
							this.cancelSelection()
						}
					}
				}
			})
		},

		// 加载教师考勤概览数据
		async loadTeacherAttendanceData() {
			try {
				this.loading = true
				const params = {
					attendanceDate: this.currentDateValue,
					pageNum: 1,
					pageSize: 100 // 小程序端一次性加载所有数据
				}
				
				const response = await getTeacherAttendanceOverview(params)
				if (response.code === 200) {
					this.teacherAttendanceData = this.formatTeacherData(response.rows || [])
					this.calculateStats()
				} else {
					console.error('获取教师考勤数据失败:', response)
					toast('获取数据失败')
				}
			} catch (error) {
				console.error('加载教师考勤数据失败:', error)
				toast('网络错误，请重试')
			} finally {
				this.loading = false
			}
		},
		
		// 加载所有教师列表
		async loadAllTeacherList() {
			try {
				const response = await getAllTeacherList()
				if (response.code === 200) {
					this.allTeacherList = response.data || []
				}
			} catch (error) {
				console.error('加载教师列表失败:', error)
			}
		},
		
		// 格式化教师数据以适配小程序端显示
		formatTeacherData(rawData) {
			return rawData.map(item => {
				return {
					id: item.teacherId,
					name: item.teacherName,
					employeeNo: item.employeeNo || '',
					position: item.position || '',
					gender: item.gender || '女',
					checkInTime: item.checkInTime || '', // 钉钉打卡时间
					checkOutTime: item.checkOutTime || '',
					attendanceStatus: item.attendanceStatus,
					attendanceId: item.attendanceId,
					workHours: item.workHours,
					isConfirmed: item.isConfirmed, // 确认状态
					dingtalkUserId: item.dingtalkUserId,
					manualRecords: item.manualRecords || [], // 手动考勤记录列表
					leaveReason: this.getLeaveReason(item.attendanceStatus)
				}
			})
		},
		
		// 获取请假原因（保留用于数据格式化）
		getLeaveReason(attendanceStatus) {
			const leaveReasons = {
				'4': '请假',
				'6': '病假', 
				'8': '休假'
			}
			return leaveReasons[attendanceStatus] || ''
		},

		calculateStats() {
			let total = 0
			let present = 0
			let absent = 0

			this.teacherAttendanceData.forEach(teacher => {
				total++
				if (teacher.attendanceStatus === '1') {
					present++ // 出勤状态算已到
				} else {
					absent++ // 其他状态算未到（包括缺勤、请假等）
				}
			})

			this.totalTeachers = total
			this.presentCount = present
			this.absentCount = absent
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 0 30rpx;
	padding-top: var(--status-bar-height, 44rpx);
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.header-content {
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.nav-left, .nav-right {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: rgba(255, 255, 255, 0.3);
	}
}

.header-title {
	flex: 1;
	text-align: center;
}

.title-text {
	display: block;
	font-size: 36rpx;
	font-weight: 700;
	color: #ffffff;
	margin-bottom: 8rpx;
}

.subtitle-text {
	display: block;
	font-size: 24rpx;
	color: rgba(255, 255, 255, 0.8);
	font-weight: 400;
}

/* 日期统计卡片 */
.date-stats-card {
	margin: 30rpx;
	background: #ffffff;
	border-radius: 24rpx;
	padding: 40rpx;
	box-shadow: 0 12rpx 40rpx rgba(0, 0, 0, 0.1);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.date-section {
	margin-bottom: 40rpx;
}

.date-nav {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 40rpx;
}

.date-btn {
	width: 60rpx;
	height: 60rpx;
	border-radius: 50%;
	background: rgba(102, 126, 234, 0.1);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: rgba(102, 126, 234, 0.2);
	}
}

.current-date {
	text-align: center;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	padding: 16rpx 24rpx;
	border-radius: 24rpx;
	background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
	border: 2rpx solid rgba(102, 126, 234, 0.1);
	transition: all 0.3s ease;
	position: relative;
	cursor: pointer;

	&:active {
		transform: scale(0.98);
		background: linear-gradient(135deg, rgba(102, 126, 234, 0.1) 0%, rgba(118, 75, 162, 0.1) 100%);
		border-color: rgba(102, 126, 234, 0.2);
	}

	&::after {
		content: '';
		position: absolute;
		top: 50%;
		right: 12rpx;
		transform: translateY(-50%);
		width: 0;
		height: 0;
		border-left: 8rpx solid transparent;
		border-right: 8rpx solid transparent;
		border-top: 8rpx solid rgba(102, 126, 234, 0.5);
		transition: all 0.3s ease;
	}

	&:active::after {
		border-top-color: rgba(102, 126, 234, 0.8);
	}
}

.calendar-icon {
	opacity: 0.7;
	transition: all 0.3s ease;
}

.date-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.stats-section {
	display: flex;
	gap: 20rpx;
}

.stat-card {
	flex: 1;
	padding: 30rpx 20rpx;
	border-radius: 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16rpx;
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 6rpx;
	}

	&.total {
		background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
		&::before { background: linear-gradient(90deg, #2196f3 0%, #1976d2 100%); }
	}

	&.present {
		background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
		&::before { background: linear-gradient(90deg, #4caf50 0%, #388e3c 100%); }
	}

	&.absent {
		background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
		&::before { background: linear-gradient(90deg, #f44336 0%, #d32f2f 100%); }
	}
}

.stat-icon {
	font-size: 32rpx;
}

.stat-info {
	text-align: center;
}

.stat-number {
	display: block;
	font-size: 36rpx;
	font-weight: 700;
	color: #333333;
	margin-bottom: 8rpx;
}

.stat-label {
	display: block;
	font-size: 24rpx;
	color: #666666;
	font-weight: 500;
}

/* 考勤列表 */
.attendance-list {
	padding: 0 30rpx 200rpx;
}

/* 教师网格 */
.teachers-grid {
	display: grid;
	grid-template-columns: 1fr;
	gap: 24rpx;
}

.teacher-card {
	background: linear-gradient(135deg, #ffffff 0%, #fafbff 100%);
	border-radius: 20rpx;
	padding: 32rpx;
	margin-bottom: 20rpx;
	box-shadow: 
		0 8rpx 32rpx rgba(102, 126, 234, 0.08),
		0 2rpx 8rpx rgba(0, 0, 0, 0.04);
	border: 1rpx solid rgba(255, 255, 255, 0.9);
	transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: 0;
		right: 0;
		height: 4rpx;
		background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #667eea 100%);
		opacity: 0;
		transition: opacity 0.3s ease;
	}

	&:active {
		transform: translateY(-2rpx);
		box-shadow: 
			0 12rpx 40rpx rgba(102, 126, 234, 0.12),
			0 4rpx 12rpx rgba(0, 0, 0, 0.06);

		&::before {
			opacity: 1;
		}
	}

	&.selection-mode {
		&.selectable {
			border: 2rpx solid rgba(102, 126, 234, 0.2);
			box-shadow: 
				0 8rpx 32rpx rgba(102, 126, 234, 0.12),
				0 2rpx 8rpx rgba(0, 0, 0, 0.04);

			&:active {
				transform: scale(0.98);
			}

			&.selected {
				border-color: #667eea;
				background: linear-gradient(135deg, rgba(102, 126, 234, 0.03) 0%, rgba(118, 75, 162, 0.03) 100%);
				box-shadow: 
					0 12rpx 40rpx rgba(102, 126, 234, 0.15),
					0 4rpx 12rpx rgba(102, 126, 234, 0.08);

				&::before {
					opacity: 1;
				}
			}
		}

		&:not(.selectable) {
			opacity: 0.5;
			pointer-events: none;
			filter: grayscale(0.3);
		}
	}
}

.card-header {
	display: flex;
	align-items: center;
	gap: 20rpx;
	margin-bottom: 24rpx;
}

.teacher-avatar {
	width: 88rpx;
	height: 88rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	font-size: 36rpx;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);

	&.present {
		background: linear-gradient(135deg, #e8f8f5 0%, #d1f2eb 100%);
		border: 3rpx solid rgba(40, 167, 69, 0.2);
	}

	&.absent {
		background: linear-gradient(135deg, #ffeaa7 0%, #fab1a0 100%);
		border: 3rpx solid rgba(244, 67, 54, 0.2);
	}

	&.leave {
		background: linear-gradient(135deg, #a29bfe 0%, #6c5ce7 100%);
		border: 3rpx solid rgba(255, 152, 0, 0.2);
	}

	&:hover {
		transform: scale(1.05);
		box-shadow: 0 6rpx 20rpx rgba(0, 0, 0, 0.15);
	}
}

.avatar-emoji {
	font-size: 36rpx;
	filter: drop-shadow(0 2rpx 4rpx rgba(0, 0, 0, 0.1));
}

.status-indicator {
	position: absolute;
	bottom: 4rpx;
	right: 4rpx;
	width: 28rpx;
	height: 28rpx;
	border-radius: 50%;
	border: 3rpx solid #ffffff;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
	transition: all 0.3s ease;

	&.present { 
		background: linear-gradient(135deg, #4caf50 0%, #388e3c 100%);
		box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.4);
	}
	&.absent { 
		background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
		box-shadow: 0 2rpx 8rpx rgba(244, 67, 54, 0.4);
		animation: pulse 2s infinite;
	}
	&.leave { 
		background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
		box-shadow: 0 2rpx 8rpx rgba(255, 152, 0, 0.4);
	}
}

@keyframes pulse {
	0% { transform: scale(1); }
	50% { transform: scale(1.1); }
	100% { transform: scale(1); }
}

.teacher-basic {
	flex: 1;
}

.teacher-name {
	display: block;
	font-size: 34rpx;
	font-weight: 700;
	color: #333333;
	margin-bottom: 8rpx;
}

.teacher-position {
	display: block;
	font-size: 26rpx;
	color: #667eea;
	font-weight: 600;
	margin-bottom: 6rpx;
}

.card-content {
	display: flex;
	justify-content: space-between;
	align-items: flex-start;
	gap: 20rpx;
}

.time-section {
	flex: 1;
}

.time-row {
	margin-bottom: 16rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.time-item {
	display: flex;
	align-items: center;
	gap: 16rpx;
}

.time-icon {
	width: 40rpx;
	height: 40rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 20rpx;
	background: rgba(102, 126, 234, 0.1);
}

.time-details {
	flex: 1;
}

.time-label {
	display: block;
	font-size: 22rpx;
	color: #666666;
	margin-bottom: 4rpx;
}

.time-value {
	display: block;
	font-size: 26rpx;
	font-weight: 600;
	color: #333333;

	&.absent {
		color: #f44336;
	}
}

.status-section {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 12rpx;
}

.status-group {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
	gap: 8rpx;
}

.status-badge {
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	font-size: 22rpx;

	&.present {
		background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
	}

	&.absent {
		background: linear-gradient(135deg, #ffebee 0%, #ffcdd2 100%);
	}

	&.leave {
		background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
	}
}

.status-text {
	font-size: 20rpx;
	font-weight: 600;

	.status-badge.present & { color: #4caf50; }
	.status-badge.absent & { color: #f44336; }
	.status-badge.leave & { color: #ff9800; }
}

.confirm-group {
	margin-top: 4rpx;
}

.confirm-label {
	font-size: 18rpx;
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	background: rgba(153, 153, 153, 0.1);
	color: #999999;
	transition: all 0.3s ease;

	&.confirmed {
		background: rgba(40, 167, 69, 0.1);
		color: #28a745;
	}
}

.work-hours {
	text-align: right;
}

.hours-label {
	display: block;
	font-size: 20rpx;
	color: #999999;
	margin-bottom: 4rpx;
}

.hours-value {
	display: block;
	font-size: 24rpx;
	font-weight: 600;
	color: #667eea;
}

.confirm-status {
	text-align: right;
}

.confirm-label {
	font-size: 20rpx;
	color: #999999;
}

/* 教师操作按钮 */
.teacher-actions {
	display: flex;
	gap: 12rpx;
	margin-top: 20rpx;
	padding-top: 20rpx;
	border-top: 1rpx solid rgba(240, 240, 240, 0.8);
}

.action-btn {
	flex: 1;
	height: 64rpx;
	border-radius: 16rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 6rpx;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.3), transparent);
		transition: left 0.6s ease;
	}

	&:active {
		transform: translateY(1rpx) scale(0.98);
		box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.12);
		
		&::before {
			left: 100%;
		}
	}
}

.btn-icon {
	width: 32rpx;
	height: 32rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
}

.sync-btn {
	background: linear-gradient(135deg, rgba(102, 126, 234, 0.08) 0%, rgba(102, 126, 234, 0.12) 100%);
	border: 1rpx solid rgba(102, 126, 234, 0.15);

	.btn-icon {
		background: rgba(102, 126, 234, 0.1);
	}

	&:active {
		background: linear-gradient(135deg, rgba(102, 126, 234, 0.12) 0%, rgba(102, 126, 234, 0.18) 100%);
		border-color: rgba(102, 126, 234, 0.25);
		
		.btn-icon {
			background: rgba(102, 126, 234, 0.15);
			transform: rotate(180deg);
		}
	}
}

.confirm-btn {
	background: linear-gradient(135deg, rgba(40, 167, 69, 0.08) 0%, rgba(40, 167, 69, 0.12) 100%);
	border: 1rpx solid rgba(40, 167, 69, 0.15);

	.btn-icon {
		background: rgba(40, 167, 69, 0.1);
	}

	&:active {
		background: linear-gradient(135deg, rgba(40, 167, 69, 0.12) 0%, rgba(40, 167, 69, 0.18) 100%);
		border-color: rgba(40, 167, 69, 0.25);
		
		.btn-icon {
			background: rgba(40, 167, 69, 0.15);
			transform: scale(1.1);
		}
	}
}

.action-text {
	font-size: 22rpx;
	font-weight: 500;
	letter-spacing: 0.5rpx;
	
	.sync-btn & {
		color: #667eea;
	}
	
	.confirm-btn & {
		color: #28a745;
	}
}

/* 浮动操作按钮样式更新 */
.fab-container {
	display: flex;
	gap: 16rpx;
}

.sync-dingtalk-btn {
	background: linear-gradient(135deg, #17a2b8 0%, #138496 100%);
	box-shadow: 0 16rpx 40rpx rgba(23, 162, 184, 0.4);

	&:active {
		box-shadow: 0 8rpx 20rpx rgba(23, 162, 184, 0.6);
	}
}
.loading-container {
	display: flex;
	justify-content: center;
	align-items: center;
	height: 400rpx;
}

.loading-text {
	color: #666666;
	font-size: 28rpx;
}

/* 浮动操作按钮 */
.floating-actions {
	position: fixed;
	bottom: 40rpx;
	left: 50%;
	transform: translateX(-50%);
	width: calc(100% - 60rpx);
	z-index: 100;
}

.fab-container {
	display: flex;
	gap: 20rpx;
}

.fab-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	box-shadow: 0 16rpx 40rpx rgba(102, 126, 234, 0.4);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	position: relative;
	overflow: hidden;

	&::before {
		content: '';
		position: absolute;
		top: 0;
		left: -100%;
		width: 100%;
		height: 100%;
		background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
		transition: left 0.5s;
	}

	&:active {
		transform: translateY(4rpx) scale(0.98);
		box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.6);

		&::before {
			left: 100%;
		}
	}

	&.active {
		background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
		box-shadow: 0 16rpx 40rpx rgba(40, 167, 69, 0.4);

		&:active {
			box-shadow: 0 8rpx 20rpx rgba(40, 167, 69, 0.6);
		}
	}
}

.fab-text {
	font-size: 28rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

/* 选择模式样式 */
.teacher-card {
	&.selection-mode {
		&.selectable {
			border: 2rpx solid rgba(102, 126, 234, 0.3);

			&:active {
				transform: scale(0.98);
			}

			&.selected {
				border-color: #667eea;
				background: linear-gradient(135deg, rgba(102, 126, 234, 0.05) 0%, rgba(118, 75, 162, 0.05) 100%);
			}
		}

		&:not(.selectable) {
			opacity: 0.6;
			pointer-events: none;
		}
	}
}

.selection-checkbox {
	position: absolute;
	top: -8rpx;
	right: -8rpx;
	width: 32rpx;
	height: 32rpx;
	border-radius: 50%;
	background: #ffffff;
	border: 3rpx solid #e0e0e0;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&.checked {
		background: #667eea;
		border-color: #667eea;
		transform: scale(1.1);
	}
}

.selection-tip {
	position: fixed;
	top: 200rpx;
	left: 50%;
	transform: translateX(-50%);
	z-index: 99;
}

.tip-content {
	background: rgba(0, 0, 0, 0.8);
	color: #ffffff;
	padding: 20rpx 30rpx;
	border-radius: 50rpx;
	display: flex;
	align-items: center;
	gap: 16rpx;
	backdrop-filter: blur(10rpx);
	animation: tipFadeIn 0.3s ease;
}

.tip-text {
	font-size: 26rpx;
	font-weight: 500;
}

.tip-actions {
	margin-left: 20rpx;
}

.cancel-selection {
	font-size: 24rpx;
	color: #ff6b6b;
	font-weight: 600;
	padding: 8rpx 16rpx;
	border-radius: 20rpx;
	background: rgba(255, 107, 107, 0.2);
}

@keyframes tipFadeIn {
	from {
		opacity: 0;
		transform: translateX(-50%) translateY(-20rpx);
	}
	to {
		opacity: 1;
		transform: translateX(-50%) translateY(0);
	}
}
</style>
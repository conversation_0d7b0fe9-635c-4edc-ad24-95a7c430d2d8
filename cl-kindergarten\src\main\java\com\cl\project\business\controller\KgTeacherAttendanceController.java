package com.cl.project.business.controller;

import java.util.List;
import java.util.Date;
import cn.dev33.satoken.annotation.SaCheckPermission;
import com.cl.project.business.domain.dto.BatchConfirmAttendanceDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.format.annotation.DateTimeFormat;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgTeacherAttendance;
import com.cl.project.business.domain.dto.TeacherAttendanceOverviewDto;
import com.cl.project.business.domain.dto.BatchTeacherCheckinDto;
import com.cl.project.business.service.IKgTeacherAttendanceService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 教师考勤记录Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/teacher-attendance")
public class KgTeacherAttendanceController extends BaseController
{
    @Autowired
    private IKgTeacherAttendanceService kgTeacherAttendanceService;

    /**
     * 查询教师考勤记录列表
     */
    @SaCheckPermission("kg:attendance:teacher:list")
    @GetMapping("/list")
    public TableDataInfo list(KgTeacherAttendance kgTeacherAttendance)
    {
        startPage();
        List<KgTeacherAttendance> list = kgTeacherAttendanceService.selectKgTeacherAttendanceList(kgTeacherAttendance);
        return getDataTable(list);
    }

    /**
     * 查询教师考勤概览列表
     * 展示所有教师及其当日考勤状态
     */
    @SaCheckPermission("kg:attendance:teacher:list")
    @GetMapping("/overview")
    public TableDataInfo overview(
            @RequestParam(value = "attendanceDate", required = false) 
            @DateTimeFormat(pattern = "yyyy-MM-dd") Date attendanceDate,
            @RequestParam(value = "teacherName", required = false) String teacherName,
            @RequestParam(value = "attendanceStatus", required = false) String attendanceStatus,
            @RequestParam(value = "dataSource", required = false) String dataSource)
    {
        startPage();
        List<TeacherAttendanceOverviewDto> list = kgTeacherAttendanceService.selectTeacherAttendanceOverview(attendanceDate, teacherName, attendanceStatus, dataSource);
        return getDataTable(list);
    }

    /**
     * 导出教师考勤记录列表
     */
    @SaCheckPermission("kg:attendance:teacher:query")
    @Log(title = "教师考勤记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgTeacherAttendance kgTeacherAttendance)
    {
        List<KgTeacherAttendance> list = kgTeacherAttendanceService.selectKgTeacherAttendanceList(kgTeacherAttendance);
        ExcelUtil<KgTeacherAttendance> util = new ExcelUtil<KgTeacherAttendance>(KgTeacherAttendance.class);
        return util.exportExcel(list, "attendance");
    }

    /**
     * 获取教师考勤记录详细信息
     */
    @SaCheckPermission("kg:attendance:teacher:query")
    @GetMapping(value = "/{attendanceId}")
    public AjaxResult getInfo(@PathVariable("attendanceId") Long attendanceId)
    {
        return AjaxResult.success(kgTeacherAttendanceService.selectKgTeacherAttendanceById(attendanceId));
    }

    /**
     * 新增教师考勤记录
     */
    @SaCheckPermission("kg:attendance:teacher:checkin")
    @Log(title = "教师考勤记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgTeacherAttendance kgTeacherAttendance)
    {
        return toAjax(kgTeacherAttendanceService.insertKgTeacherAttendance(kgTeacherAttendance));
    }

    /**
     * 修改教师考勤记录
     */
    @SaCheckPermission("kg:attendance:teacher:checkout")
    @Log(title = "教师考勤记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgTeacherAttendance kgTeacherAttendance)
    {
        return toAjax(kgTeacherAttendanceService.updateKgTeacherAttendance(kgTeacherAttendance));
    }

    /**
     * 删除教师考勤记录
     */
    @SaCheckPermission("kg:attendance:teacher:checkin")
    @Log(title = "教师考勤记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{attendanceIds}")
    public AjaxResult remove(@PathVariable Long[] attendanceIds)
    {
        return toAjax(kgTeacherAttendanceService.deleteKgTeacherAttendanceByIds(attendanceIds));
    }
    /**
     * 批量教师签到
     */
//    @SaCheckPermission("kg:attendance:teacher:checkin")
    @Log(title = "批量教师签到", businessType = BusinessType.INSERT)
    @PostMapping("/batchCheckin")
    public AjaxResult batchCheckin(@RequestBody BatchTeacherCheckinDto batchDto)
    {
        int successCount = kgTeacherAttendanceService.batchTeacherCheckin(batchDto);
        return AjaxResult.success("批量签到成功，共处理 " + successCount + " 条记录", successCount);

    }

    /** 批量确认教师考勤（自动获取当前用户ID） */
//    @SaCheckPermission("kg:teacher-attendance:confirm")
    @Log(title = "批量确认教师考勤", businessType = BusinessType.UPDATE)
    @PostMapping("/batchConfirm")
    public AjaxResult batchConfirm(@RequestBody BatchConfirmAttendanceDto dto) {
        dto.setConfirmedBy(cn.dev33.satoken.stp.StpUtil.getLoginIdAsLong());
        return toAjax(kgTeacherAttendanceService.batchConfirmAttendance(dto));
    }
}

<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="100px">
      <el-form-item label="计算类型" prop="calculationType">
        <el-select v-model="queryParams.calculationType" placeholder="请选择计算类型" clearable size="small">
          <el-option label="单个教师" value="single" />
          <el-option label="全体教师" value="all" />
        </el-select>
      </el-form-item>
      <el-form-item label="教师" prop="teacherId" v-if="queryParams.calculationType === 'single'">
        <el-select v-model="queryParams.teacherId" placeholder="请选择教师" clearable size="small" filterable>
          <el-option
            v-for="item in teacherList"
            :key="item.teacherId"
            :label="item.teacherName"
            :value="item.teacherId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="计算月份">
        <el-date-picker
          v-model="calculationMonth"
          type="month"
          placeholder="选择月份"
          size="small"
          @change="handleMonthChange"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleCalculate">开始计算</el-button>
        <el-button type="success" icon="el-icon-view" size="mini" @click="handlePreview">预览</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 工资统计卡片 -->
    <el-row :gutter="20" class="mb20" v-if="salaryStatistics">
      <el-col :span="6">
        <div class="statistics-card">
          <div class="card-title">教师总数</div>
          <div class="card-value">{{ salaryStatistics.totalTeachers || 0 }}</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="statistics-card">
          <div class="card-title">工资总额</div>
          <div class="card-value text-success">¥{{ salaryStatistics.totalSalary || 0 }}</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="statistics-card">
          <div class="card-title">平均工资</div>
          <div class="card-value text-primary">¥{{ salaryStatistics.averageSalary || 0 }}</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="statistics-card">
          <div class="card-title">已发放数</div>
          <div class="card-value text-warning">{{ salaryStatistics.paidCount || 0 }}</div>
        </div>
      </el-col>
    </el-row>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8" v-if="calculationResults.length > 0">
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-check"
          size="mini"
          :disabled="multiple"
          @click="handleGeneratePayslips"
          v-hasPermi="['kg:salary:generate']"
        >生成工资单</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-money"
          size="mini"
          :disabled="multiple"
          @click="handleConfirmPayment"
          v-hasPermi="['kg:salary:confirm']"
        >确认发放</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExportReport"
          v-hasPermi="['kg:salary:report']"
        >导出报表</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          icon="el-icon-refresh"
          size="mini"
          @click="handleRecalculateAll"
          v-hasPermi="['kg:salary:calculate']"
        >重新计算</el-button>
      </el-col>
    </el-row>

    <!-- 工资计算结果表格 -->
    <el-table 
      v-loading="loading" 
      :data="calculationResults" 
      @selection-change="handleSelectionChange"
      :summary-method="getSummaries"
      show-summary
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="教师姓名" align="center" prop="teacherInfo.teacherName" width="100" />
      <el-table-column label="职位" align="center" prop="teacherInfo.position" width="80" />
      <el-table-column label="出勤天数" align="center" width="80">
        <template slot-scope="scope">
          {{ scope.row.attendanceStats ? scope.row.attendanceStats.attendanceDays : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="出勤率" align="center" width="80">
        <template slot-scope="scope">
          <span :class="getAttendanceRateClass(scope.row.attendanceStats ? scope.row.attendanceStats.attendanceRate : 0)">
            {{ scope.row.attendanceStats ? scope.row.attendanceStats.attendanceRate : 0 }}%
          </span>
        </template>
      </el-table-column>
      <el-table-column label="基本工资" align="center" width="100">
        <template slot-scope="scope">
          <span class="text-primary">¥{{ scope.row.salaryBreakdown ? scope.row.salaryBreakdown.baseSalary : 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="满勤奖" align="center" width="80">
        <template slot-scope="scope">
          <span class="text-success">¥{{ scope.row.salaryBreakdown ? scope.row.salaryBreakdown.attendanceBonus : 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="课时费" align="center" width="80">
        <template slot-scope="scope">
          <span class="text-info">¥{{ scope.row.salaryBreakdown ? scope.row.salaryBreakdown.courseFee : 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="奖励金额" align="center" width="80">
        <template slot-scope="scope">
          <span class="text-warning">¥{{ scope.row.salaryBreakdown ? scope.row.salaryBreakdown.totalBonus : 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="扣款金额" align="center" width="80">
        <template slot-scope="scope">
          <span class="text-danger">¥{{ scope.row.salaryBreakdown ? scope.row.salaryBreakdown.totalDeduction : 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="应发工资" align="center" width="100">
        <template slot-scope="scope">
          <span class="text-success font-weight-bold">¥{{ scope.row.salaryBreakdown ? scope.row.salaryBreakdown.grossSalary : 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="社保代扣" align="center" width="80">
        <template slot-scope="scope">
          <span class="text-danger">¥{{ scope.row.salaryBreakdown ? scope.row.salaryBreakdown.socialInsurance : 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实发工资" align="center" width="100">
        <template slot-scope="scope">
          <span class="text-primary font-weight-bold">¥{{ scope.row.salaryBreakdown ? scope.row.salaryBreakdown.netSalary : 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="发放状态" align="center" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.error" type="danger">计算失败</el-tag>
          <el-tag v-else-if="scope.row.paymentStatus === 'paid'" type="success">已发放</el-tag>
          <el-tag v-else-if="scope.row.paymentStatus === 'confirmed'" type="warning">已确认</el-tag>
          <el-tag v-else type="info">待发放</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewDetail(scope.row)"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleAdjust(scope.row)"
            v-hasPermi="['kg:salary:adjust']"
          >调整</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-pie-chart"
            @click="handleViewCourseStats(scope.row)"
          >课时</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 工资详情对话框 -->
    <el-dialog title="工资计算详情" :visible.sync="detailDialogVisible" width="900px" append-to-body>
      <div v-if="selectedCalculation">
        <el-descriptions title="教师信息" :column="3" border>
          <el-descriptions-item label="教师姓名">{{ selectedCalculation.teacherInfo.teacherName }}</el-descriptions-item>
          <el-descriptions-item label="职位">{{ selectedCalculation.teacherInfo.position }}</el-descriptions-item>
          <el-descriptions-item label="入职日期">{{ selectedCalculation.teacherInfo.hireDate }}</el-descriptions-item>
        </el-descriptions>
        
        <el-descriptions title="考勤统计" :column="4" border class="mt20">
          <el-descriptions-item label="应出勤天数">{{ selectedCalculation.attendanceStats.workDays }}</el-descriptions-item>
          <el-descriptions-item label="实际出勤">{{ selectedCalculation.attendanceStats.attendanceDays }}</el-descriptions-item>
          <el-descriptions-item label="缺勤天数">{{ selectedCalculation.attendanceStats.absentDays }}</el-descriptions-item>
          <el-descriptions-item label="出勤率">{{ selectedCalculation.attendanceStats.attendanceRate }}%</el-descriptions-item>
        </el-descriptions>
        
        <el-descriptions title="工资明细" :column="2" border class="mt20">
          <el-descriptions-item label="基本工资">¥{{ selectedCalculation.salaryBreakdown.baseSalary }}</el-descriptions-item>
          <el-descriptions-item label="满勤奖">¥{{ selectedCalculation.salaryBreakdown.attendanceBonus }}</el-descriptions-item>
          <el-descriptions-item label="课时费">¥{{ selectedCalculation.salaryBreakdown.courseFee }}</el-descriptions-item>
          <el-descriptions-item label="报名奖励">¥{{ selectedCalculation.salaryBreakdown.enrollmentBonus }}</el-descriptions-item>
          <el-descriptions-item label="出勤率奖励">¥{{ selectedCalculation.salaryBreakdown.attendanceRateBonus }}</el-descriptions-item>
          <el-descriptions-item label="新生奖励">¥{{ selectedCalculation.salaryBreakdown.newStudentBonus }}</el-descriptions-item>
          <el-descriptions-item label="退园扣款">¥{{ selectedCalculation.salaryBreakdown.withdrawalDeduction }}</el-descriptions-item>
          <el-descriptions-item label="社保代扣">¥{{ selectedCalculation.salaryBreakdown.socialInsurance }}</el-descriptions-item>
          <el-descriptions-item label="应发工资">¥{{ selectedCalculation.salaryBreakdown.grossSalary }}</el-descriptions-item>
          <el-descriptions-item label="实发工资">¥{{ selectedCalculation.salaryBreakdown.netSalary }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 工资调整对话框 -->
    <el-dialog title="工资调整" :visible.sync="adjustDialogVisible" width="500px" append-to-body>
      <el-form ref="adjustForm" :model="adjustForm" :rules="adjustRules" label-width="100px">
        <el-form-item label="调整类型" prop="adjustType">
          <el-select v-model="adjustForm.adjustType" placeholder="请选择调整类型">
            <el-option label="基本工资" value="base" />
            <el-option label="奖励金额" value="bonus" />
            <el-option label="扣款金额" value="deduction" />
            <el-option label="课时费" value="course" />
          </el-select>
        </el-form-item>
        <el-form-item label="调整金额" prop="adjustAmount">
          <el-input-number 
            v-model="adjustForm.adjustAmount" 
            :precision="2" 
            :step="1" 
            placeholder="正数为增加，负数为减少"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="调整原因" prop="reason">
          <el-input v-model="adjustForm.reason" type="textarea" placeholder="请输入调整原因" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAdjust">确 定</el-button>
        <el-button @click="cancelAdjust">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 课时费统计对话框 -->
    <el-dialog title="课时费统计" :visible.sync="courseStatsDialogVisible" width="700px" append-to-body>
      <div v-if="courseStatistics">
        <el-table :data="courseStatistics.courseDetails" border>
          <el-table-column label="课程名称" align="center" prop="courseName" />
          <el-table-column label="授课次数" align="center" prop="teachingCount" />
          <el-table-column label="学生人数" align="center" prop="studentCount" />
          <el-table-column label="课时单价" align="center" prop="hourlyRate">
            <template slot-scope="scope">
              ¥{{ scope.row.hourlyRate }}
            </template>
          </el-table-column>
          <el-table-column label="课时费小计" align="center" prop="subtotal">
            <template slot-scope="scope">
              ¥{{ scope.row.subtotal }}
            </template>
          </el-table-column>
        </el-table>
        <div class="mt20 text-right">
          <span class="font-weight-bold">课时费总计：¥{{ courseStatistics.totalCourseFee }}</span>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  calculateMonthlySalary,
  calculateAllSalaryBatch,
  previewSalaryCalculation,
  getSalaryCalculationRules,
  generatePayslips,
  adjustSalary,
  getSalaryStatistics,
  confirmSalaryPayment,
  generateSalaryReport,
  getCourseFeeStatistics
} from "@/api/kg/salary/calculation";
import { listTeacher } from "@/api/kg/teacher/info";

export default {
  name: "SalaryCalculation",
  data() {
    return {
      // 查询参数
      queryParams: {
        calculationType: 'single',
        teacherId: null,
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1
      },
      // 计算月份
      calculationMonth: new Date(),
      // 加载状态
      loading: false,
      // 计算结果
      calculationResults: [],
      // 工资统计
      salaryStatistics: null,
      // 选中的记录
      ids: [],
      multiple: true,
      // 基础数据
      teacherList: [],
      // 详情对话框
      detailDialogVisible: false,
      selectedCalculation: null,
      // 调整对话框
      adjustDialogVisible: false,
      adjustForm: {
        salaryId: null,
        adjustType: '',
        adjustAmount: 0,
        reason: ''
      },
      adjustRules: {
        adjustType: [
          { required: true, message: "调整类型不能为空", trigger: "change" }
        ],
        adjustAmount: [
          { required: true, message: "调整金额不能为空", trigger: "blur" }
        ],
        reason: [
          { required: true, message: "调整原因不能为空", trigger: "blur" }
        ]
      },
      // 课时统计对话框
      courseStatsDialogVisible: false,
      courseStatistics: null
    };
  },
  created() {
    this.getTeacherList();
    this.getSalaryStats();
  },
  methods: {
    /** 获取教师列表 */
    getTeacherList() {
      listTeacher().then(response => {
        this.teacherList = response.rows;
      });
    },
    
    /** 获取工资统计 */
    getSalaryStats() {
      getSalaryStatistics(this.queryParams.year, this.queryParams.month).then(response => {
        this.salaryStatistics = response.data;
      });
    },
    
    /** 月份改变 */
    handleMonthChange(date) {
      if (date) {
        this.queryParams.year = date.getFullYear();
        this.queryParams.month = date.getMonth() + 1;
        this.getSalaryStats();
      }
    },
    
    /** 开始计算 */
    handleCalculate() {
      if (!this.validateParams()) {
        return;
      }
      
      this.loading = true;
      
      let apiCall;
      if (this.queryParams.calculationType === 'single') {
        apiCall = calculateMonthlySalary(
          this.queryParams.teacherId,
          this.queryParams.year,
          this.queryParams.month
        );
      } else {
        apiCall = calculateAllSalaryBatch(
          this.queryParams.year,
          this.queryParams.month
        );
      }
      
      apiCall.then(response => {
        if (this.queryParams.calculationType === 'single') {
          this.calculationResults = [response.data];
        } else {
          this.calculationResults = response.data.teacherResults || [];
        }
        
        this.loading = false;
        this.$message.success('计算完成');
        this.getSalaryStats();
      }).catch(() => {
        this.loading = false;
      });
    },
    
    /** 预览计算 */
    handlePreview() {
      if (this.queryParams.calculationType !== 'single' || !this.queryParams.teacherId) {
        this.$message.warning('预览功能仅支持单个教师计算');
        return;
      }
      
      previewSalaryCalculation(
        this.queryParams.teacherId,
        this.queryParams.year,
        this.queryParams.month
      ).then(response => {
        this.selectedCalculation = response.data;
        this.detailDialogVisible = true;
      });
    },
    
    /** 参数验证 */
    validateParams() {
      if (this.queryParams.calculationType === 'single' && !this.queryParams.teacherId) {
        this.$message.warning('请选择教师');
        return false;
      }
      
      if (!this.queryParams.year || !this.queryParams.month) {
        this.$message.warning('请选择计算月份');
        return false;
      }
      
      return true;
    },
    
    /** 重置查询 */
    resetQuery() {
      this.resetForm("queryForm");
      this.calculationResults = [];
      this.salaryStatistics = null;
    },
    
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.teacherInfo.teacherId);
      this.multiple = !selection.length;
    },
    
    /** 生成工资单 */
    handleGeneratePayslips() {
      if (this.calculationResults.length === 0) {
        this.$message.warning('没有可生成的工资单');
        return;
      }
      
      const validResults = this.calculationResults.filter(result => !result.error);
      
      if (validResults.length === 0) {
        this.$message.warning('没有计算成功的记录');
        return;
      }
      
      this.$confirm(`确认生成 ${validResults.length} 条工资单?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        generatePayslips(validResults).then(response => {
          this.$message.success(response.msg);
          this.calculationResults = [];
          this.getSalaryStats();
        });
      });
    },
    
    /** 确认发放 */
    handleConfirmPayment() {
      if (this.ids.length === 0) {
        this.$message.warning('请选择要确认发放的工资记录');
        return;
      }
      
      this.$confirm('确认发放选中的工资?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        confirmSalaryPayment(this.ids).then(response => {
          this.$message.success("确认成功");
          this.handleCalculate();
        });
      });
    },
    
    /** 导出报表 */
    handleExportReport() {
      generateSalaryReport(this.queryParams.year, this.queryParams.month).then(response => {
        this.$message.success("报表生成成功");
        // TODO: 下载报表文件
      });
    },
    
    /** 重新计算全部 */
    handleRecalculateAll() {
      this.handleCalculate();
    },
    
    /** 查看详情 */
    handleViewDetail(row) {
      this.selectedCalculation = row;
      this.detailDialogVisible = true;
    },
    
    /** 工资调整 */
    handleAdjust(row) {
      this.adjustForm.salaryId = row.salaryId;
      this.adjustForm.adjustType = '';
      this.adjustForm.adjustAmount = 0;
      this.adjustForm.reason = '';
      this.adjustDialogVisible = true;
    },
    
    /** 查看课时统计 */
    handleViewCourseStats(row) {
      getCourseFeeStatistics(
        row.teacherInfo.teacherId,
        this.queryParams.year,
        this.queryParams.month
      ).then(response => {
        this.courseStatistics = response.data;
        this.courseStatsDialogVisible = true;
      });
    },
    
    /** 提交调整 */
    submitAdjust() {
      this.$refs["adjustForm"].validate(valid => {
        if (valid) {
          adjustSalary(
            this.adjustForm.salaryId,
            this.adjustForm.adjustType,
            this.adjustForm.adjustAmount,
            this.adjustForm.reason
          ).then(response => {
            this.$message.success("调整成功");
            this.adjustDialogVisible = false;
            this.handleCalculate();
          });
        }
      });
    },
    
    /** 取消调整 */
    cancelAdjust() {
      this.adjustDialogVisible = false;
    },
    
    /** 获取出勤率样式类 */
    getAttendanceRateClass(rate) {
      if (rate >= 95) return 'text-success';
      if (rate >= 85) return 'text-warning';
      return 'text-danger';
    },
    
    /** 表格合计行 */
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        
        const values = data.map(item => {
          if (!item.salaryBreakdown) return 0;
          
          switch (column.property) {
            case 'salaryBreakdown.baseSalary':
              return Number(item.salaryBreakdown.baseSalary);
            case 'salaryBreakdown.attendanceBonus':
              return Number(item.salaryBreakdown.attendanceBonus);
            case 'salaryBreakdown.courseFee':
              return Number(item.salaryBreakdown.courseFee);
            case 'salaryBreakdown.grossSalary':
              return Number(item.salaryBreakdown.grossSalary);
            case 'salaryBreakdown.netSalary':
              return Number(item.salaryBreakdown.netSalary);
            default:
              return 0;
          }
        });
        
        if (values.every(value => !isNaN(value))) {
          const sum = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index] = '¥' + sum.toFixed(2);
        } else {
          sums[index] = '';
        }
      });
      
      return sums;
    }
  }
};
</script>

<style scoped>
.statistics-card {
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.text-primary {
  color: #409eff !important;
}

.text-success {
  color: #67c23a !important;
}

.text-info {
  color: #909399 !important;
}

.text-warning {
  color: #e6a23c !important;
}

.text-danger {
  color: #f56c6c !important;
}

.font-weight-bold {
  font-weight: bold;
}

.mb20 {
  margin-bottom: 20px;
}

.mt20 {
  margin-top: 20px;
}

.text-right {
  text-align: right;
}
</style>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgCourseBillDetailMapper">
    
    <resultMap type="KgCourseBillDetail" id="KgCourseBillDetailResult">
        <result property="detailId"    column="detail_id"    />
        <result property="billId"    column="bill_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="courseName"    column="course_name"    />
        <result property="sessionsCount"    column="sessions_count"    />
        <result property="pricePerSession"    column="price_per_session"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="comId"    column="com_id"    />
    </resultMap>

    <sql id="selectKgCourseBillDetailVo">
        select detail_id, bill_id, course_id, course_name, sessions_count, price_per_session, total_amount, com_id from kg_course_bill_detail
    </sql>

    <select id="selectKgCourseBillDetailList" parameterType="KgCourseBillDetail" resultMap="KgCourseBillDetailResult">
        <include refid="selectKgCourseBillDetailVo"/>
        <where>  
            <if test="billId != null "> and bill_id = #{billId}</if>
            <if test="courseId != null "> and course_id = #{courseId}</if>
            <if test="courseName != null  and courseName != ''"> and course_name like concat('%', #{courseName}, '%')</if>
            <if test="sessionsCount != null "> and sessions_count = #{sessionsCount}</if>
            <if test="pricePerSession != null "> and price_per_session = #{pricePerSession}</if>
            <if test="totalAmount != null "> and total_amount = #{totalAmount}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgCourseBillDetailById" parameterType="Long" resultMap="KgCourseBillDetailResult">
        <include refid="selectKgCourseBillDetailVo"/>
        where detail_id = #{detailId}
    </select>
        
    <insert id="insertKgCourseBillDetail" parameterType="KgCourseBillDetail" useGeneratedKeys="true" keyProperty="detailId">
        insert into kg_course_bill_detail
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="billId != null">bill_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="courseName != null">course_name,</if>
            <if test="sessionsCount != null">sessions_count,</if>
            <if test="pricePerSession != null">price_per_session,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="comId != null">com_id,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="billId != null">#{billId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="courseName != null">#{courseName},</if>
            <if test="sessionsCount != null">#{sessionsCount},</if>
            <if test="pricePerSession != null">#{pricePerSession},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="comId != null">#{comId},</if>
         </trim>
    </insert>

    <update id="updateKgCourseBillDetail" parameterType="KgCourseBillDetail">
        update kg_course_bill_detail
        <trim prefix="SET" suffixOverrides=",">
            <if test="billId != null">bill_id = #{billId},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="courseName != null">course_name = #{courseName},</if>
            <if test="sessionsCount != null">sessions_count = #{sessionsCount},</if>
            <if test="pricePerSession != null">price_per_session = #{pricePerSession},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="comId != null">com_id = #{comId},</if>
        </trim>
        where detail_id = #{detailId}
    </update>

    <delete id="deleteKgCourseBillDetailById" parameterType="Long">
        delete from kg_course_bill_detail where detail_id = #{detailId}
    </delete>

    <delete id="deleteKgCourseBillDetailByIds" parameterType="String">
        delete from kg_course_bill_detail where detail_id in 
        <foreach item="detailId" collection="array" open="(" separator="," close=")">
            #{detailId}
        </foreach>
    </delete>
    
</mapper>
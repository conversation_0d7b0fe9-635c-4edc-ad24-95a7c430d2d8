package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgIncome;
import com.cl.project.business.service.IKgIncomeService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 收入记录Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/income")
public class KgIncomeController extends BaseController
{
    @Autowired
    private IKgIncomeService kgIncomeService;

    /**
     * 查询收入记录列表
     */
    @SaCheckPermission("business:income:list")
    @GetMapping("/list")
    public TableDataInfo list(KgIncome kgIncome)
    {
        startPage();
        List<KgIncome> list = kgIncomeService.selectKgIncomeList(kgIncome);
        return getDataTable(list);
    }

    /**
     * 导出收入记录列表
     */
    @SaCheckPermission("business:income:export")
    @Log(title = "收入记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgIncome kgIncome)
    {
        List<KgIncome> list = kgIncomeService.selectKgIncomeList(kgIncome);
        ExcelUtil<KgIncome> util = new ExcelUtil<KgIncome>(KgIncome.class);
        return util.exportExcel(list, "income");
    }

    /**
     * 获取收入记录详细信息
     */
    @SaCheckPermission("business:income:query")
    @GetMapping(value = "/{incomeId}")
    public AjaxResult getInfo(@PathVariable("incomeId") Long incomeId)
    {
        return AjaxResult.success(kgIncomeService.selectKgIncomeById(incomeId));
    }

    /**
     * 新增收入记录
     */
    @SaCheckPermission("business:income:add")
    @Log(title = "收入记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgIncome kgIncome)
    {
        return toAjax(kgIncomeService.insertKgIncome(kgIncome));
    }

    /**
     * 修改收入记录
     */
    @SaCheckPermission("business:income:edit")
    @Log(title = "收入记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgIncome kgIncome)
    {
        return toAjax(kgIncomeService.updateKgIncome(kgIncome));
    }

    /**
     * 删除收入记录
     */
    @SaCheckPermission("business:income:remove")
    @Log(title = "收入记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{incomeIds}")
    public AjaxResult remove(@PathVariable Long[] incomeIds)
    {
        return toAjax(kgIncomeService.deleteKgIncomeByIds(incomeIds));
    }
}

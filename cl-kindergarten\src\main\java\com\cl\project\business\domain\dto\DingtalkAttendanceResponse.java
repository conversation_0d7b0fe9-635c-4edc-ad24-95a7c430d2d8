package com.cl.project.business.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;
import java.util.List;

/**
 * 钉钉打卡记录响应实体
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public class DingtalkAttendanceResponse 
{
    @JSONField(name = "errcode")
    private Integer errcode;
    
    @JSONField(name = "errmsg")
    private String errmsg;
    
    // 直接的recordresult字段，没有result封装
    @JSONField(name = "recordresult")
    private List<AttendanceRecord> recordresult;
    
    @JSONField(name = "hasMore")
    private Boolean hasMore;
    
    // 保留原有的result字段以兼容可能的旧格式
    @JSONField(name = "result")
    private Result result;
    
    public static class Result 
    {
        @JSONField(name = "recordresult")
        private List<AttendanceRecord> recordresult;
        
        @JSONField(name = "has_more")
        private Boolean hasMore;
        
        public List<AttendanceRecord> getRecordresult() {
            return recordresult;
        }
        
        public void setRecordresult(List<AttendanceRecord> recordresult) {
            this.recordresult = recordresult;
        }
        
        public Boolean getHasMore() {
            return hasMore;
        }
        
        public void setHasMore(Boolean hasMore) {
            this.hasMore = hasMore;
        }
    }
    
    public static class AttendanceRecord 
    {
        @JSONField(name = "userId")
        private String userId;
        
        @JSONField(name = "groupId")
        private Long groupId;
        
        @JSONField(name = "workDate")
        private Long workDate;
        
        @JSONField(name = "timeResult")
        private String timeResult;
        
        @JSONField(name = "locationResult")
        private String locationResult;
        
        @JSONField(name = "baseCheckTime")
        private Long baseCheckTime;
        
        @JSONField(name = "userCheckTime")
        private Long userCheckTime;
        
        @JSONField(name = "checkType")
        private String checkType;
        
        @JSONField(name = "corpId")
        private String corpId;
        
        @JSONField(name = "id")
        private Long id;
        
        @JSONField(name = "recordId")
        private Long recordId;
        
        @JSONField(name = "planId")
        private Long planId;
        
        @JSONField(name = "isLegal")
        private String isLegal;
        
        @JSONField(name = "locationMethod")
        private String locationMethod;
        
        @JSONField(name = "deviceId")
        private String deviceId;
        
        @JSONField(name = "userAddress")
        private String userAddress;
        
        @JSONField(name = "userLongitude")
        private Double userLongitude;
        
        @JSONField(name = "userLatitude")
        private Double userLatitude;
        
        @JSONField(name = "planCheckTime")
        private Long planCheckTime;
        
        @JSONField(name = "sourceType")
        private String sourceType;
        
        @JSONField(name = "procInstId")
        private String procInstId;
        
        @JSONField(name = "approveId")
        private Long approveId;
        
        @JSONField(name = "classId")
        private Long classId;
        
        // Getters and Setters
        public String getUserId() {
            return userId;
        }
        
        public void setUserId(String userId) {
            this.userId = userId;
        }
        
        public Long getGroupId() {
            return groupId;
        }
        
        public void setGroupId(Long groupId) {
            this.groupId = groupId;
        }
        
        public Long getWorkDate() {
            return workDate;
        }
        
        public void setWorkDate(Long workDate) {
            this.workDate = workDate;
        }
        
        public String getTimeResult() {
            return timeResult;
        }
        
        public void setTimeResult(String timeResult) {
            this.timeResult = timeResult;
        }
        
        public String getLocationResult() {
            return locationResult;
        }
        
        public void setLocationResult(String locationResult) {
            this.locationResult = locationResult;
        }
        
        public Long getBaseCheckTime() {
            return baseCheckTime;
        }
        
        public void setBaseCheckTime(Long baseCheckTime) {
            this.baseCheckTime = baseCheckTime;
        }
        
        public Long getUserCheckTime() {
            return userCheckTime;
        }
        
        public void setUserCheckTime(Long userCheckTime) {
            this.userCheckTime = userCheckTime;
        }
        
        public String getCheckType() {
            return checkType;
        }
        
        public void setCheckType(String checkType) {
            this.checkType = checkType;
        }
        
        public String getCorpId() {
            return corpId;
        }
        
        public void setCorpId(String corpId) {
            this.corpId = corpId;
        }
        
        public Long getId() {
            return id;
        }
        
        public void setId(Long id) {
            this.id = id;
        }
        
        public Long getRecordId() {
            return recordId;
        }
        
        public void setRecordId(Long recordId) {
            this.recordId = recordId;
        }
        
        public Long getPlanId() {
            return planId;
        }
        
        public void setPlanId(Long planId) {
            this.planId = planId;
        }
        
        public String getIsLegal() {
            return isLegal;
        }
        
        public void setIsLegal(String isLegal) {
            this.isLegal = isLegal;
        }
        
        public String getLocationMethod() {
            return locationMethod;
        }
        
        public void setLocationMethod(String locationMethod) {
            this.locationMethod = locationMethod;
        }
        
        public String getDeviceId() {
            return deviceId;
        }
        
        public void setDeviceId(String deviceId) {
            this.deviceId = deviceId;
        }
        
        public String getUserAddress() {
            return userAddress;
        }
        
        public void setUserAddress(String userAddress) {
            this.userAddress = userAddress;
        }
        
        public Double getUserLongitude() {
            return userLongitude;
        }
        
        public void setUserLongitude(Double userLongitude) {
            this.userLongitude = userLongitude;
        }
        
        public Double getUserLatitude() {
            return userLatitude;
        }
        
        public void setUserLatitude(Double userLatitude) {
            this.userLatitude = userLatitude;
        }
        
        public Long getPlanCheckTime() {
            return planCheckTime;
        }
        
        public void setPlanCheckTime(Long planCheckTime) {
            this.planCheckTime = planCheckTime;
        }
        
        public String getSourceType() {
            return sourceType;
        }
        
        public void setSourceType(String sourceType) {
            this.sourceType = sourceType;
        }
        
        public String getProcInstId() {
            return procInstId;
        }
        
        public void setProcInstId(String procInstId) {
            this.procInstId = procInstId;
        }
        
        public Long getApproveId() {
            return approveId;
        }
        
        public void setApproveId(Long approveId) {
            this.approveId = approveId;
        }
        
        public Long getClassId() {
            return classId;
        }
        
        public void setClassId(Long classId) {
            this.classId = classId;
        }
    }
    
    public Integer getErrcode() {
        return errcode;
    }
    
    public void setErrcode(Integer errcode) {
        this.errcode = errcode;
    }
    
    public String getErrmsg() {
        return errmsg;
    }
    
    public void setErrmsg(String errmsg) {
        this.errmsg = errmsg;
    }
    
    public List<AttendanceRecord> getRecordresult() {
        return recordresult;
    }
    
    public void setRecordresult(List<AttendanceRecord> recordresult) {
        this.recordresult = recordresult;
    }
    
    public Boolean getHasMore() {
        return hasMore;
    }
    
    public void setHasMore(Boolean hasMore) {
        this.hasMore = hasMore;
    }
    
    public Result getResult() {
        return result;
    }
    
    public void setResult(Result result) {
        this.result = result;
    }
}

<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
      <el-form-item label="教师姓名" prop="teacherName">
        <el-input
          v-model="queryParams.teacherName"
          placeholder="请输入教师姓名"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工资月份" prop="salaryMonth">
        <el-date-picker clearable size="small" style="width: 200px"
          v-model="queryParams.salaryMonth"
          type="month"
          value-format="yyyy-MM"
          placeholder="选择工资月份">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="确认状态" prop="confirmStatus">
        <el-select v-model="queryParams.confirmStatus" placeholder="请选择确认状态" clearable size="small">
          <el-option label="未确认" value="0" />
          <el-option label="已确认" value="1" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleCalculate"
        >工资计算</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-check"
          size="mini"
          @click="handleConfirm"
          v-hasPermi="['kg:salary:calculate:confirm']"
        >工资确认</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-money"
          size="mini"
          @click="handlePay"
          v-hasPermi="['kg:salary:calculate:pay']"
        >工资发放</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="salaryList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="教师姓名" align="center" prop="teacherName" />
      <el-table-column label="工资月份" align="center" prop="salaryMonth" />
      <el-table-column label="基本工资" align="center" prop="baseSalary">
        <template slot-scope="scope">
          <span>¥{{ scope.row.baseSalary }}</span>
        </template>
      </el-table-column>
      <el-table-column label="课时费" align="center" prop="classFee">
        <template slot-scope="scope">
          <span>¥{{ scope.row.classFee }}</span>
        </template>
      </el-table-column>
      <el-table-column label="奖金" align="center" prop="bonus">
        <template slot-scope="scope">
          <span>¥{{ scope.row.bonus }}</span>
        </template>
      </el-table-column>
      <el-table-column label="扣款" align="center" prop="deduction">
        <template slot-scope="scope">
          <span style="color: #F56C6C;">¥{{ scope.row.deduction }}</span>
        </template>
      </el-table-column>
      <el-table-column label="应发工资" align="center" prop="totalSalary">
        <template slot-scope="scope">
          <span style="color: #E6A23C; font-weight: bold;">¥{{ scope.row.totalSalary }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工作天数" align="center" prop="workDays" />
      <el-table-column label="确认状态" align="center" prop="confirmStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.confirmStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="发放状态" align="center" prop="paymentStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.kg_payment_status" :value="scope.row.paymentStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 工资计算对话框 -->
    <el-dialog title="工资计算" :visible.sync="calculateOpen" width="500px" append-to-body>
      <el-form ref="calculateForm" :model="calculateForm" :rules="calculateRules" label-width="80px">
        <el-form-item label="工资月份" prop="salaryMonth">
          <el-date-picker clearable size="small" style="width: 200px"
            v-model="calculateForm.salaryMonth"
            type="month"
            value-format="yyyy-MM"
            placeholder="选择工资月份">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="教师" prop="teacherId">
          <el-select v-model="calculateForm.teacherId" placeholder="请选择教师（可选）" clearable>
            <el-option
              v-for="item in teacherList"
              :key="item.teacherId"
              :label="item.teacherName"
              :value="item.teacherId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="课时费单价" prop="classFeePerHour">
          <el-input v-model="calculateForm.classFeePerHour" placeholder="请输入课时费单价" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitCalculate">开始计算</el-button>
        <el-button @click="calculateOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 工资修改对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-row>
          <el-col :span="12">
            <el-form-item label="教师姓名" prop="teacherName">
              <el-input v-model="form.teacherName" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="工资月份" prop="salaryMonth">
              <el-input v-model="form.salaryMonth" disabled />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="基本工资" prop="baseSalary">
              <el-input v-model="form.baseSalary" placeholder="请输入基本工资" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="课时费" prop="classFee">
              <el-input v-model="form.classFee" placeholder="请输入课时费" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="奖金" prop="bonus">
              <el-input v-model="form.bonus" placeholder="请输入奖金" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="扣款" prop="deduction">
              <el-input v-model="form.deduction" placeholder="请输入扣款" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSalaryCalculate, getSalaryCalculate, updateSalaryCalculate, calculateSalary, confirmSalary, paySalary, exportSalaryCalculate } from "@/api/kg/salary/calculate";
import { listTeacher } from "@/api/kg/teacher/info";

export default {
  name: "SalaryCalculate",
  dicts: ['sys_yes_no', 'kg_payment_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 工资表格数据
      salaryList: [],
      // 教师列表
      teacherList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示计算对话框
      calculateOpen: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        teacherName: undefined,
        salaryMonth: undefined,
        confirmStatus: undefined,
      },
      // 表单参数
      form: {},
      // 计算表单参数
      calculateForm: {},
      // 表单校验
      rules: {
        baseSalary: [
          { required: true, message: "基本工资不能为空", trigger: "blur" }
        ]
      },
      // 计算表单校验
      calculateRules: {
        salaryMonth: [
          { required: true, message: "工资月份不能为空", trigger: "blur" }
        ],
        classFeePerHour: [
          { required: true, message: "课时费单价不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getTeacherList();
  },
  methods: {
    /** 查询工资列表 */
    getList() {
      this.loading = true;
      listSalaryCalculate(this.queryParams).then(response => {
        this.salaryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询教师列表 */
    getTeacherList() {
      listTeacher().then(response => {
        this.teacherList = response.rows;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        salaryId: undefined,
        teacherName: undefined,
        salaryMonth: undefined,
        baseSalary: undefined,
        classFee: undefined,
        bonus: undefined,
        deduction: undefined,
        remark: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.salaryId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    /** 工资计算按钮操作 */
    handleCalculate() {
      this.calculateForm = {
        salaryMonth: undefined,
        teacherId: undefined,
        classFeePerHour: undefined
      };
      this.calculateOpen = true;
    },
    /** 提交计算 */
    submitCalculate() {
      this.$refs["calculateForm"].validate(valid => {
        if (valid) {
          calculateSalary(this.calculateForm).then(response => {
            if (response.code === 200) {
              this.msgSuccess("工资计算成功");
              this.calculateOpen = false;
              this.getList();
            }
          });
        }
      });
    },
    /** 工资确认按钮操作 */
    handleConfirm() {
      const salaryIds = this.ids;
      if (salaryIds.length === 0) {
        this.msgError("请选择要确认的工资记录");
        return;
      }
      this.$confirm('是否确认选中的工资记录?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        confirmSalary(salaryIds).then(response => {
          if (response.code === 200) {
            this.msgSuccess("工资确认成功");
            this.getList();
          }
        });
      }).catch(() => {});
    },
    /** 工资发放按钮操作 */
    handlePay() {
      const salaryIds = this.ids;
      if (salaryIds.length === 0) {
        this.msgError("请选择要发放的工资记录");
        return;
      }
      this.$confirm('是否确认发放选中的工资?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        paySalary(salaryIds).then(response => {
          if (response.code === 200) {
            this.msgSuccess("工资发放成功");
            this.getList();
          }
        });
      }).catch(() => {});
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const salaryId = row.salaryId || this.ids
      getSalaryCalculate(salaryId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改工资";
      });
    },
    /** 查看按钮操作 */
    handleView(row) {
      // 可以跳转到工资查询页面或显示详情
      this.$router.push('/kg/salary/query');
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          updateSalaryCalculate(this.form).then(response => {
            if (response.code === 200) {
              this.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            }
          });
        }
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有工资数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportSalaryCalculate(queryParams);
        }).then(response => {
          this.download(response.msg);
        }).catch(function() {});
    }
  }
};
</script>

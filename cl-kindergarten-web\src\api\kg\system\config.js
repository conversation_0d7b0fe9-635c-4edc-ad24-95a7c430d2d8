import request from '@/utils/request'

// 查询移动端权限配置列表
export function listConfig(query) {
  return request({
    url: '/business/config/list',
    method: 'get',
    params: query
  })
}

// 查询移动端权限配置详细
export function getConfig(configId) {
  return request({
    url: '/business/config/' + configId,
    method: 'get'
  })
}

// 新增移动端权限配置
export function addConfig(data) {
  return request({
    url: '/business/config',
    method: 'post',
    data: data
  })
}

// 修改移动端权限配置
export function updateConfig(data) {
  return request({
    url: '/business/config',
    method: 'put',
    data: data
  })
}

// 删除移动端权限配置
export function delConfig(configId) {
  return request({
    url: '/business/config/' + configId,
    method: 'delete'
  })
}

// 导出移动端权限配置
export function exportConfig(query) {
  return request({
    url: '/business/config/export',
    method: 'get',
    params: query
  })
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgMessagePushMapper">
    
    <resultMap type="KgMessagePush" id="KgMessagePushResult">
        <result property="pushId"    column="push_id"    />
        <result property="messageType"    column="message_type"    />
        <result property="recipientType"    column="recipient_type"    />
        <result property="recipientId"    column="recipient_id"    />
        <result property="openid"    column="openid"    />
        <result property="title"    column="title"    />
        <result property="content"    column="content"    />
        <result property="templateId"    column="template_id"    />
        <result property="pushTime"    column="push_time"    />
        <result property="pushStatus"    column="push_status"    />
        <result property="errorMessage"    column="error_message"    />
        <result property="readStatus"    column="read_status"    />
        <result property="readTime"    column="read_time"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgMessagePushVo">
        select push_id, message_type, recipient_type, recipient_id, openid, title, content, template_id, push_time, push_status, error_message, read_status, read_time, com_id, create_by, create_time, update_by, update_time, remark from kg_message_push
    </sql>

    <select id="selectKgMessagePushList" parameterType="KgMessagePush" resultMap="KgMessagePushResult">
        <include refid="selectKgMessagePushVo"/>
        <where>  
            <if test="messageType != null  and messageType != ''"> and message_type = #{messageType}</if>
            <if test="recipientType != null  and recipientType != ''"> and recipient_type = #{recipientType}</if>
            <if test="recipientId != null "> and recipient_id = #{recipientId}</if>
            <if test="openid != null  and openid != ''"> and openid = #{openid}</if>
            <if test="title != null  and title != ''"> and title = #{title}</if>
            <if test="content != null  and content != ''"> and content = #{content}</if>
            <if test="templateId != null  and templateId != ''"> and template_id = #{templateId}</if>
            <if test="pushTime != null "> and push_time = #{pushTime}</if>
            <if test="pushStatus != null  and pushStatus != ''"> and push_status = #{pushStatus}</if>
            <if test="errorMessage != null  and errorMessage != ''"> and error_message = #{errorMessage}</if>
            <if test="readStatus != null "> and read_status = #{readStatus}</if>
            <if test="readTime != null "> and read_time = #{readTime}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgMessagePushById" parameterType="Long" resultMap="KgMessagePushResult">
        <include refid="selectKgMessagePushVo"/>
        where push_id = #{pushId}
    </select>
        
    <insert id="insertKgMessagePush" parameterType="KgMessagePush" useGeneratedKeys="true" keyProperty="pushId">
        insert into kg_message_push
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="messageType != null and messageType != ''">message_type,</if>
            <if test="recipientType != null and recipientType != ''">recipient_type,</if>
            <if test="recipientId != null">recipient_id,</if>
            <if test="openid != null">openid,</if>
            <if test="title != null and title != ''">title,</if>
            <if test="content != null and content != ''">content,</if>
            <if test="templateId != null">template_id,</if>
            <if test="pushTime != null">push_time,</if>
            <if test="pushStatus != null">push_status,</if>
            <if test="errorMessage != null">error_message,</if>
            <if test="readStatus != null">read_status,</if>
            <if test="readTime != null">read_time,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="messageType != null and messageType != ''">#{messageType},</if>
            <if test="recipientType != null and recipientType != ''">#{recipientType},</if>
            <if test="recipientId != null">#{recipientId},</if>
            <if test="openid != null">#{openid},</if>
            <if test="title != null and title != ''">#{title},</if>
            <if test="content != null and content != ''">#{content},</if>
            <if test="templateId != null">#{templateId},</if>
            <if test="pushTime != null">#{pushTime},</if>
            <if test="pushStatus != null">#{pushStatus},</if>
            <if test="errorMessage != null">#{errorMessage},</if>
            <if test="readStatus != null">#{readStatus},</if>
            <if test="readTime != null">#{readTime},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgMessagePush" parameterType="KgMessagePush">
        update kg_message_push
        <trim prefix="SET" suffixOverrides=",">
            <if test="messageType != null and messageType != ''">message_type = #{messageType},</if>
            <if test="recipientType != null and recipientType != ''">recipient_type = #{recipientType},</if>
            <if test="recipientId != null">recipient_id = #{recipientId},</if>
            <if test="openid != null">openid = #{openid},</if>
            <if test="title != null and title != ''">title = #{title},</if>
            <if test="content != null and content != ''">content = #{content},</if>
            <if test="templateId != null">template_id = #{templateId},</if>
            <if test="pushTime != null">push_time = #{pushTime},</if>
            <if test="pushStatus != null">push_status = #{pushStatus},</if>
            <if test="errorMessage != null">error_message = #{errorMessage},</if>
            <if test="readStatus != null">read_status = #{readStatus},</if>
            <if test="readTime != null">read_time = #{readTime},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where push_id = #{pushId}
    </update>

    <delete id="deleteKgMessagePushById" parameterType="Long">
        delete from kg_message_push where push_id = #{pushId}
    </delete>

    <delete id="deleteKgMessagePushByIds" parameterType="String">
        delete from kg_message_push where push_id in 
        <foreach item="pushId" collection="array" open="(" separator="," close=")">
            #{pushId}
        </foreach>
    </delete>
    
</mapper>
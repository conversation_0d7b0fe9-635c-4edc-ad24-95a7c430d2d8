package com.cl.project.business.mapper;

import java.util.List;
import com.cl.project.business.domain.KgParent;

/**
 * 家长信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
public interface KgParentMapper 
{
    /**
     * 查询家长信息
     * 
     * @param parentId 家长信息主键
     * @return 家长信息
     */
    public KgParent selectKgParentByParentId(Long parentId);

    /**
     * 查询家长信息列表
     * 
     * @param kgParent 家长信息
     * @return 家长信息集合
     */
    public List<KgParent> selectKgParentList(KgParent kgParent);

    /**
     * 新增家长信息
     * 
     * @param kgParent 家长信息
     * @return 结果
     */
    public int insertKgParent(KgParent kgParent);

    /**
     * 修改家长信息
     * 
     * @param kgParent 家长信息
     * @return 结果
     */
    public int updateKgParent(KgParent kgParent);

    /**
     * 删除家长信息
     * 
     * @param parentId 家长信息主键
     * @return 结果
     */
    public int deleteKgParentByParentId(Long parentId);

    /**
     * 批量删除家长信息
     * 
     * @param parentIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteKgParentByParentIds(Long[] parentIds);

    /**
     * 根据手机号查询家长信息
     * 
     * @param parentPhone 家长手机号
     * @return 家长信息
     */
    public KgParent selectKgParentByPhone(String parentPhone);

    /**
     * 根据微信openid查询家长信息
     * 
     * @param wechatOpenid 微信openid
     * @return 家长信息
     */
    public KgParent selectKgParentByWechatOpenid(String wechatOpenid);

    /**
     * 查询家长关联的学生数量
     * 
     * @param parentId 家长ID
     * @return 学生数量
     */
    public int countStudentsByParentId(Long parentId);

    /**
     * 批量更新家长绑定状态
     * 
     * @param parentIds 家长ID数组
     * @param bindStatus 绑定状态
     * @return 结果
     */
    public int updateParentBindStatus(Long[] parentIds, String bindStatus);
}

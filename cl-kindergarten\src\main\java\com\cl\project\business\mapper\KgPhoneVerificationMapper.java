package com.cl.project.business.mapper;

import java.util.List;
import com.cl.project.business.domain.KgPhoneVerification;

/**
 * 手机号验证码Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface KgPhoneVerificationMapper 
{
    /**
     * 查询手机号验证码
     * 
     * @param verificationId 手机号验证码ID
     * @return 手机号验证码
     */
    public KgPhoneVerification selectKgPhoneVerificationById(Long verificationId);

    /**
     * 查询手机号验证码列表
     * 
     * @param kgPhoneVerification 手机号验证码
     * @return 手机号验证码集合
     */
    public List<KgPhoneVerification> selectKgPhoneVerificationList(KgPhoneVerification kgPhoneVerification);

    /**
     * 新增手机号验证码
     * 
     * @param kgPhoneVerification 手机号验证码
     * @return 结果
     */
    public int insertKgPhoneVerification(KgPhoneVerification kgPhoneVerification);

    /**
     * 修改手机号验证码
     * 
     * @param kgPhoneVerification 手机号验证码
     * @return 结果
     */
    public int updateKgPhoneVerification(KgPhoneVerification kgPhoneVerification);

    /**
     * 删除手机号验证码
     * 
     * @param verificationId 手机号验证码ID
     * @return 结果
     */
    public int deleteKgPhoneVerificationById(Long verificationId);

    /**
     * 批量删除手机号验证码
     * 
     * @param verificationIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteKgPhoneVerificationByIds(Long[] verificationIds);
}

package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgWechatUser;
import com.cl.project.business.service.IKgWechatUserService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 微信用户Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/user")
public class KgWechatUserController extends BaseController
{
    @Autowired
    private IKgWechatUserService kgWechatUserService;

    /**
     * 查询微信用户列表
     */
    @SaCheckPermission("business:user:list")
    @GetMapping("/list")
    public TableDataInfo list(KgWechatUser kgWechatUser)
    {
        startPage();
        List<KgWechatUser> list = kgWechatUserService.selectKgWechatUserList(kgWechatUser);
        return getDataTable(list);
    }

    /**
     * 导出微信用户列表
     */
    @SaCheckPermission("business:user:export")
    @Log(title = "微信用户", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgWechatUser kgWechatUser)
    {
        List<KgWechatUser> list = kgWechatUserService.selectKgWechatUserList(kgWechatUser);
        ExcelUtil<KgWechatUser> util = new ExcelUtil<KgWechatUser>(KgWechatUser.class);
        return util.exportExcel(list, "user");
    }

    /**
     * 获取微信用户详细信息
     */
    @SaCheckPermission("business:user:query")
    @GetMapping(value = "/{wechatUserId}")
    public AjaxResult getInfo(@PathVariable("wechatUserId") Long wechatUserId)
    {
        return AjaxResult.success(kgWechatUserService.selectKgWechatUserById(wechatUserId));
    }

    /**
     * 新增微信用户
     */
    @SaCheckPermission("business:user:add")
    @Log(title = "微信用户", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgWechatUser kgWechatUser)
    {
        return toAjax(kgWechatUserService.insertKgWechatUser(kgWechatUser));
    }

    /**
     * 修改微信用户
     */
    @SaCheckPermission("business:user:edit")
    @Log(title = "微信用户", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgWechatUser kgWechatUser)
    {
        return toAjax(kgWechatUserService.updateKgWechatUser(kgWechatUser));
    }

    /**
     * 删除微信用户
     */
    @SaCheckPermission("business:user:remove")
    @Log(title = "微信用户", businessType = BusinessType.DELETE)
	@DeleteMapping("/{wechatUserIds}")
    public AjaxResult remove(@PathVariable Long[] wechatUserIds)
    {
        return toAjax(kgWechatUserService.deleteKgWechatUserByIds(wechatUserIds));
    }
}

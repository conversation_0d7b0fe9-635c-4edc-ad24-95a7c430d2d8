package com.cl.project.business.controller;

import com.cl.project.business.domain.dto.DingtalkUserCreateResult;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgTeacher;
import java.util.List;
import com.cl.project.business.service.IKgTeacherService;
import com.cl.project.business.service.IDingtalkApiService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import com.cl.common.utils.StringUtils;

/**
 * 教师信息Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/teacher")
public class KgTeacherController extends BaseController
{
    private static final Logger logger = LoggerFactory.getLogger(KgTeacherController.class);
    
    @Autowired
    private IKgTeacherService kgTeacherService;
    
    @Autowired
    private IDingtalkApiService dingtalkApiService;

    /**
     * 查询教师信息列表
     */
//    @SaCheckPermission("kg:teacher:info:list")
    @GetMapping("/list")
    public TableDataInfo list(KgTeacher kgTeacher)
    {
        startPage();
        List<KgTeacher> list = kgTeacherService.selectKgTeacherList(kgTeacher);
        return getDataTable(list);
    }

    /**
     * 获取所有教师信息列表
     */
    @GetMapping("/allList")
    public AjaxResult allList() {
        // 复用已有的 selectKgTeacherList 方法（不分页）
        List<KgTeacher> list = kgTeacherService.selectKgTeacherList(new KgTeacher());
        return AjaxResult.success(list);
    }

    /**
     * 导出教师信息列表
     */
    @SaCheckPermission("kg:teacher:info:list")
    @Log(title = "教师信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgTeacher kgTeacher)
    {
        List<KgTeacher> list = kgTeacherService.selectKgTeacherList(kgTeacher);
        ExcelUtil<KgTeacher> util = new ExcelUtil<KgTeacher>(KgTeacher.class);
        return util.exportExcel(list, "teacher");
    }

    /**
     * 获取教师信息详细信息
     */
//    @SaCheckPermission("kg:teacher:info:list")
    @GetMapping(value = "/{teacherId}")
    public AjaxResult getInfo(@PathVariable("teacherId") Long teacherId)
    {
        return AjaxResult.success(kgTeacherService.selectKgTeacherById(teacherId));
    }

    /**
     * 新增教师信息
     */
//    @SaCheckPermission("kg:teacher:info:add")
    @Log(title = "教师信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgTeacher kgTeacher)
    {
        // 先新增本地教师信息
        int result = kgTeacherService.insertKgTeacher(kgTeacher);
        
        // 如果新增成功且有必要信息，则同步创建钉钉用户
        if (result > 0 && StringUtils.isNotEmpty(kgTeacher.getTeacherName())) {
            try {
                // 生成钉钉用户ID（使用教师ID）
                String dingtalkUserId = "teacher_" + kgTeacher.getTeacherId();
                
                DingtalkUserCreateResult createResult = dingtalkApiService.createUser(
                    dingtalkUserId,
                    kgTeacher.getTeacherName(),
                    kgTeacher.getPhone(),
                    kgTeacher.getEmail(),
                    kgTeacher.getPosition(),
                    1L // 默认分配到根部门
                );
                boolean needSaveDingtalkUserId = false;
                if (createResult != null && createResult.isSuccess()) {
                    needSaveDingtalkUserId = true;
                    logger.info("同步创建钉钉用户成功: teacherId={}, dingtalkUserId={}", 
                               kgTeacher.getTeacherId(), dingtalkUserId);
                } else if (createResult != null && createResult.getErrCode() != null && createResult.getErrCode() == 40103) {
                    // 钉钉返回“已发出邀请”，也保存ID
                    needSaveDingtalkUserId = true;
                    logger.warn("钉钉用户已发出邀请: teacherId={}, teacherName={}, dingtalkUserId={}, errMsg={}",
                               kgTeacher.getTeacherId(), kgTeacher.getTeacherName(), dingtalkUserId, createResult.getErrMsg());
                } else {
                    logger.warn("同步创建钉钉用户失败: teacherId={}, teacherName={}, errMsg={}, errCode={}", 
                               kgTeacher.getTeacherId(), kgTeacher.getTeacherName(),
                               createResult != null ? createResult.getErrMsg() : "未知错误",
                               createResult != null ? createResult.getErrCode() : null);
                }
                if (needSaveDingtalkUserId) {
                    kgTeacher.setDingtalkUserId(dingtalkUserId);
                    kgTeacherService.updateKgTeacher(kgTeacher);
                }
            } catch (Exception e) {
                logger.error("同步创建钉钉用户异常: teacherId={}, teacherName={}", 
                            kgTeacher.getTeacherId(), kgTeacher.getTeacherName(), e);
            }
        }
        
        return toAjax(result);
    }

    /**
     * 修改教师信息
     */
//    @SaCheckPermission("kg:teacher:info:edit")
    @Log(title = "教师信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgTeacher kgTeacher)
    {
        // 先更新本地教师信息
        int result = kgTeacherService.updateKgTeacher(kgTeacher);
        
        // 如果更新成功且有钉钉用户ID，则同步更新钉钉用户信息
        if (result > 0 && StringUtils.isNotEmpty(kgTeacher.getDingtalkUserId())) {
            try {
                boolean updateSuccess = dingtalkApiService.updateUser(
                    kgTeacher.getDingtalkUserId(),
                    kgTeacher.getTeacherName(),
                    kgTeacher.getPhone(),
                    kgTeacher.getEmail(),
                    kgTeacher.getPosition()
                );
                
                if (updateSuccess) {
                    logger.info("同步更新钉钉用户成功: teacherId={}, dingtalkUserId={}", 
                               kgTeacher.getTeacherId(), kgTeacher.getDingtalkUserId());
                } else {
                    logger.warn("同步更新钉钉用户失败: teacherId={}, dingtalkUserId={}", 
                               kgTeacher.getTeacherId(), kgTeacher.getDingtalkUserId());
                }
            } catch (Exception e) {
                logger.error("同步更新钉钉用户异常: teacherId={}, dingtalkUserId={}", 
                            kgTeacher.getTeacherId(), kgTeacher.getDingtalkUserId(), e);
            }
        }
        
        return toAjax(result);
    }

    /**
     * 删除教师信息
     */
//    @SaCheckPermission("kg:teacher:info:remove")
    @Log(title = "教师信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{teacherIds}")
    public AjaxResult remove(@PathVariable Long[] teacherIds)
    {
        try 
        {
            // 先获取要删除的教师信息，以便删除钉钉用户
            for (Long teacherId : teacherIds) 
            {
                KgTeacher teacher = kgTeacherService.selectKgTeacherById(teacherId);
                if (teacher != null && StringUtils.isNotEmpty(teacher.getDingtalkUserId())) 
                {
                    try 
                    {
                        boolean success = dingtalkApiService.deleteUser(teacher.getDingtalkUserId());
                        if (success) 
                        {
                            logger.info("删除钉钉用户成功: {}, dingtalkUserId: {}", teacher.getTeacherName(), teacher.getDingtalkUserId());
                        } 
                        else 
                        {
                            logger.warn("删除钉钉用户失败，但会继续删除本地记录: {}", teacher.getTeacherName());
                        }
                    } 
                    catch (Exception e) 
                    {
                        logger.error("删除钉钉用户异常，但会继续删除本地记录: {}", teacher.getTeacherName(), e);
                    }
                }
            }
            
            // 删除本地教师记录
            int result = kgTeacherService.deleteKgTeacherByIds(teacherIds);
            if (result > 0) 
            {
                return AjaxResult.success("删除成功");
            } 
            else 
            {
                return AjaxResult.error("删除失败");
            }
        } 
        catch (Exception e) 
        {
            logger.error("删除教师异常", e);
            return AjaxResult.error("删除失败: " + e.getMessage());
        }
    }
}

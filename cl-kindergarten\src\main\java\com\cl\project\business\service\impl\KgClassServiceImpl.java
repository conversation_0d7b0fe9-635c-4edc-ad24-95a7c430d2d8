package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgClassMapper;
import com.cl.project.business.domain.KgClass;
import com.cl.project.business.service.IKgClassService;

/**
 * 班级信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgClassServiceImpl implements IKgClassService 
{
    @Autowired
    private KgClassMapper kgClassMapper;

    /**
     * 查询班级信息
     * 
     * @param classId 班级信息ID
     * @return 班级信息
     */
    @Override
    public KgClass selectKgClassById(Long classId)
    {
        return kgClassMapper.selectKgClassById(classId);
    }
    
    /**
     * 根据钉钉部门ID查询班级信息
     * 
     * @param dingtalkDeptId 钉钉部门ID
     * @return 班级信息
     */
    @Override
    public KgClass selectKgClassByDingtalkDeptId(Long dingtalkDeptId)
    {
        return kgClassMapper.selectKgClassByDingtalkDeptId(dingtalkDeptId);
    }

    /**
     * 查询班级信息列表
     * 
     * @param kgClass 班级信息
     * @return 班级信息
     */
    @Override
    public List<KgClass> selectKgClassList(KgClass kgClass)
    {
        return kgClassMapper.selectKgClassList(kgClass);
    }

    /**
     * 新增班级信息
     * 
     * @param kgClass 班级信息
     * @return 结果
     */
    @Override
    public int insertKgClass(KgClass kgClass)
    {
        kgClass.setCreateTime(DateUtils.getNowDate());
        return kgClassMapper.insertKgClass(kgClass);
    }

    /**
     * 修改班级信息
     * 
     * @param kgClass 班级信息
     * @return 结果
     */
    @Override
    public int updateKgClass(KgClass kgClass)
    {
        kgClass.setUpdateTime(DateUtils.getNowDate());
        return kgClassMapper.updateKgClass(kgClass);
    }

    /**
     * 批量删除班级信息
     * 
     * @param classIds 需要删除的班级信息ID
     * @return 结果
     */
    @Override
    public int deleteKgClassByIds(Long[] classIds)
    {
        return kgClassMapper.deleteKgClassByIds(classIds);
    }

    /**
     * 删除班级信息信息
     * 
     * @param classId 班级信息ID
     * @return 结果
     */
    @Override
    public int deleteKgClassById(Long classId)
    {
        return kgClassMapper.deleteKgClassById(classId);
    }
}

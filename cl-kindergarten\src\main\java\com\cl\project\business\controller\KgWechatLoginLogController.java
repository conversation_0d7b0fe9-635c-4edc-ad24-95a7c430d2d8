package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgWechatLoginLog;
import com.cl.project.business.service.IKgWechatLoginLogService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 微信登录日志Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/log")
public class KgWechatLoginLogController extends BaseController
{
    @Autowired
    private IKgWechatLoginLogService kgWechatLoginLogService;

    /**
     * 查询微信登录日志列表
     */
    @SaCheckPermission("business:log:list")
    @GetMapping("/list")
    public TableDataInfo list(KgWechatLoginLog kgWechatLoginLog)
    {
        startPage();
        List<KgWechatLoginLog> list = kgWechatLoginLogService.selectKgWechatLoginLogList(kgWechatLoginLog);
        return getDataTable(list);
    }

    /**
     * 导出微信登录日志列表
     */
    @SaCheckPermission("business:log:export")
    @Log(title = "微信登录日志", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgWechatLoginLog kgWechatLoginLog)
    {
        List<KgWechatLoginLog> list = kgWechatLoginLogService.selectKgWechatLoginLogList(kgWechatLoginLog);
        ExcelUtil<KgWechatLoginLog> util = new ExcelUtil<KgWechatLoginLog>(KgWechatLoginLog.class);
        return util.exportExcel(list, "log");
    }

    /**
     * 获取微信登录日志详细信息
     */
    @SaCheckPermission("business:log:query")
    @GetMapping(value = "/{logId}")
    public AjaxResult getInfo(@PathVariable("logId") Long logId)
    {
        return AjaxResult.success(kgWechatLoginLogService.selectKgWechatLoginLogById(logId));
    }

    /**
     * 新增微信登录日志
     */
    @SaCheckPermission("business:log:add")
    @Log(title = "微信登录日志", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgWechatLoginLog kgWechatLoginLog)
    {
        return toAjax(kgWechatLoginLogService.insertKgWechatLoginLog(kgWechatLoginLog));
    }

    /**
     * 修改微信登录日志
     */
    @SaCheckPermission("business:log:edit")
    @Log(title = "微信登录日志", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgWechatLoginLog kgWechatLoginLog)
    {
        return toAjax(kgWechatLoginLogService.updateKgWechatLoginLog(kgWechatLoginLog));
    }

    /**
     * 删除微信登录日志
     */
    @SaCheckPermission("business:log:remove")
    @Log(title = "微信登录日志", businessType = BusinessType.DELETE)
	@DeleteMapping("/{logIds}")
    public AjaxResult remove(@PathVariable Long[] logIds)
    {
        return toAjax(kgWechatLoginLogService.deleteKgWechatLoginLogByIds(logIds));
    }
}

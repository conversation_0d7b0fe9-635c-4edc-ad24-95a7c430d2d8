<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgItemCategoryMapper">
    
    <resultMap type="KgItemCategory" id="KgItemCategoryResult">
        <result property="categoryId"    column="category_id"    />
        <result property="categoryName"    column="category_name"    />
        <result property="categoryCode"    column="category_code"    />
        <result property="parentId"    column="parent_id"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="status"    column="status"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgItemCategoryVo">
        select category_id, category_name, category_code, parent_id, sort_order, status, com_id, create_by, create_time, update_by, update_time, remark from kg_item_category
    </sql>

    <select id="selectKgItemCategoryList" parameterType="KgItemCategory" resultMap="KgItemCategoryResult">
        <include refid="selectKgItemCategoryVo"/>
        <where>  
            <if test="categoryName != null  and categoryName != ''"> and category_name like concat('%', #{categoryName}, '%')</if>
            <if test="categoryCode != null  and categoryCode != ''"> and category_code = #{categoryCode}</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="sortOrder != null "> and sort_order = #{sortOrder}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgItemCategoryById" parameterType="Long" resultMap="KgItemCategoryResult">
        <include refid="selectKgItemCategoryVo"/>
        where category_id = #{categoryId}
    </select>
        
    <insert id="insertKgItemCategory" parameterType="KgItemCategory" useGeneratedKeys="true" keyProperty="categoryId">
        insert into kg_item_category
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name,</if>
            <if test="categoryCode != null and categoryCode != ''">category_code,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="status != null">status,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">#{categoryName},</if>
            <if test="categoryCode != null and categoryCode != ''">#{categoryCode},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="status != null">#{status},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgItemCategory" parameterType="KgItemCategory">
        update kg_item_category
        <trim prefix="SET" suffixOverrides=",">
            <if test="categoryName != null and categoryName != ''">category_name = #{categoryName},</if>
            <if test="categoryCode != null and categoryCode != ''">category_code = #{categoryCode},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="status != null">status = #{status},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where category_id = #{categoryId}
    </update>

    <delete id="deleteKgItemCategoryById" parameterType="Long">
        delete from kg_item_category where category_id = #{categoryId}
    </delete>

    <delete id="deleteKgItemCategoryByIds" parameterType="String">
        delete from kg_item_category where category_id in 
        <foreach item="categoryId" collection="array" open="(" separator="," close=")">
            #{categoryId}
        </foreach>
    </delete>
    
</mapper>
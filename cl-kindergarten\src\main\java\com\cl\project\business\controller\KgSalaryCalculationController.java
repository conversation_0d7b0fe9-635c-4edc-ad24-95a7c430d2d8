package com.cl.project.business.controller;

import java.util.List;
import java.util.Map;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.service.IKgSalaryCalculationService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;

/**
 * 工资计算Controller
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/business/salary-calculation")
public class KgSalaryCalculationController extends BaseController
{
    @Autowired
    private IKgSalaryCalculationService salaryCalculationService;

    /**
     * 计算教师月度工资
     */
    @SaCheckPermission("kg:salary:calculate")
    @PostMapping("/calculate-monthly")
    @Log(title = "计算月度工资", businessType = BusinessType.OTHER)
    public AjaxResult calculateMonthlySalary(
            @RequestParam Long teacherId,
            @RequestParam Integer year,
            @RequestParam Integer month)
    {
        Map<String, Object> result = salaryCalculationService.calculateMonthlySalary(teacherId, year, month);
        return AjaxResult.success(result);
    }

    /**
     * 批量计算全体教师工资
     */
    @SaCheckPermission("kg:salary:calculate")
    @PostMapping("/calculate-all-batch")
    @Log(title = "批量计算教师工资", businessType = BusinessType.OTHER)
    public AjaxResult calculateAllSalaryBatch(
            @RequestParam Integer year,
            @RequestParam Integer month)
    {
        Map<String, Object> result = salaryCalculationService.calculateAllSalaryBatch(year, month);
        return AjaxResult.success(result);
    }

    /**
     * 预览工资计算结果
     */
    @SaCheckPermission("kg:salary:view")
    @GetMapping("/preview")
    public AjaxResult previewSalaryCalculation(
            @RequestParam Long teacherId,
            @RequestParam Integer year,
            @RequestParam Integer month)
    {
        Map<String, Object> preview = salaryCalculationService.previewSalaryCalculation(teacherId, year, month);
        return AjaxResult.success(preview);
    }

    /**
     * 获取工资计算规则
     */
    @SaCheckPermission("kg:salary:view")
    @GetMapping("/rules")
    public AjaxResult getSalaryCalculationRules(@RequestParam Long teacherId)
    {
        Map<String, Object> rules = salaryCalculationService.getSalaryCalculationRules(teacherId);
        return AjaxResult.success(rules);
    }

    /**
     * 生成工资单
     */
    @SaCheckPermission("kg:salary:generate")
    @PostMapping("/generate-payslips")
    @Log(title = "生成工资单", businessType = BusinessType.INSERT)
    public AjaxResult generatePayslips(@RequestBody List<Map<String, Object>> calculations)
    {
        int count = salaryCalculationService.generatePayslips(calculations);
        return AjaxResult.success("成功生成 " + count + " 条工资单");
    }

    /**
     * 重新计算工资
     */
    @SaCheckPermission("kg:salary:calculate")
    @PostMapping("/recalculate")
    @Log(title = "重新计算工资", businessType = BusinessType.UPDATE)
    public AjaxResult recalculateSalary(
            @RequestParam Long salaryId,
            @RequestParam(required = false) String reason)
    {
        Map<String, Object> result = salaryCalculationService.recalculateSalary(salaryId, reason);
        return AjaxResult.success(result);
    }

    /**
     * 工资调整
     */
    @SaCheckPermission("kg:salary:adjust")
    @PostMapping("/adjust")
    @Log(title = "工资调整", businessType = BusinessType.UPDATE)
    public AjaxResult adjustSalary(
            @RequestParam Long salaryId,
            @RequestParam String adjustType,
            @RequestParam Double adjustAmount,
            @RequestParam String reason)
    {
        int result = salaryCalculationService.adjustSalary(salaryId, adjustType, adjustAmount, reason);
        return toAjax(result);
    }

    /**
     * 获取工资统计
     */
    @SaCheckPermission("kg:salary:view")
    @GetMapping("/statistics")
    public AjaxResult getSalaryStatistics(
            @RequestParam Integer year,
            @RequestParam Integer month)
    {
        Map<String, Object> statistics = salaryCalculationService.getSalaryStatistics(year, month);
        return AjaxResult.success(statistics);
    }

    /**
     * 获取教师工资明细
     */
    @SaCheckPermission("kg:salary:view")
    @GetMapping("/details")
    public AjaxResult getSalaryDetails(
            @RequestParam Long teacherId,
            @RequestParam Integer year,
            @RequestParam Integer month)
    {
        Map<String, Object> details = salaryCalculationService.getSalaryDetails(teacherId, year, month);
        return AjaxResult.success(details);
    }

    /**
     * 工资发放确认
     */
    @SaCheckPermission("kg:salary:confirm")
    @PostMapping("/confirm-payment")
    @Log(title = "工资发放确认", businessType = BusinessType.UPDATE)
    public AjaxResult confirmSalaryPayment(@RequestParam List<Long> salaryIds)
    {
        int count = salaryCalculationService.confirmSalaryPayment(salaryIds);
        return AjaxResult.success("成功确认 " + count + " 条工资发放");
    }

    /**
     * 生成工资报表
     */
    @SaCheckPermission("kg:salary:report")
    @PostMapping("/generate-report")
    @Log(title = "生成工资报表", businessType = BusinessType.EXPORT)
    public AjaxResult generateSalaryReport(
            @RequestParam Integer year,
            @RequestParam Integer month)
    {
        String reportPath = salaryCalculationService.generateSalaryReport(year, month);
        return AjaxResult.success("报表生成成功", reportPath);
    }

    /**
     * 计算课时费统计
     */
    @SaCheckPermission("kg:salary:view")
    @GetMapping("/course-fee-statistics")
    public AjaxResult getCourseFeeStatistics(
            @RequestParam Long teacherId,
            @RequestParam Integer year,
            @RequestParam Integer month)
    {
        Map<String, Object> statistics = salaryCalculationService.getCourseFeeStatistics(teacherId, year, month);
        return AjaxResult.success(statistics);
    }
}

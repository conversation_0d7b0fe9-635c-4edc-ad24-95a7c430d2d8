<template>
  <div class="dashboard-container">
    <!-- 数据概览卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
        <div class="dashboard-card">
          <div class="card-panel">
            <div class="card-panel-icon-wrapper icon-student">
              <i class="el-icon-user"></i>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">在园学生</div>
              <div class="card-panel-num">{{ dashboardData.studentCount }}</div>
            </div>
          </div>
        </div>
      </el-col>
      
      <el-col :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
        <div class="dashboard-card">
          <div class="card-panel">
            <div class="card-panel-icon-wrapper icon-teacher">
              <i class="el-icon-user-solid"></i>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">在职教师</div>
              <div class="card-panel-num">{{ dashboardData.teacherCount }}</div>
            </div>
          </div>
        </div>
      </el-col>
      
      <el-col :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
        <div class="dashboard-card">
          <div class="card-panel">
            <div class="card-panel-icon-wrapper icon-attendance">
              <i class="el-icon-check"></i>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">今日出勤率</div>
              <div class="card-panel-num">{{ dashboardData.attendanceRate }}%</div>
            </div>
          </div>
        </div>
      </el-col>
      
      <el-col :xl="6" :lg="12" :md="12" :sm="24" :xs="24">
        <div class="dashboard-card">
          <div class="card-panel">
            <div class="card-panel-icon-wrapper icon-income">
              <i class="el-icon-money"></i>
            </div>
            <div class="card-panel-description">
              <div class="card-panel-text">本月收入</div>
              <div class="card-panel-num">¥{{ dashboardData.monthlyIncome }}</div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 图表和统计 -->
    <el-row :gutter="20" class="mb20">
      <el-col :xl="12" :lg="24" :md="24" :sm="24" :xs="24">
        <div class="dashboard-chart">
          <div class="chart-header">
            <h4>近7天考勤趋势</h4>
          </div>
          <div ref="attendanceChart" class="chart-container"></div>
        </div>
      </el-col>
      
      <el-col :xl="12" :lg="24" :md="24" :sm="24" :xs="24">
        <div class="dashboard-chart">
          <div class="chart-header">
            <h4>班级学生分布</h4>
          </div>
          <div ref="classChart" class="chart-container"></div>
        </div>
      </el-col>
    </el-row>

    <!-- 待办事项和快捷操作 -->
    <el-row :gutter="20">
      <el-col :xl="8" :lg="24" :md="24" :sm="24" :xs="24">
        <div class="dashboard-panel">
          <div class="panel-header">
            <h4>待办事项</h4>
            <span class="panel-count">{{ todoList.length }}</span>
          </div>
          <div class="panel-content">
            <div v-for="item in todoList" :key="item.id" class="todo-item">
              <div class="todo-icon" :class="item.type">
                <i :class="item.icon"></i>
              </div>
              <div class="todo-content">
                <div class="todo-title">{{ item.title }}</div>
                <div class="todo-desc">{{ item.desc }}</div>
              </div>
              <div class="todo-action">
                <el-button type="text" @click="handleTodo(item)">处理</el-button>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      
      <el-col :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
        <div class="dashboard-panel">
          <div class="panel-header">
            <h4>快捷操作</h4>
          </div>
          <div class="panel-content">
            <div class="quick-actions">
              <div class="action-item" @click="$router.push('/kg/attendance/student')">
                <i class="el-icon-check"></i>
                <span>学生考勤</span>
              </div>
              <div class="action-item" @click="$router.push('/kg/course/enrollment')">
                <i class="el-icon-plus"></i>
                <span>课程报名</span>
              </div>
              <div class="action-item" @click="$router.push('/kg/communication/message')">
                <i class="el-icon-message"></i>
                <span>消息推送</span>
              </div>
              <div class="action-item" @click="handleExport">
                <i class="el-icon-download"></i>
                <span>数据导出</span>
              </div>
            </div>
          </div>
        </div>
      </el-col>
      
      <el-col :xl="8" :lg="12" :md="12" :sm="24" :xs="24">
        <div class="dashboard-panel">
          <div class="panel-header">
            <h4>最新通知</h4>
            <el-button type="text" @click="$router.push('/kg/communication/notice')">更多</el-button>
          </div>
          <div class="panel-content">
            <div v-for="notice in noticeList" :key="notice.id" class="notice-item">
              <div class="notice-title">{{ notice.title }}</div>
              <div class="notice-time">{{ notice.createTime }}</div>
            </div>
          </div>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getDashboardData, getTodoList, getNoticeList } from "@/api/kg/dashboard";

export default {
  name: "Dashboard",
  data() {
    return {
      // 仪表板数据
      dashboardData: {
        studentCount: 0,
        teacherCount: 0,
        attendanceRate: 0,
        monthlyIncome: 0
      },
      // 待办事项
      todoList: [],
      // 通知列表
      noticeList: [],
      // 图表实例
      attendanceChart: null,
      classChart: null
    };
  },
  created() {
    this.loadDashboardData();
    this.loadTodoList();
    this.loadNoticeList();
  },
  mounted() {
    this.$nextTick(() => {
      this.initCharts();
    });
  },
  methods: {
    // 加载仪表板数据
    loadDashboardData() {
      getDashboardData().then(response => {
        this.dashboardData = response.data;
        this.updateCharts();
      }).catch(() => {
        // 模拟数据
        this.dashboardData = {
          studentCount: 125,
          teacherCount: 18,
          attendanceRate: 92.5,
          monthlyIncome: 45680
        };
      });
    },
    
    // 加载待办事项
    loadTodoList() {
      getTodoList().then(response => {
        this.todoList = response.data;
      }).catch(() => {
        // 模拟数据
        this.todoList = [
          {
            id: 1,
            type: 'warning',
            icon: 'el-icon-warning',
            title: '待确认考勤',
            desc: '有5条学生考勤记录需要确认'
          },
          {
            id: 2,
            type: 'danger',
            icon: 'el-icon-warning-outline',
            title: '欠费提醒',
            desc: '有3名学生园费逾期未缴'
          },
          {
            id: 3,
            type: 'info',
            icon: 'el-icon-info',
            title: '请假申请',
            desc: '有2条请假申请待审批'
          }
        ];
      });
    },
    
    // 加载通知列表
    loadNoticeList() {
      getNoticeList().then(response => {
        this.noticeList = response.data;
      }).catch(() => {
        // 模拟数据
        this.noticeList = [
          {
            id: 1,
            title: '关于调整园费标准的通知',
            createTime: '2025-07-28'
          },
          {
            id: 2,
            title: '端午节放假安排',
            createTime: '2025-07-27'
          },
          {
            id: 3,
            title: '新学期招生简章',
            createTime: '2025-07-26'
          }
        ];
      });
    },
    
    // 初始化图表
    initCharts() {
      // 这里需要引入echarts或其他图表库
      // 考勤趋势图
      this.initAttendanceChart();
      // 班级分布图
      this.initClassChart();
    },
    
    // 初始化考勤趋势图
    initAttendanceChart() {
      // 模拟图表初始化
      console.log('初始化考勤趋势图');
    },
    
    // 初始化班级分布图
    initClassChart() {
      // 模拟图表初始化
      console.log('初始化班级分布图');
    },
    
    // 更新图表
    updateCharts() {
      if (this.attendanceChart) {
        this.attendanceChart.setOption({
          // 更新考勤图表数据
        });
      }
      if (this.classChart) {
        this.classChart.setOption({
          // 更新班级图表数据
        });
      }
    },
    
    // 处理待办事项
    handleTodo(item) {
      switch(item.type) {
        case 'warning':
          this.$router.push('/kg/attendance/student');
          break;
        case 'danger':
          this.$router.push('/kg/finance/tuition');
          break;
        case 'info':
          this.$router.push('/kg/attendance/student');
          break;
      }
    },
    
    // 数据导出
    handleExport() {
      this.$message.info('数据导出功能开发中...');
    }
  }
};
</script>

<style lang="scss" scoped>
.dashboard-container {
  padding: 20px;
  background-color: #f5f5f5;
  min-height: 100vh;
}

.mb20 {
  margin-bottom: 20px;
}

// 数据卡片样式
.dashboard-card {
  .card-panel {
    height: 108px;
    cursor: pointer;
    font-size: 12px;
    position: relative;
    overflow: hidden;
    color: #666;
    background: #fff;
    box-shadow: 4px 4px 40px rgba(0, 0, 0, 0.05);
    border-color: rgba(0, 0, 0, 0.05);
    border-radius: 8px;
    padding: 20px;
    display: flex;
    align-items: center;

    &:hover {
      .card-panel-icon-wrapper {
        color: #fff;
      }
      .icon-student { background: #40c9c6; }
      .icon-teacher { background: #36a3f7; }
      .icon-attendance { background: #f4516c; }
      .icon-income { background: #34bfa3; }
    }

    .card-panel-icon-wrapper {
      float: left;
      font-size: 48px;
      margin: 14px 0 0 14px;
      padding: 16px;
      transition: all 0.38s ease-out;
      border-radius: 6px;
      color: #fff;
      margin-right: 20px;
    }

    .icon-student { background: #40c9c6; }
    .icon-teacher { background: #36a3f7; }
    .icon-attendance { background: #f4516c; }
    .icon-income { background: #34bfa3; }

    .card-panel-description {
      float: right;
      font-weight: bold;
      margin: 26px;
      margin-left: 0px;
      flex: 1;

      .card-panel-text {
        line-height: 18px;
        color: rgba(0, 0, 0, 0.45);
        font-size: 16px;
        margin-bottom: 12px;
      }

      .card-panel-num {
        font-size: 20px;
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }
}

// 图表样式
.dashboard-chart {
  background: #fff;
  border-radius: 8px;
  box-shadow: 4px 4px 40px rgba(0, 0, 0, 0.05);
  padding: 20px;

  .chart-header {
    margin-bottom: 20px;
    h4 {
      margin: 0;
      color: #333;
    }
  }

  .chart-container {
    width: 100%;
    height: 300px;
  }
}

// 面板样式
.dashboard-panel {
  background: #fff;
  border-radius: 8px;
  box-shadow: 4px 4px 40px rgba(0, 0, 0, 0.05);
  margin-bottom: 20px;

  .panel-header {
    padding: 20px 20px 0;
    display: flex;
    justify-content: space-between;
    align-items: center;

    h4 {
      margin: 0;
      color: #333;
    }

    .panel-count {
      background: #f56c6c;
      color: #fff;
      border-radius: 10px;
      padding: 2px 8px;
      font-size: 12px;
    }
  }

  .panel-content {
    padding: 20px;
  }
}

// 待办事项样式
.todo-item {
  display: flex;
  align-items: center;
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  .todo-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;

    &.warning { background: #fdf6ec; color: #e6a23c; }
    &.danger { background: #fef0f0; color: #f56c6c; }
    &.info { background: #f4f4f5; color: #909399; }
  }

  .todo-content {
    flex: 1;

    .todo-title {
      font-weight: 500;
      color: #333;
      margin-bottom: 4px;
    }

    .todo-desc {
      font-size: 12px;
      color: #999;
    }
  }
}

// 快捷操作样式
.quick-actions {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 16px;

  .action-item {
    text-align: center;
    padding: 20px;
    border: 1px dashed #ddd;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      border-color: #409eff;
      color: #409eff;
    }

    i {
      font-size: 24px;
      display: block;
      margin-bottom: 8px;
    }

    span {
      font-size: 14px;
    }
  }
}

// 通知列表样式
.notice-item {
  padding: 12px 0;
  border-bottom: 1px solid #f0f0f0;

  &:last-child {
    border-bottom: none;
  }

  .notice-title {
    color: #333;
    margin-bottom: 4px;
    font-size: 14px;
  }

  .notice-time {
    color: #999;
    font-size: 12px;
  }
}
</style>

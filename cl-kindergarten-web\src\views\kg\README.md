# 幼儿园管理系统前端文件结构

## 概述
根据菜单SQL语句重新组织的幼儿园管理系统前端文件结构，采用模块化设计，便于维护和扩展。

## 目录结构

```
kg/                                    # 幼儿园管理主目录
├── attendance/                        # 考勤管理
│   ├── student/                       # 学生考勤
│   │   └── index.vue                  # 学生考勤页面
│   └── teacher/                       # 教师考勤
│       └── index.vue                  # 教师考勤页面
├── course/                            # 托管管理
│   ├── manage/                        # 课程管理
│   │   └── index.vue                  # 课程管理页面
│   └── attendance/                    # 托管考勤
│       └── index.vue                  # 托管考勤页面
├── finance/                           # 费用管理
│   ├── tuition/                       # 园费管理
│   │   └── index.vue                  # 园费管理页面
│   └── course-fee/                    # 托管费管理
│       └── index.vue                  # 托管费管理页面（待创建）
├── student/                           # 学生管理
│   ├── info/                          # 学生信息
│   │   └── index.vue                  # 学生信息页面
│   └── class/                         # 班级管理
│       └── index.vue                  # 班级管理页面
├── salary/                            # 工资管理
│   ├── query/                         # 工资查询
│   │   └── index.vue                  # 工资查询页面
│   └── calculate/                     # 工资计算
│       └── index.vue                  # 工资计算页面
├── inventory/                         # 库存管理（待创建）
│   ├── item/                          # 物品管理
│   │   └── index.vue                  # 物品管理页面
│   └── record/                        # 出入库
│       └── index.vue                  # 出入库页面
├── report/                            # 统计报表（待创建）
│   ├── attendance/                    # 考勤统计
│   │   └── index.vue                  # 考勤统计页面
│   └── finance/                       # 财务报表
│       └── index.vue                  # 财务报表页面
└── README.md                          # 本文档
```

## API文件结构

```
api/kg/                                # 幼儿园管理API目录
├── attendance/                        # 考勤管理API
│   ├── student.js                     # 学生考勤API
│   └── teacher.js                     # 教师考勤API
├── course/                            # 托管管理API
│   ├── manage.js                      # 课程管理API
│   └── attendance.js                  # 托管考勤API
├── finance/                           # 费用管理API
│   ├── tuition.js                     # 园费管理API
│   └── course-fee.js                  # 托管费管理API（待创建）
├── student/                           # 学生管理API
│   ├── info.js                        # 学生信息API
│   └── class.js                       # 班级管理API
├── salary/                            # 工资管理API
│   ├── query.js                       # 工资查询API
│   └── calculate.js                   # 工资计算API
├── teacher/                           # 教师管理API
│   └── info.js                        # 教师信息API
├── inventory/                         # 库存管理API（待创建）
│   ├── item.js                        # 物品管理API
│   └── record.js                      # 出入库API
└── report/                            # 统计报表API（待创建）
    ├── attendance.js                  # 考勤统计API
    └── finance.js                     # 财务报表API
```

## 菜单权限对应关系

### 考勤管理 (kg:attendance)
- **学生考勤** (`kg/attendance/student`)
  - 学生签到: `kg:attendance:student:checkin`
  - 学生签退: `kg:attendance:student:checkout`
  - 考勤确认: `kg:attendance:student:confirm`
  - 缺勤登记: `kg:attendance:student:absence`
  - 考勤查询: `kg:attendance:student:query`

- **教师考勤** (`kg/attendance/teacher`)
  - 教师签到: `kg:attendance:teacher:checkin`
  - 教师签退: `kg:attendance:teacher:checkout`
  - 教师考勤查询: `kg:attendance:teacher:query`

### 托管管理 (kg:course)
- **课程管理** (`kg/course/manage`)
  - 课程新增: `kg:course:manage:add`
  - 课程修改: `kg:course:manage:edit`
  - 课程删除: `kg:course:manage:remove`

- **托管考勤** (`kg/course/attendance`)
  - 托管签到: `kg:course:attendance:checkin`
  - 托管确认: `kg:course:attendance:confirm`
  - 托管查询: `kg:course:attendance:query`

### 费用管理 (kg:finance)
- **园费管理** (`kg/finance/tuition`)
  - 查看园费: `kg:finance:tuition:view`
  - 发送账单: `kg:finance:tuition:send`
  - 园费计算: `kg:finance:tuition:calculate`

- **托管费管理** (`kg/finance/course-fee`)
  - 查看托管费: `kg:finance:course:view`
  - 发送托管账单: `kg:finance:course:send`
  - 托管费计算: `kg:finance:course:calculate`

### 学生管理 (kg:student)
- **学生信息** (`kg/student/info`)
  - 学生新增: `kg:student:info:add`
  - 学生修改: `kg:student:info:edit`
  - 学生删除: `kg:student:info:remove`

- **班级管理** (`kg/student/class`)
  - 班级新增: `kg:student:class:add`
  - 班级修改: `kg:student:class:edit`
  - 班级删除: `kg:student:class:remove`

### 工资管理 (kg:salary)
- **工资查询** (`kg/salary/query`)
  - 工资查询列表: `kg:salary:query:list`

- **工资计算** (`kg/salary/calculate`)
  - 工资确认: `kg:salary:calculate:confirm`
  - 工资发放: `kg:salary:calculate:pay`

## 已创建的文件

### Vue组件文件
1. `kg/attendance/student/index.vue` - 学生考勤管理
2. `kg/attendance/teacher/index.vue` - 教师考勤管理
3. `kg/course/manage/index.vue` - 课程管理
4. `kg/course/attendance/index.vue` - 托管考勤管理
5. `kg/finance/tuition/index.vue` - 园费管理
6. `kg/student/info/index.vue` - 学生信息管理
7. `kg/student/class/index.vue` - 班级管理
8. `kg/salary/query/index.vue` - 工资查询
9. `kg/salary/calculate/index.vue` - 工资计算

### API文件
1. `api/kg/attendance/student.js` - 学生考勤API
2. `api/kg/attendance/teacher.js` - 教师考勤API
3. `api/kg/course/manage.js` - 课程管理API
4. `api/kg/course/attendance.js` - 托管考勤API
5. `api/kg/finance/tuition.js` - 园费管理API
6. `api/kg/student/info.js` - 学生信息API
7. `api/kg/student/class.js` - 班级管理API
8. `api/kg/teacher/info.js` - 教师信息API
9. `api/kg/salary/query.js` - 工资查询API
10. `api/kg/salary/calculate.js` - 工资计算API

## 待创建的文件

### Vue组件文件
1. `kg/finance/course-fee/index.vue` - 托管费管理
2. `kg/inventory/item/index.vue` - 物品管理
3. `kg/inventory/record/index.vue` - 出入库管理
4. `kg/report/attendance/index.vue` - 考勤统计
5. `kg/report/finance/index.vue` - 财务报表

### API文件
1. `api/kg/finance/course-fee.js` - 托管费管理API
2. `api/kg/inventory/item.js` - 物品管理API
3. `api/kg/inventory/record.js` - 出入库API
4. `api/kg/report/attendance.js` - 考勤统计API
5. `api/kg/report/finance.js` - 财务报表API

## 注意事项

1. **路由配置**: 需要在后端配置对应的路由，确保前端路由能正确加载组件
2. **权限控制**: 所有页面都使用了 `v-hasPermi` 指令进行权限控制
3. **字典数据**: 部分页面使用了字典数据，需要在后端配置对应的字典类型
4. **API接口**: 所有API接口都需要在后端实现对应的Controller和Service
5. **数据库表**: 需要创建对应的数据库表结构

## 字典类型

需要在系统中配置以下字典类型：
- `kg_attendance_status` - 考勤状态
- `kg_course_attendance_status` - 托管考勤状态
- `kg_course_type` - 课程类型
- `kg_student_status` - 学生状态
- `kg_payment_status` - 缴费状态

## 下一步工作

1. 创建剩余的Vue组件和API文件
2. 实现后端对应的Controller、Service和Mapper
3. 创建数据库表结构
4. 配置路由和权限
5. 测试各个功能模块

package com.cl.project.business.service.impl;

import java.util.List;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgCourseBillDetailMapper;
import com.cl.project.business.domain.KgCourseBillDetail;
import com.cl.project.business.service.IKgCourseBillDetailService;

/**
 * 托管费账单明细Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgCourseBillDetailServiceImpl implements IKgCourseBillDetailService 
{
    @Autowired
    private KgCourseBillDetailMapper kgCourseBillDetailMapper;

    /**
     * 查询托管费账单明细
     * 
     * @param detailId 托管费账单明细ID
     * @return 托管费账单明细
     */
    @Override
    public KgCourseBillDetail selectKgCourseBillDetailById(Long detailId)
    {
        return kgCourseBillDetailMapper.selectKgCourseBillDetailById(detailId);
    }

    /**
     * 查询托管费账单明细列表
     * 
     * @param kgCourseBillDetail 托管费账单明细
     * @return 托管费账单明细
     */
    @Override
    public List<KgCourseBillDetail> selectKgCourseBillDetailList(KgCourseBillDetail kgCourseBillDetail)
    {
        return kgCourseBillDetailMapper.selectKgCourseBillDetailList(kgCourseBillDetail);
    }

    /**
     * 新增托管费账单明细
     * 
     * @param kgCourseBillDetail 托管费账单明细
     * @return 结果
     */
    @Override
    public int insertKgCourseBillDetail(KgCourseBillDetail kgCourseBillDetail)
    {
        return kgCourseBillDetailMapper.insertKgCourseBillDetail(kgCourseBillDetail);
    }

    /**
     * 修改托管费账单明细
     * 
     * @param kgCourseBillDetail 托管费账单明细
     * @return 结果
     */
    @Override
    public int updateKgCourseBillDetail(KgCourseBillDetail kgCourseBillDetail)
    {
        return kgCourseBillDetailMapper.updateKgCourseBillDetail(kgCourseBillDetail);
    }

    /**
     * 批量删除托管费账单明细
     * 
     * @param detailIds 需要删除的托管费账单明细ID
     * @return 结果
     */
    @Override
    public int deleteKgCourseBillDetailByIds(Long[] detailIds)
    {
        return kgCourseBillDetailMapper.deleteKgCourseBillDetailByIds(detailIds);
    }

    /**
     * 删除托管费账单明细信息
     * 
     * @param detailId 托管费账单明细ID
     * @return 结果
     */
    @Override
    public int deleteKgCourseBillDetailById(Long detailId)
    {
        return kgCourseBillDetailMapper.deleteKgCourseBillDetailById(detailId);
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgMobilePermissionConfigMapper">
    
    <resultMap type="KgMobilePermissionConfig" id="KgMobilePermissionConfigResult">
        <result property="configId"    column="config_id"    />
        <result property="roleKey"    column="role_key"    />
        <result property="permissionType"    column="permission_type"    />
        <result property="dataScope"    column="data_scope"    />
        <result property="timeRestriction"    column="time_restriction"    />
        <result property="locationRestriction"    column="location_restriction"    />
        <result property="approvalRequired"    column="approval_required"    />
        <result property="status"    column="status"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgMobilePermissionConfigVo">
        select config_id, role_key, permission_type, data_scope, time_restriction, location_restriction, approval_required, status, com_id, create_by, create_time, update_by, update_time, remark from kg_mobile_permission_config
    </sql>

    <select id="selectKgMobilePermissionConfigList" parameterType="KgMobilePermissionConfig" resultMap="KgMobilePermissionConfigResult">
        <include refid="selectKgMobilePermissionConfigVo"/>
        <where>  
            <if test="roleKey != null  and roleKey != ''"> and role_key = #{roleKey}</if>
            <if test="permissionType != null  and permissionType != ''"> and permission_type = #{permissionType}</if>
            <if test="dataScope != null  and dataScope != ''"> and data_scope = #{dataScope}</if>
            <if test="timeRestriction != null  and timeRestriction != ''"> and time_restriction = #{timeRestriction}</if>
            <if test="locationRestriction != null "> and location_restriction = #{locationRestriction}</if>
            <if test="approvalRequired != null "> and approval_required = #{approvalRequired}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgMobilePermissionConfigById" parameterType="Long" resultMap="KgMobilePermissionConfigResult">
        <include refid="selectKgMobilePermissionConfigVo"/>
        where config_id = #{configId}
    </select>
        
    <insert id="insertKgMobilePermissionConfig" parameterType="KgMobilePermissionConfig" useGeneratedKeys="true" keyProperty="configId">
        insert into kg_mobile_permission_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="roleKey != null and roleKey != ''">role_key,</if>
            <if test="permissionType != null and permissionType != ''">permission_type,</if>
            <if test="dataScope != null">data_scope,</if>
            <if test="timeRestriction != null">time_restriction,</if>
            <if test="locationRestriction != null">location_restriction,</if>
            <if test="approvalRequired != null">approval_required,</if>
            <if test="status != null">status,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="roleKey != null and roleKey != ''">#{roleKey},</if>
            <if test="permissionType != null and permissionType != ''">#{permissionType},</if>
            <if test="dataScope != null">#{dataScope},</if>
            <if test="timeRestriction != null">#{timeRestriction},</if>
            <if test="locationRestriction != null">#{locationRestriction},</if>
            <if test="approvalRequired != null">#{approvalRequired},</if>
            <if test="status != null">#{status},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgMobilePermissionConfig" parameterType="KgMobilePermissionConfig">
        update kg_mobile_permission_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="roleKey != null and roleKey != ''">role_key = #{roleKey},</if>
            <if test="permissionType != null and permissionType != ''">permission_type = #{permissionType},</if>
            <if test="dataScope != null">data_scope = #{dataScope},</if>
            <if test="timeRestriction != null">time_restriction = #{timeRestriction},</if>
            <if test="locationRestriction != null">location_restriction = #{locationRestriction},</if>
            <if test="approvalRequired != null">approval_required = #{approvalRequired},</if>
            <if test="status != null">status = #{status},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where config_id = #{configId}
    </update>

    <delete id="deleteKgMobilePermissionConfigById" parameterType="Long">
        delete from kg_mobile_permission_config where config_id = #{configId}
    </delete>

    <delete id="deleteKgMobilePermissionConfigByIds" parameterType="String">
        delete from kg_mobile_permission_config where config_id in 
        <foreach item="configId" collection="array" open="(" separator="," close=")">
            #{configId}
        </foreach>
    </delete>
    
</mapper>
package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgMessagePushMapper;
import com.cl.project.business.domain.KgMessagePush;
import com.cl.project.business.service.IKgMessagePushService;

/**
 * 消息推送记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgMessagePushServiceImpl implements IKgMessagePushService 
{
    @Autowired
    private KgMessagePushMapper kgMessagePushMapper;

    /**
     * 查询消息推送记录
     * 
     * @param pushId 消息推送记录ID
     * @return 消息推送记录
     */
    @Override
    public KgMessagePush selectKgMessagePushById(Long pushId)
    {
        return kgMessagePushMapper.selectKgMessagePushById(pushId);
    }

    /**
     * 查询消息推送记录列表
     * 
     * @param kgMessagePush 消息推送记录
     * @return 消息推送记录
     */
    @Override
    public List<KgMessagePush> selectKgMessagePushList(KgMessagePush kgMessagePush)
    {
        return kgMessagePushMapper.selectKgMessagePushList(kgMessagePush);
    }

    /**
     * 新增消息推送记录
     * 
     * @param kgMessagePush 消息推送记录
     * @return 结果
     */
    @Override
    public int insertKgMessagePush(KgMessagePush kgMessagePush)
    {
        kgMessagePush.setCreateTime(DateUtils.getNowDate());
        return kgMessagePushMapper.insertKgMessagePush(kgMessagePush);
    }

    /**
     * 修改消息推送记录
     * 
     * @param kgMessagePush 消息推送记录
     * @return 结果
     */
    @Override
    public int updateKgMessagePush(KgMessagePush kgMessagePush)
    {
        kgMessagePush.setUpdateTime(DateUtils.getNowDate());
        return kgMessagePushMapper.updateKgMessagePush(kgMessagePush);
    }

    /**
     * 批量删除消息推送记录
     * 
     * @param pushIds 需要删除的消息推送记录ID
     * @return 结果
     */
    @Override
    public int deleteKgMessagePushByIds(Long[] pushIds)
    {
        return kgMessagePushMapper.deleteKgMessagePushByIds(pushIds);
    }

    /**
     * 删除消息推送记录信息
     * 
     * @param pushId 消息推送记录ID
     * @return 结果
     */
    @Override
    public int deleteKgMessagePushById(Long pushId)
    {
        return kgMessagePushMapper.deleteKgMessagePushById(pushId);
    }
}

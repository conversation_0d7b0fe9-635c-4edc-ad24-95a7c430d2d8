package com.cl.project.business.mapper;

import java.util.List;
import com.cl.project.business.domain.KgWechatLoginLog;

/**
 * 微信登录日志Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface KgWechatLoginLogMapper 
{
    /**
     * 查询微信登录日志
     * 
     * @param logId 微信登录日志ID
     * @return 微信登录日志
     */
    public KgWechatLoginLog selectKgWechatLoginLogById(Long logId);

    /**
     * 查询微信登录日志列表
     * 
     * @param kgWechatLoginLog 微信登录日志
     * @return 微信登录日志集合
     */
    public List<KgWechatLoginLog> selectKgWechatLoginLogList(KgWechatLoginLog kgWechatLoginLog);

    /**
     * 新增微信登录日志
     * 
     * @param kgWechatLoginLog 微信登录日志
     * @return 结果
     */
    public int insertKgWechatLoginLog(KgWechatLoginLog kgWechatLoginLog);

    /**
     * 修改微信登录日志
     * 
     * @param kgWechatLoginLog 微信登录日志
     * @return 结果
     */
    public int updateKgWechatLoginLog(KgWechatLoginLog kgWechatLoginLog);

    /**
     * 删除微信登录日志
     * 
     * @param logId 微信登录日志ID
     * @return 结果
     */
    public int deleteKgWechatLoginLogById(Long logId);

    /**
     * 批量删除微信登录日志
     * 
     * @param logIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteKgWechatLoginLogByIds(Long[] logIds);
}

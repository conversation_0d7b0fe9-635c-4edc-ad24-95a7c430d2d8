package com.cl.project.business.mapper;

import java.util.List;
import com.cl.project.business.domain.KgExpenseType;

/**
 * 支出类型Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface KgExpenseTypeMapper 
{
    /**
     * 查询支出类型
     * 
     * @param typeId 支出类型ID
     * @return 支出类型
     */
    public KgExpenseType selectKgExpenseTypeById(Long typeId);

    /**
     * 查询支出类型列表
     * 
     * @param kgExpenseType 支出类型
     * @return 支出类型集合
     */
    public List<KgExpenseType> selectKgExpenseTypeList(KgExpenseType kgExpenseType);

    /**
     * 新增支出类型
     * 
     * @param kgExpenseType 支出类型
     * @return 结果
     */
    public int insertKgExpenseType(KgExpenseType kgExpenseType);

    /**
     * 修改支出类型
     * 
     * @param kgExpenseType 支出类型
     * @return 结果
     */
    public int updateKgExpenseType(KgExpenseType kgExpenseType);

    /**
     * 删除支出类型
     * 
     * @param typeId 支出类型ID
     * @return 结果
     */
    public int deleteKgExpenseTypeById(Long typeId);

    /**
     * 批量删除支出类型
     * 
     * @param typeIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteKgExpenseTypeByIds(Long[] typeIds);
}

package com.cl.project.business.mapper;

import java.util.List;
import com.cl.project.business.domain.KgIncome;

/**
 * 收入记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface KgIncomeMapper 
{
    /**
     * 查询收入记录
     * 
     * @param incomeId 收入记录ID
     * @return 收入记录
     */
    public KgIncome selectKgIncomeById(Long incomeId);

    /**
     * 查询收入记录列表
     * 
     * @param kgIncome 收入记录
     * @return 收入记录集合
     */
    public List<KgIncome> selectKgIncomeList(KgIncome kgIncome);

    /**
     * 新增收入记录
     * 
     * @param kgIncome 收入记录
     * @return 结果
     */
    public int insertKgIncome(KgIncome kgIncome);

    /**
     * 修改收入记录
     * 
     * @param kgIncome 收入记录
     * @return 结果
     */
    public int updateKgIncome(KgIncome kgIncome);

    /**
     * 删除收入记录
     * 
     * @param incomeId 收入记录ID
     * @return 结果
     */
    public int deleteKgIncomeById(Long incomeId);

    /**
     * 批量删除收入记录
     * 
     * @param incomeIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteKgIncomeByIds(Long[] incomeIds);
}

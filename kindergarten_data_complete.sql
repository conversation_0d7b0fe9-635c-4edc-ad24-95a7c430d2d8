-- =====================================================
-- 幼儿园管理系统完整初始数据
-- 包含: 角色权限、菜单权限、基础配置数据
-- =====================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. 角色数据
-- =====================================================

-- 幼儿园专用角色数据
INSERT INTO `sys_role` VALUES 
(10, '园长', 'kg_director', 10, '1', '0', '0', 'admin', NOW(), 'admin', NOW(), '幼儿园园长，拥有全部管理权限', NULL),
(11, '教务主任', 'kg_academic_director', 11, '3', '0', '0', 'admin', NOW(), 'admin', NOW(), '教务主任，负责教学管理', NULL),
(12, '班主任', 'kg_head_teacher', 12, '4', '0', '0', 'admin', NOW(), 'admin', NOW(), '班主任，管理本班级学生', NULL),
(13, '副班主任', 'kg_assistant_teacher', 13, '4', '0', '0', 'admin', NOW(), 'admin', NOW(), '副班主任，协助班级管理', NULL),
(14, '托管教师', 'kg_course_teacher', 14, '5', '0', '0', 'admin', NOW(), 'admin', NOW(), '托管课程教师', NULL),
(15, '财务', 'kg_finance', 15, '3', '0', '0', 'admin', NOW(), 'admin', NOW(), '财务人员', NULL),
(16, '保健医', 'kg_health_teacher', 16, '5', '0', '0', 'admin', NOW(), 'admin', NOW(), '保健医生', NULL);

-- =====================================================
-- 2. 菜单权限数据
-- =====================================================

-- 幼儿园移动端菜单权限
INSERT INTO `sys_menu` VALUES 
-- 主菜单
(3000, '幼儿园管理', 0, 1, 'kindergarten', NULL, 1, 'M', '0', '0', '', 'kindergarten', 'admin', NOW(), 'admin', NOW(), '幼儿园管理系统', 0),

-- 考勤管理
(3100, '考勤管理', 3000, 10, 'attendance', NULL, 1, 'M', '0', '0', '', 'attendance', 'admin', NOW(), 'admin', NOW(), '考勤管理', 0),
(3101, '学生考勤', 3100, 1, 'student-attendance', 'kg/attendance/student', 1, 'C', '0', '0', 'kg:attendance:student:list', 'student', 'admin', NOW(), 'admin', NOW(), '学生考勤', 0),
(3102, '教师考勤', 3100, 2, 'teacher-attendance', 'kg/attendance/teacher', 1, 'C', '0', '0', 'kg:attendance:teacher:list', 'teacher', 'admin', NOW(), 'admin', NOW(), '教师考勤', 0),

-- 托管管理
(3200, '托管管理', 3000, 20, 'course', NULL, 1, 'M', '0', '0', '', 'course', 'admin', NOW(), 'admin', NOW(), '托管管理', 0),
(3201, '课程管理', 3200, 1, 'course-manage', 'kg/course/manage', 1, 'C', '0', '0', 'kg:course:manage:list', 'course', 'admin', NOW(), 'admin', NOW(), '课程管理', 0),
(3202, '托管考勤', 3200, 2, 'course-attendance', 'kg/course/attendance', 1, 'C', '0', '0', 'kg:course:attendance:list', 'attendance', 'admin', NOW(), 'admin', NOW(), '托管考勤', 0),

-- 费用管理
(3300, '费用管理', 3000, 30, 'finance', NULL, 1, 'M', '0', '0', '', 'finance', 'admin', NOW(), 'admin', NOW(), '费用管理', 0),
(3301, '园费管理', 3300, 1, 'tuition', 'kg/finance/tuition', 1, 'C', '0', '0', 'kg:finance:tuition:list', 'tuition', 'admin', NOW(), 'admin', NOW(), '园费管理', 0),
(3302, '托管费管理', 3300, 2, 'course-fee', 'kg/finance/course-fee', 1, 'C', '0', '0', 'kg:finance:course:list', 'course-fee', 'admin', NOW(), 'admin', NOW(), '托管费管理', 0),

-- 学生管理
(3400, '学生管理', 3000, 40, 'student', NULL, 1, 'M', '0', '0', '', 'student', 'admin', NOW(), 'admin', NOW(), '学生管理', 0),
(3401, '学生信息', 3400, 1, 'student-info', 'kg/student/info', 1, 'C', '0', '0', 'kg:student:info:list', 'student-info', 'admin', NOW(), 'admin', NOW(), '学生信息', 0),
(3402, '班级管理', 3400, 2, 'class-manage', 'kg/student/class', 1, 'C', '0', '0', 'kg:student:class:list', 'class', 'admin', NOW(), 'admin', NOW(), '班级管理', 0),

-- 工资管理
(3500, '工资管理', 3000, 50, 'salary', NULL, 1, 'M', '0', '0', '', 'salary', 'admin', NOW(), 'admin', NOW(), '工资管理', 0),
(3501, '工资查询', 3500, 1, 'salary-query', 'kg/salary/query', 1, 'C', '0', '0', 'kg:salary:query:list', 'salary-query', 'admin', NOW(), 'admin', NOW(), '工资查询', 0),
(3502, '工资计算', 3500, 2, 'salary-calculate', 'kg/salary/calculate', 1, 'C', '0', '0', 'kg:salary:calculate:list', 'salary-calculate', 'admin', NOW(), 'admin', NOW(), '工资计算', 0),

-- 库存管理
(3600, '库存管理', 3000, 60, 'inventory', NULL, 1, 'M', '0', '0', '', 'inventory', 'admin', NOW(), 'admin', NOW(), '库存管理', 0),
(3601, '物品管理', 3600, 1, 'item-manage', 'kg/inventory/item', 1, 'C', '0', '0', 'kg:inventory:item:list', 'item', 'admin', NOW(), 'admin', NOW(), '物品管理', 0),
(3602, '出入库', 3600, 2, 'stock-record', 'kg/inventory/record', 1, 'C', '0', '0', 'kg:inventory:record:list', 'record', 'admin', NOW(), 'admin', NOW(), '出入库管理', 0),

-- 统计报表
(3700, '统计报表', 3000, 70, 'report', NULL, 1, 'M', '0', '0', '', 'report', 'admin', NOW(), 'admin', NOW(), '统计报表', 0),
(3701, '考勤统计', 3700, 1, 'attendance-report', 'kg/report/attendance', 1, 'C', '0', '0', 'kg:report:attendance:list', 'attendance-report', 'admin', NOW(), 'admin', NOW(), '考勤统计', 0),
(3702, '财务报表', 3700, 2, 'finance-report', 'kg/report/finance', 1, 'C', '0', '0', 'kg:report:finance:list', 'finance-report', 'admin', NOW(), 'admin', NOW(), '财务报表', 0);

-- 功能权限按钮
INSERT INTO `sys_menu` VALUES 
-- 学生考勤权限
(3111, '学生签到', 3101, 1, '', '', 1, 'F', '0', '0', 'kg:attendance:student:checkin', '#', 'admin', NOW(), 'admin', NOW(), '学生签到', 0),
(3112, '学生签退', 3101, 2, '', '', 1, 'F', '0', '0', 'kg:attendance:student:checkout', '#', 'admin', NOW(), 'admin', NOW(), '学生签退', 0),
(3113, '考勤确认', 3101, 3, '', '', 1, 'F', '0', '0', 'kg:attendance:student:confirm', '#', 'admin', NOW(), 'admin', NOW(), '考勤确认', 0),
(3114, '缺勤登记', 3101, 4, '', '', 1, 'F', '0', '0', 'kg:attendance:student:absence', '#', 'admin', NOW(), 'admin', NOW(), '缺勤登记', 0),
(3115, '考勤查询', 3101, 5, '', '', 1, 'F', '0', '0', 'kg:attendance:student:query', '#', 'admin', NOW(), 'admin', NOW(), '考勤查询', 0),

-- 教师考勤权限
(3121, '教师签到', 3102, 1, '', '', 1, 'F', '0', '0', 'kg:attendance:teacher:checkin', '#', 'admin', NOW(), 'admin', NOW(), '教师签到', 0),
(3122, '教师签退', 3102, 2, '', '', 1, 'F', '0', '0', 'kg:attendance:teacher:checkout', '#', 'admin', NOW(), 'admin', NOW(), '教师签退', 0),
(3123, '教师考勤查询', 3102, 3, '', '', 1, 'F', '0', '0', 'kg:attendance:teacher:query', '#', 'admin', NOW(), 'admin', NOW(), '教师考勤查询', 0),

-- 托管考勤权限
(3211, '托管签到', 3202, 1, '', '', 1, 'F', '0', '0', 'kg:course:attendance:checkin', '#', 'admin', NOW(), 'admin', NOW(), '托管签到', 0),
(3212, '托管确认', 3202, 2, '', '', 1, 'F', '0', '0', 'kg:course:attendance:confirm', '#', 'admin', NOW(), 'admin', NOW(), '托管确认', 0),
(3213, '托管查询', 3202, 3, '', '', 1, 'F', '0', '0', 'kg:course:attendance:query', '#', 'admin', NOW(), 'admin', NOW(), '托管查询', 0),

-- 课程管理权限
(3221, '课程新增', 3201, 1, '', '', 1, 'F', '0', '0', 'kg:course:manage:add', '#', 'admin', NOW(), 'admin', NOW(), '课程新增', 0),
(3222, '课程修改', 3201, 2, '', '', 1, 'F', '0', '0', 'kg:course:manage:edit', '#', 'admin', NOW(), 'admin', NOW(), '课程修改', 0),
(3223, '课程删除', 3201, 3, '', '', 1, 'F', '0', '0', 'kg:course:manage:remove', '#', 'admin', NOW(), 'admin', NOW(), '课程删除', 0),

-- 费用查看权限
(3311, '查看园费', 3301, 1, '', '', 1, 'F', '0', '0', 'kg:finance:tuition:view', '#', 'admin', NOW(), 'admin', NOW(), '查看园费', 0),
(3312, '发送账单', 3301, 2, '', '', 1, 'F', '0', '0', 'kg:finance:tuition:send', '#', 'admin', NOW(), 'admin', NOW(), '发送账单', 0),
(3313, '园费计算', 3301, 3, '', '', 1, 'F', '0', '0', 'kg:finance:tuition:calculate', '#', 'admin', NOW(), 'admin', NOW(), '园费计算', 0),

(3321, '查看托管费', 3302, 1, '', '', 1, 'F', '0', '0', 'kg:finance:course:view', '#', 'admin', NOW(), 'admin', NOW(), '查看托管费', 0),
(3322, '发送托管账单', 3302, 2, '', '', 1, 'F', '0', '0', 'kg:finance:course:send', '#', 'admin', NOW(), 'admin', NOW(), '发送托管账单', 0),
(3323, '托管费计算', 3302, 3, '', '', 1, 'F', '0', '0', 'kg:finance:course:calculate', '#', 'admin', NOW(), 'admin', NOW(), '托管费计算', 0),

-- 学生管理权限
(3411, '学生新增', 3401, 1, '', '', 1, 'F', '0', '0', 'kg:student:info:add', '#', 'admin', NOW(), 'admin', NOW(), '学生新增', 0),
(3412, '学生修改', 3401, 2, '', '', 1, 'F', '0', '0', 'kg:student:info:edit', '#', 'admin', NOW(), 'admin', NOW(), '学生修改', 0),
(3413, '学生删除', 3401, 3, '', '', 1, 'F', '0', '0', 'kg:student:info:remove', '#', 'admin', NOW(), 'admin', NOW(), '学生删除', 0),

-- 班级管理权限
(3421, '班级新增', 3402, 1, '', '', 1, 'F', '0', '0', 'kg:student:class:add', '#', 'admin', NOW(), 'admin', NOW(), '班级新增', 0),
(3422, '班级修改', 3402, 2, '', '', 1, 'F', '0', '0', 'kg:student:class:edit', '#', 'admin', NOW(), 'admin', NOW(), '班级修改', 0),
(3423, '班级删除', 3402, 3, '', '', 1, 'F', '0', '0', 'kg:student:class:remove', '#', 'admin', NOW(), 'admin', NOW(), '班级删除', 0),

-- 工资管理权限
(3511, '工资确认', 3502, 1, '', '', 1, 'F', '0', '0', 'kg:salary:calculate:confirm', '#', 'admin', NOW(), 'admin', NOW(), '工资确认', 0),
(3512, '工资发放', 3502, 2, '', '', 1, 'F', '0', '0', 'kg:salary:calculate:pay', '#', 'admin', NOW(), 'admin', NOW(), '工资发放', 0),

-- 库存管理权限
(3611, '物品新增', 3601, 1, '', '', 1, 'F', '0', '0', 'kg:inventory:item:add', '#', 'admin', NOW(), 'admin', NOW(), '物品新增', 0),
(3612, '物品修改', 3601, 2, '', '', 1, 'F', '0', '0', 'kg:inventory:item:edit', '#', 'admin', NOW(), 'admin', NOW(), '物品修改', 0),
(3613, '物品删除', 3601, 3, '', '', 1, 'F', '0', '0', 'kg:inventory:item:remove', '#', 'admin', NOW(), 'admin', NOW(), '物品删除', 0),

(3621, '入库操作', 3602, 1, '', '', 1, 'F', '0', '0', 'kg:inventory:record:in', '#', 'admin', NOW(), 'admin', NOW(), '入库操作', 0),
(3622, '出库操作', 3602, 2, '', '', 1, 'F', '0', '0', 'kg:inventory:record:out', '#', 'admin', NOW(), 'admin', NOW(), '出库操作', 0),
(3623, '库存调整', 3602, 3, '', '', 1, 'F', '0', '0', 'kg:inventory:record:adjust', '#', 'admin', NOW(), 'admin', NOW(), '库存调整', 0);

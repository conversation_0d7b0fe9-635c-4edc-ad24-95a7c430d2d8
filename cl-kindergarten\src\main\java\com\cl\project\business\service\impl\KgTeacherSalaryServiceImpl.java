package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgTeacherSalaryMapper;
import com.cl.project.business.domain.KgTeacherSalary;
import com.cl.project.business.service.IKgTeacherSalaryService;

/**
 * 教师工资Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgTeacherSalaryServiceImpl implements IKgTeacherSalaryService 
{
    @Autowired
    private KgTeacherSalaryMapper kgTeacherSalaryMapper;

    /**
     * 查询教师工资
     * 
     * @param salaryId 教师工资ID
     * @return 教师工资
     */
    @Override
    public KgTeacherSalary selectKgTeacherSalaryById(Long salaryId)
    {
        return kgTeacherSalaryMapper.selectKgTeacherSalaryById(salaryId);
    }

    /**
     * 查询教师工资列表
     * 
     * @param kgTeacherSalary 教师工资
     * @return 教师工资
     */
    @Override
    public List<KgTeacherSalary> selectKgTeacherSalaryList(KgTeacherSalary kgTeacherSalary)
    {
        return kgTeacherSalaryMapper.selectKgTeacherSalaryList(kgTeacherSalary);
    }

    /**
     * 新增教师工资
     * 
     * @param kgTeacherSalary 教师工资
     * @return 结果
     */
    @Override
    public int insertKgTeacherSalary(KgTeacherSalary kgTeacherSalary)
    {
        kgTeacherSalary.setCreateTime(DateUtils.getNowDate());
        return kgTeacherSalaryMapper.insertKgTeacherSalary(kgTeacherSalary);
    }

    /**
     * 修改教师工资
     * 
     * @param kgTeacherSalary 教师工资
     * @return 结果
     */
    @Override
    public int updateKgTeacherSalary(KgTeacherSalary kgTeacherSalary)
    {
        kgTeacherSalary.setUpdateTime(DateUtils.getNowDate());
        return kgTeacherSalaryMapper.updateKgTeacherSalary(kgTeacherSalary);
    }

    /**
     * 批量删除教师工资
     * 
     * @param salaryIds 需要删除的教师工资ID
     * @return 结果
     */
    @Override
    public int deleteKgTeacherSalaryByIds(Long[] salaryIds)
    {
        return kgTeacherSalaryMapper.deleteKgTeacherSalaryByIds(salaryIds);
    }

    /**
     * 删除教师工资信息
     * 
     * @param salaryId 教师工资ID
     * @return 结果
     */
    @Override
    public int deleteKgTeacherSalaryById(Long salaryId)
    {
        return kgTeacherSalaryMapper.deleteKgTeacherSalaryById(salaryId);
    }
}

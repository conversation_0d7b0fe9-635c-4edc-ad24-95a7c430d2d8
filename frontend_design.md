# 幼儿园管理系统后台页面设计方案

## 一、整体架构设计

### 1.1 主导航菜单结构
```
├── 首页仪表板 (Dashboard)
├── 基础信息管理 (Basic Info)
│   ├── 学生管理 (Students)
│   ├── 教师管理 (Teachers) 
│   ├── 班级管理 (Classes)
│   └── 家长管理 (Parents)
├── 考勤管理 (Attendance)
│   ├── 学生考勤 (Student Attendance)
│   ├── 教师考勤 (Teacher Attendance)
│   ├── 考勤统计 (Statistics)
│   └── 考勤设置 (Settings)
├── 托管课程 (Courses)
│   ├── 课程管理 (Course Management)
│   ├── 课程报名 (Enrollment)
│   ├── 课程考勤 (Course Attendance)
│   └── 课程统计 (Course Statistics)
├── 财务管理 (Finance)
│   ├── 园费管理 (Tuition)
│   ├── 托管费用 (Course Fees)
│   ├── 收支管理 (Income/Expense)
│   ├── 工资管理 (Salary)
│   └── 财务报表 (Reports)
├── 家校互动 (Communication)
│   ├── 消息推送 (Messages)
│   ├── 微信管理 (WeChat)
│   ├── 通知公告 (Notices)
│   └── 家长反馈 (Feedback)
├── 库存管理 (Inventory)
│   ├── 物品管理 (Items)
│   ├── 入库出库 (Stock Records)
│   └── 库存统计 (Statistics)
└── 系统管理 (System)
    ├── 用户管理 (Users)
    ├── 权限管理 (Permissions)
    ├── 系统配置 (Settings)
    └── 数据备份 (Backup)
```

## 二、核心页面详细设计

### 2.1 首页仪表板 (/dashboard)
**功能**: 数据概览、快捷操作、重要提醒
**组件**:
- 数据卡片: 在园学生数、教师数、今日考勤率、本月收入
- 图表: 考勤趋势图、收入趋势图、班级分布饼图
- 待办事项: 待确认考勤、待处理请假、欠费提醒
- 快捷操作: 学生考勤、消息推送、数据导出

### 2.2 基础信息管理

#### 2.2.1 学生管理 (/kg/student)
**页面**: index.vue, form.vue, detail.vue
**功能**: 学生信息CRUD、批量导入、照片管理、家长绑定
**字段**: 学生编号、姓名、性别、出生日期、班级、家长信息、入园日期、状态
**操作**: 新增、编辑、删除、查看详情、批量操作、导出Excel

#### 2.2.2 教师管理 (/kg/teacher)  
**页面**: index.vue, form.vue, detail.vue, salary.vue
**功能**: 教师信息管理、工资设置、权限分配、微信绑定
**字段**: 教师编号、姓名、职位、学历、入职日期、基本工资、联系方式
**操作**: 新增、编辑、删除、工资设置、权限配置、微信绑定

#### 2.2.3 班级管理 (/kg/class)
**页面**: index.vue, form.vue, students.vue
**功能**: 班级信息管理、学生分配、教师分配
**字段**: 班级名称、类型、容量、当前人数、班主任、副班主任
**操作**: 新增班级、分配学生、分配教师、班级统计

### 2.3 考勤管理

#### 2.3.1 学生考勤 (/kg/attendance/student)
**页面**: index.vue, checkin.vue, statistics.vue, calendar.vue
**功能**: 考勤记录、批量签到、考勤统计、日历视图
**字段**: 学生、班级、考勤日期、签到时间、签退时间、考勤状态、确认状态
**操作**: 手动签到、批量操作、考勤确认、导出报表

#### 2.3.2 教师考勤 (/kg/attendance/teacher)
**页面**: index.vue, checkin.vue, statistics.vue
**功能**: 教师考勤记录、工时统计、考勤分析
**字段**: 教师、考勤日期、签到时间、签退时间、工作时长、考勤状态
**操作**: 考勤记录、工时计算、月度统计

### 2.4 托管课程

#### 2.4.1 课程管理 (/kg/course)
**页面**: index.vue, form.vue, schedule.vue
**功能**: 课程信息管理、课程安排、价格设置
**字段**: 课程名称、类型、价格、时长、最少/最大人数、状态
**操作**: 新增课程、编辑课程、课程安排、价格调整

#### 2.4.2 课程报名 (/kg/course/enrollment)
**页面**: index.vue, form.vue, batch.vue
**功能**: 学生报名管理、批量报名、报名统计
**字段**: 学生、课程、报名日期、报名状态、费用
**操作**: 学生报名、批量报名、报名确认、退课处理

### 2.5 财务管理

#### 2.5.1 园费管理 (/kg/finance/tuition)
**页面**: index.vue, config.vue, bill.vue, payment.vue
**功能**: 园费配置、账单生成、缴费记录、欠费管理
**字段**: 班级类型、园费标准、账单月份、应缴金额、实缴金额、缴费状态
**操作**: 费用配置、生成账单、记录缴费、欠费提醒

#### 2.5.2 工资管理 (/kg/finance/salary)
**页面**: index.vue, calculate.vue, payslip.vue, statistics.vue
**功能**: 工资计算、工资条生成、工资统计、工资发放
**字段**: 教师、工资月份、基本工资、满勤奖、课时费、社保扣款、实发工资
**操作**: 工资计算、生成工资条、工资发放、统计分析

### 2.6 家校互动

#### 2.6.1 消息推送 (/kg/communication/message)
**页面**: index.vue, send.vue, template.vue
**功能**: 消息推送、模板管理、推送记录
**字段**: 推送对象、消息类型、消息内容、推送时间、推送状态
**操作**: 发送消息、批量推送、模板管理、推送统计

#### 2.6.2 微信管理 (/kg/communication/wechat)
**页面**: index.vue, bind.vue, users.vue
**功能**: 微信用户管理、绑定管理、登录记录
**字段**: 微信昵称、openid、绑定用户、绑定时间、登录记录
**操作**: 用户绑定、解除绑定、登录统计

## 三、页面布局规范

### 3.1 列表页面布局 (index.vue)
```vue
<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true">
      <el-form-item label="关键字" prop="keyword">
        <el-input v-model="queryParams.keyword" placeholder="请输入关键字" />
      </el-form-item>
      <el-form-item>
        <el-button type="primary" @click="handleQuery">搜索</el-button>
        <el-button @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区域 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button type="primary" @click="handleAdd">新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button type="success" @click="handleExport">导出</el-button>
      </el-col>
    </el-row>

    <!-- 数据表格 -->
    <el-table v-loading="loading" :data="dataList">
      <!-- 表格列定义 -->
    </el-table>

    <!-- 分页组件 -->
    <pagination v-show="total>0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize" @pagination="getList" />

    <!-- 添加或修改对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="600px">
      <!-- 表单内容 -->
    </el-dialog>
  </div>
</template>
```

### 3.2 表单页面布局 (form.vue)
```vue
<template>
  <div class="app-container">
    <el-form ref="form" :model="form" :rules="rules" label-width="120px">
      <el-row>
        <el-col :span="12">
          <el-form-item label="字段名" prop="fieldName">
            <el-input v-model="form.fieldName" placeholder="请输入" />
          </el-form-item>
        </el-col>
      </el-row>
      
      <el-form-item>
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </el-form-item>
    </el-form>
  </div>
</template>
```

## 四、实施建议

### 4.1 开发优先级
1. **第一阶段**: 完善基础信息管理(学生、教师、班级)
2. **第二阶段**: 考勤管理功能完善
3. **第三阶段**: 财务管理模块
4. **第四阶段**: 家校互动功能
5. **第五阶段**: 系统管理和报表功能

### 4.2 技术要求
- 使用Vue 3 + Element Plus
- 响应式设计，支持移动端
- 统一的API接口规范
- 完善的权限控制
- 数据校验和错误处理

### 4.3 用户体验优化
- 统一的操作流程和交互方式
- 清晰的数据展示和筛选功能
- 便捷的批量操作功能
- 完善的提示和帮助信息

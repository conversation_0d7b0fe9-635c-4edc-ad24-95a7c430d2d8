// 管理员端JavaScript功能

class AdminPage {
    constructor() {
        this.currentUser = Auth.getCurrentUser();
        this.currentPeriod = 'month';
        this.init();
    }

    init() {
        this.loadUserInfo();
        this.loadFinanceData();
        this.bindEvents();
    }

    // 加载用户信息
    loadUserInfo() {
        if (this.currentUser) {
            const adminNameEl = document.getElementById('adminName');
            const adminPositionEl = document.getElementById('adminPosition');
            
            if (adminNameEl) adminNameEl.textContent = this.currentUser.name;
            if (adminPositionEl) adminPositionEl.textContent = this.currentUser.position;
        }
    }

    // 加载财务数据
    async loadFinanceData() {
        try {
            // 更新时间显示
            this.updatePeriodDisplay();
            
            // 加载财务概览
            await this.loadFinanceOverview();
            
            // 加载缴费统计
            await this.loadPaymentStats();
            
            // 加载最新动态
            await this.loadRecentActivities();
            
        } catch (error) {
            console.error('加载财务数据失败:', error);
            Toast.error('数据加载失败，请刷新重试');
        }
    }

    // 更新时间显示
    updatePeriodDisplay() {
        const currentPeriodEl = document.getElementById('currentPeriod');
        if (currentPeriodEl) {
            const now = new Date();
            let periodText = '';
            
            switch (this.currentPeriod) {
                case 'month':
                    periodText = `${now.getFullYear()}年${now.getMonth() + 1}月`;
                    break;
                case 'quarter':
                    const quarter = Math.floor(now.getMonth() / 3) + 1;
                    periodText = `${now.getFullYear()}年第${quarter}季度`;
                    break;
                case 'year':
                    periodText = `${now.getFullYear()}年`;
                    break;
            }
            
            currentPeriodEl.textContent = periodText;
        }
    }

    // 加载财务概览
    async loadFinanceOverview() {
        // 模拟API调用
        const financeData = {
            income: {
                total: 156800,
                trend: 12.5,
                details: {
                    tuition: 98400,
                    course: 45600,
                    other: 12800
                }
            },
            expense: {
                total: 89200,
                trend: -5.2,
                details: {
                    salary: 52000,
                    meal: 18500,
                    operation: 18700
                }
            },
            profit: {
                total: 67600,
                rate: 43.1,
                trend: 18.3
            }
        };

        // 更新收入卡片
        this.updateFinanceCard('income', financeData.income);
        
        // 更新支出卡片
        this.updateFinanceCard('expense', financeData.expense);
        
        // 更新利润卡片
        this.updateFinanceCard('profit', financeData.profit);
    }

    // 更新财务卡片
    updateFinanceCard(type, data) {
        const card = document.querySelector(`.overview-card.${type}`);
        if (!card) return;

        const amountEl = card.querySelector('.card-amount');
        const trendEl = card.querySelector('.trend');
        const detailEl = card.querySelector('.card-detail');

        if (amountEl) {
            if (type === 'profit') {
                amountEl.textContent = Utils.formatMoney(data.total);
            } else {
                amountEl.textContent = Utils.formatMoney(data.total);
            }
        }

        if (trendEl && data.trend !== undefined) {
            const isPositive = data.trend > 0;
            trendEl.textContent = `${isPositive ? '📈' : '📉'} ${isPositive ? '+' : ''}${data.trend}%`;
            trendEl.className = `trend ${isPositive ? 'up' : 'down'}`;
        }

        if (detailEl && data.details) {
            const details = Object.entries(data.details);
            detailEl.innerHTML = details.map(([key, value]) => {
                const labels = {
                    tuition: '园费',
                    course: '托管费',
                    other: '其他',
                    salary: '工资',
                    meal: '餐费',
                    operation: '运营'
                };
                return `<span>${labels[key]}: ${Utils.formatMoney(value)}</span>`;
            }).join('');
        }

        // 特殊处理利润卡片
        if (type === 'profit' && detailEl) {
            detailEl.innerHTML = `
                <span>利润率: ${data.rate}%</span>
                <span>环比: +${data.trend}%</span>
            `;
        }
    }

    // 加载缴费统计
    async loadPaymentStats() {
        // 模拟API调用
        const paymentData = [
            {
                className: '小班',
                totalStudents: 40,
                paidStudents: 38,
                paymentRate: 95,
                totalAmount: 59280
            },
            {
                className: '中班',
                totalStudents: 38,
                paidStudents: 35,
                paymentRate: 92,
                totalAmount: 54600
            },
            {
                className: '大班',
                totalStudents: 36,
                paidStudents: 32,
                paymentRate: 88,
                totalAmount: 49920
            }
        ];

        // 更新缴费统计显示
        const statsItems = document.querySelectorAll('.stats-item');
        paymentData.forEach((data, index) => {
            if (statsItems[index]) {
                this.updatePaymentStatsItem(statsItems[index], data);
            }
        });
    }

    // 更新缴费统计项
    updatePaymentStatsItem(item, data) {
        const classNameEl = item.querySelector('.class-info h4');
        const totalStudentsEl = item.querySelector('.class-info p');
        const progressFillEl = item.querySelector('.progress-fill');
        const progressTextEl = item.querySelector('.progress-text');
        const amountEl = item.querySelector('.payment-amount');

        if (classNameEl) classNameEl.textContent = data.className;
        if (totalStudentsEl) totalStudentsEl.textContent = `${data.totalStudents}人`;
        if (progressFillEl) progressFillEl.style.width = `${data.paymentRate}%`;
        if (progressTextEl) {
            progressTextEl.textContent = `${data.paymentRate}% (${data.paidStudents}/${data.totalStudents})`;
        }
        if (amountEl) amountEl.textContent = Utils.formatMoney(data.totalAmount);
    }

    // 加载最新动态
    async loadRecentActivities() {
        // 模拟API调用
        const activities = [
            {
                icon: '💰',
                title: '张小明家长缴费',
                description: '园费 ¥1,560 - 刚刚'
            },
            {
                icon: '📋',
                title: '11月账单已生成',
                description: '共114份账单 - 5分钟前'
            },
            {
                icon: '👩‍🏫',
                title: '李老师提交考勤',
                description: '小班考勤已确认 - 10分钟前'
            },
            {
                icon: '📊',
                title: '月度报表生成',
                description: '财务报表已更新 - 1小时前'
            }
        ];

        // 这里可以动态生成活动列表
        // 当前使用静态HTML，实际项目中可以动态渲染
    }

    // 绑定事件
    bindEvents() {
        // 时间筛选标签
        const filterTabs = document.querySelectorAll('.filter-tab');
        filterTabs.forEach(tab => {
            tab.addEventListener('click', (e) => {
                this.switchPeriod(e.target.dataset.period);
            });
        });

        // 头部操作按钮
        const headerBtns = document.querySelectorAll('.header-btn');
        headerBtns.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const btnText = e.target.textContent;
                if (btnText === '🔔') {
                    this.showNotifications();
                } else if (btnText === '⚙️') {
                    this.showSettings();
                }
            });
        });

        // 快捷操作
        const actionItems = document.querySelectorAll('.action-item');
        actionItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                const actionText = item.querySelector('span').textContent;
                this.handleQuickAction(actionText);
            });
        });
    }

    // 切换时间周期
    async switchPeriod(period) {
        if (period === this.currentPeriod) return;

        // 更新标签状态
        document.querySelectorAll('.filter-tab').forEach(tab => {
            tab.classList.remove('active');
        });
        document.querySelector(`[data-period="${period}"]`).classList.add('active');

        // 更新当前周期
        this.currentPeriod = period;

        // 重新加载数据
        Toast.info('正在切换数据...');
        await this.loadFinanceData();
        Toast.success('数据已更新');
    }

    // 显示通知
    showNotifications() {
        Toast.info('通知功能开发中...');
        // 这里可以显示通知列表弹窗
    }

    // 显示设置
    showSettings() {
        Toast.info('设置功能开发中...');
        // 这里可以跳转到设置页面
    }

    // 处理快捷操作
    handleQuickAction(actionText) {
        switch (actionText) {
            case '财务管理':
                Toast.info('跳转到财务管理页面...');
                // window.location.href = 'finance.html';
                break;
            case '账单管理':
                Toast.info('跳转到账单管理页面...');
                // window.location.href = 'bills.html';
                break;
            case '数据报表':
                Toast.info('跳转到数据报表页面...');
                // window.location.href = 'reports.html';
                break;
            case '系统设置':
                Toast.info('跳转到系统设置页面...');
                // window.location.href = 'settings.html';
                break;
            default:
                Toast.info(`${actionText}功能开发中...`);
        }
    }

    // 导出数据
    async exportData() {
        Toast.info('正在导出数据...');
        
        try {
            // 模拟导出过程
            await new Promise(resolve => setTimeout(resolve, 2000));
            Toast.success('数据导出成功');
        } catch (error) {
            Toast.error('数据导出失败');
        }
    }

    // 生成报表
    async generateReport() {
        Toast.info('正在生成报表...');
        
        try {
            // 模拟报表生成过程
            await new Promise(resolve => setTimeout(resolve, 3000));
            Toast.success('报表生成成功');
        } catch (error) {
            Toast.error('报表生成失败');
        }
    }

    // 刷新数据
    async refreshData() {
        Toast.info('正在刷新数据...');
        await this.loadFinanceData();
        Toast.success('数据刷新成功');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查登录状态
    if (!Auth.isLoggedIn() || Auth.getCurrentRole() !== 'admin') {
        window.location.href = '/index.html';
        return;
    }
    
    new AdminPage();
});

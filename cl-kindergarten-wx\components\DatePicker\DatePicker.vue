<template>
	<u-popup v-model="show" mode="bottom" border-radius="24">
		<view class="date-picker-popup">
			<view class="picker-header">
				<text class="picker-cancel" @click="cancel">取消</text>
				<text class="picker-title">{{ title || '选择日期' }}</text>
				<text class="picker-confirm" @click="confirm">确定</text>
			</view>
			<picker-view class="date-picker-view" :value="pickerValue" @change="onChange">
				<picker-view-column>
					<view v-for="year in yearOptions" :key="year" class="picker-item">
						{{ year }}年
					</view>
				</picker-view-column>
				<picker-view-column>
					<view v-for="month in monthOptions" :key="month" class="picker-item">
						{{ month }}月
					</view>
				</picker-view-column>
				<picker-view-column>
					<view v-for="day in dayOptions" :key="day" class="picker-item">
						{{ day }}日
					</view>
				</picker-view-column>
			</picker-view>
		</view>
	</u-popup>
</template>

<script>
export default {
	name: 'DatePicker',
	props: {
		show: {
			type: Boolean,
			default: false
		},
		value: {
			type: [String, Number, Date],
			default: ''
		},
		title: {
			type: String,
			default: '选择日期'
		},
		minDate: {
			type: [String, Number, Date],
			default: () => {
				const date = new Date()
				date.setFullYear(date.getFullYear() - 10)
				return date
			}
		},
		maxDate: {
			type: [String, Number, Date],
			default: () => {
				const date = new Date()
				date.setFullYear(date.getFullYear() + 10)
				return date
			}
		}
	},
	data() {
		return {
			yearOptions: [],
			monthOptions: [],
			dayOptions: [],
			pickerValue: [0, 0, 0],
			tempValue: [0, 0, 0]
		}
	},
	watch: {
		show(newVal) {
			if (newVal) {
				this.initPicker()
			}
		},
		value: {
			handler() {
				this.initPicker()
			},
			immediate: true
		}
	},
	methods: {
		initPicker() {
			this.generateOptions()
			this.setCurrentValue()
		},
		
		generateOptions() {
			const minDate = new Date(this.minDate)
			const maxDate = new Date(this.maxDate)
			
			// 生成年份选项
			this.yearOptions = []
			for (let year = minDate.getFullYear(); year <= maxDate.getFullYear(); year++) {
				this.yearOptions.push(year)
			}
			
			// 生成月份选项
			this.monthOptions = []
			for (let month = 1; month <= 12; month++) {
				this.monthOptions.push(month)
			}
			
			// 生成日期选项（默认31天，会在onChange中动态调整）
			this.updateDayOptions(new Date().getFullYear(), 1)
		},
		
		updateDayOptions(year, month) {
			const daysInMonth = new Date(year, month, 0).getDate()
			this.dayOptions = []
			for (let day = 1; day <= daysInMonth; day++) {
				this.dayOptions.push(day)
			}
		},
		
		setCurrentValue() {
			let currentDate
			if (this.value) {
				currentDate = new Date(this.value)
			} else {
				currentDate = new Date()
			}
			
			const year = currentDate.getFullYear()
			const month = currentDate.getMonth() + 1
			const day = currentDate.getDate()
			
			const yearIndex = this.yearOptions.findIndex(y => y === year)
			const monthIndex = month - 1
			const dayIndex = day - 1
			
			this.pickerValue = [
				Math.max(0, yearIndex),
				Math.max(0, monthIndex),
				Math.max(0, dayIndex)
			]
			this.tempValue = [...this.pickerValue]
		},
		
		onChange(e) {
			this.tempValue = e.detail.value
			
			// 当年份或月份变化时，更新日期选项
			const year = this.yearOptions[this.tempValue[0]]
			const month = this.monthOptions[this.tempValue[1]]
			this.updateDayOptions(year, month)
			
			// 如果当前选中的日期超出了新月份的天数，调整到最后一天
			if (this.tempValue[2] >= this.dayOptions.length) {
				this.tempValue[2] = this.dayOptions.length - 1
			}
		},
		
		confirm() {
			const year = this.yearOptions[this.tempValue[0]]
			const month = this.monthOptions[this.tempValue[1]]
			const day = this.dayOptions[this.tempValue[2]]
			
			const selectedDate = new Date(year, month - 1, day)
			const formattedDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
			
			this.pickerValue = [...this.tempValue]
			
			this.$emit('confirm', {
				value: selectedDate.getTime(),
				date: selectedDate,
				formatted: formattedDate
			})
			this.$emit('input', selectedDate.getTime())
			this.$emit('update:show', false)
		},
		
		cancel() {
			this.tempValue = [...this.pickerValue]
			this.$emit('cancel')
			this.$emit('update:show', false)
		}
	}
}
</script>

<style lang="scss" scoped>
.date-picker-popup {
	background: #ffffff;
	border-radius: 24rpx 24rpx 0 0;
}

.picker-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.picker-cancel {
	color: #999999;
	font-size: 32rpx;
}

.picker-title {
	color: #333333;
	font-size: 34rpx;
	font-weight: 600;
}

.picker-confirm {
	color: #667eea;
	font-size: 32rpx;
	font-weight: 600;
}

.date-picker-view {
	height: 500rpx;
}

.picker-item {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100rpx;
	font-size: 32rpx;
	color: #333333;
}
</style>

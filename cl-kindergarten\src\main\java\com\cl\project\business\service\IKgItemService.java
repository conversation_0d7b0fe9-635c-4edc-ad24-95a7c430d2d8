package com.cl.project.business.service;

import java.util.List;
import com.cl.project.business.domain.KgItem;

/**
 * 物品信息Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IKgItemService 
{
    /**
     * 查询物品信息
     * 
     * @param itemId 物品信息ID
     * @return 物品信息
     */
    public KgItem selectKgItemById(Long itemId);

    /**
     * 查询物品信息列表
     * 
     * @param kgItem 物品信息
     * @return 物品信息集合
     */
    public List<KgItem> selectKgItemList(KgItem kgItem);

    /**
     * 新增物品信息
     * 
     * @param kgItem 物品信息
     * @return 结果
     */
    public int insertKgItem(KgItem kgItem);

    /**
     * 修改物品信息
     * 
     * @param kgItem 物品信息
     * @return 结果
     */
    public int updateKgItem(KgItem kgItem);

    /**
     * 批量删除物品信息
     * 
     * @param itemIds 需要删除的物品信息ID
     * @return 结果
     */
    public int deleteKgItemByIds(Long[] itemIds);

    /**
     * 删除物品信息信息
     * 
     * @param itemId 物品信息ID
     * @return 结果
     */
    public int deleteKgItemById(Long itemId);
}

package com.cl.project.business.service;

import com.cl.project.business.domain.dto.*;

import java.util.List;

/**
 * 钉钉API服务接口
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
public interface IDingtalkApiService 
{
    /**
     * 获取钉钉访问令牌
     * 
     * @return 访问令牌
     */
    String getAccessToken();
    
    /**
     * 获取部门用户ID列表
     * 
     * @param deptId 部门ID，1为根部门
     * @return 用户ID列表
     */
    List<String> getUserIdList(Long deptId);
    
    /**
     * 获取用户详细信息
     * 
     * @param userId 用户ID
     * @return 用户详细信息
     */
    DingtalkUserDetailResponse.Result getUserDetail(String userId);
    
    /**
     * 同步所有用户信息到本地教师表
     * 
     * @return 同步结果数量
     */
    int syncAllUsers();
    
    /**
     * 获取指定部门的所有父部门列表
     * 
     * @param deptId 部门ID
     * @return 部门列表
     */
    List<DingtalkDepartmentResponse.Department> getDepartmentList(Long deptId);
    
    /**
     * 同步指定部门下的所有用户到学生表
     * 
     * @param deptId 部门ID
     * @return 同步结果数量
     */
    int syncStudentsFromDepartment(Long deptId);
    
    /**
     * 同步所有部门的学生信息
     * 
     * @return 同步结果数量
     */
    int syncAllStudents();
    
    // ========================= 部门管理相关接口 =========================
    
    /**
     * 创建钉钉部门
     * 
     * @param request 创建部门请求
     * @return 创建的部门ID
     */
    Long createDepartment(DingtalkDepartmentRequest.CreateRequest request);
    
    /**
     * 更新钉钉部门
     * 
     * @param request 更新部门请求
     * @return 是否成功
     */
    boolean updateDepartment(DingtalkDepartmentRequest.UpdateRequest request);
    
    /**
     * 删除钉钉部门
     * 
     * @param deptId 部门ID
     * @return 是否成功
     */
    boolean deleteDepartment(Long deptId);
    
    /**
     * 同步钉钉部门列表到本地班级
     * 
     * @param parentDeptId 父部门ID，为null时同步所有部门
     * @return 同步结果数量
     */
    int syncDepartmentsToClasses(Long parentDeptId);
    
    // ========================= 用户管理相关接口 =========================
    
    /**
     * 删除钉钉用户
     * 
     * @param userid 用户ID
     * @return 是否成功
     */
    boolean deleteUser(String userid);
    
    /**
     * 更新钉钉用户信息
     * 
     * @param userid 用户ID
     * @param name 用户姓名
     * @param mobile 手机号
     * @param email 邮箱
     * @param title 职位
     * @return 是否成功
     */
    boolean updateUser(String userid, String name, String mobile, String email, String title);
    
    /**
     * 创建钉钉用户
     * 
     * @param userid 用户ID
     * @param name 用户姓名
     * @param mobile 手机号
     * @param email 邮箱
     * @param title 职位
     * @param deptId 部门ID
     * @return 创建结果（包含成功标志、错误码、错误信息等）
     */
    DingtalkUserCreateResult createUser(String userid, String name, String mobile, String email, String title, Long deptId);
    
    // ========================= 打卡记录相关接口 =========================
    
    /**
     * 获取钉钉打卡记录
     * 
     * @param userIdList 用户ID列表
     * @param workDateFrom 开始工作日期，格式：yyyy-MM-dd HH:mm:ss
     * @param workDateTo 结束工作日期，格式：yyyy-MM-dd HH:mm:ss
     * @param offset 偏移量
     * @param limit 每页数量，最大50
     * @return 打卡记录列表
     */
    List<DingtalkAttendanceResponse.AttendanceRecord> getAttendanceRecords(
        List<String> userIdList, String workDateFrom, String workDateTo, Long offset, Long limit);
    
    /**
     * 同步钉钉打卡记录到本地数据库
     * 
     * @param workDateFrom 开始工作日期，格式：yyyy-MM-dd HH:mm:ss
     * @param workDateTo 结束工作日期，格式：yyyy-MM-dd HH:mm:ss
     * @return 同步的记录数量
     */
    int syncAttendanceRecords(String workDateFrom, String workDateTo);
    
    /**
     * 同步指定用户的打卡记录
     * 
     * @param userIdList 用户ID列表
     * @param workDateFrom 开始工作日期，格式：yyyy-MM-dd HH:mm:ss
     * @param workDateTo 结束工作日期，格式：yyyy-MM-dd HH:mm:ss
     * @return 同步的记录数量
     */
    int syncAttendanceRecordsByUsers(List<String> userIdList, String workDateFrom, String workDateTo);
}

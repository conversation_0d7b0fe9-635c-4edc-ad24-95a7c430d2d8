<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="100px">
      <el-form-item label="统计类型" prop="statisticsType">
        <el-select v-model="queryParams.statisticsType" placeholder="请选择统计类型" clearable size="small">
          <el-option label="学生考勤" value="student" />
          <el-option label="教师考勤" value="teacher" />
          <el-option label="班级考勤" value="class" />
        </el-select>
      </el-form-item>
      <el-form-item label="班级" prop="classId" v-if="queryParams.statisticsType === 'student' || queryParams.statisticsType === 'class'">
        <el-select v-model="queryParams.classId" placeholder="请选择班级" clearable size="small">
          <el-option
            v-for="item in classList"
            :key="item.classId"
            :label="item.className"
            :value="item.classId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="学生" prop="studentId" v-if="queryParams.statisticsType === 'student'">
        <el-select v-model="queryParams.studentId" placeholder="请选择学生" clearable size="small" filterable>
          <el-option
            v-for="item in studentList"
            :key="item.studentId"
            :label="item.studentName"
            :value="item.studentId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="教师" prop="teacherId" v-if="queryParams.statisticsType === 'teacher'">
        <el-select v-model="queryParams.teacherId" placeholder="请选择教师" clearable size="small">
          <el-option
            v-for="item in teacherList"
            :key="item.teacherId"
            :label="item.teacherName"
            :value="item.teacherId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="统计日期">
        <el-date-picker
          v-model="dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          size="small"
          @change="handleDateChange"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 统计卡片 -->
    <el-row :gutter="20" class="mb20" v-if="statisticsData">
      <el-col :span="6">
        <div class="statistics-card">
          <div class="card-title">总天数</div>
          <div class="card-value">{{ statisticsData.totalDays || statisticsData.workDays || 0 }}</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="statistics-card">
          <div class="card-title">出勤天数</div>
          <div class="card-value text-success">{{ statisticsData.attendanceDays || 0 }}</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="statistics-card">
          <div class="card-title">缺勤天数</div>
          <div class="card-value text-danger">{{ statisticsData.absentDays || 0 }}</div>
        </div>
      </el-col>
      <el-col :span="6">
        <div class="statistics-card">
          <div class="card-title">出勤率</div>
          <div class="card-value" :class="getAttendanceRateClass(statisticsData.attendanceRate)">
            {{ statisticsData.attendanceRate || 0 }}%
          </div>
        </div>
      </el-col>
    </el-row>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-check"
          size="mini"
          :disabled="multiple"
          @click="handleConfirmBatch"
          v-hasPermi="['kg:attendance:student:confirm']"
        >批量确认</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExportReport"
          v-hasPermi="['kg:attendance:statistics:report']"
        >导出报表</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          icon="el-icon-document"
          size="mini"
          @click="handleGenerateReport"
          v-hasPermi="['kg:attendance:statistics:report']"
        >生成月报</el-button>
      </el-col>
    </el-row>

    <!-- 考勤详情表格 -->
    <el-table 
      v-loading="loading" 
      :data="attendanceDetails" 
      @selection-change="handleSelectionChange"
      v-if="queryParams.statisticsType === 'student'"
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="日期" align="center" prop="attendanceDate" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.attendanceDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="学生姓名" align="center" prop="studentName" />
      <el-table-column label="班级" align="center" prop="className" />
      <el-table-column label="签到时间" align="center" prop="checkinTime" width="120" />
      <el-table-column label="签退时间" align="center" prop="checkoutTime" width="120" />
      <el-table-column label="考勤状态" align="center" prop="attendanceStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.kg_attendance_status" :value="scope.row.attendanceStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="确认状态" align="center" prop="confirmStatus">
        <template slot-scope="scope">
          <el-tag :type="scope.row.confirmStatus === 'confirmed' ? 'success' : 'warning'">
            {{ scope.row.confirmStatus === 'confirmed' ? '已确认' : '待确认' }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="备注" align="center" prop="remark" show-overflow-tooltip />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleException(scope.row)"
            v-hasPermi="['kg:attendance:student:checkin']"
          >异常处理</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 班级统计表格 -->
    <el-table 
      v-loading="loading" 
      :data="classStatistics" 
      v-if="queryParams.statisticsType === 'class' && statisticsData"
    >
      <el-table-column label="学生姓名" align="center" prop="studentName" />
      <el-table-column label="学生编号" align="center" prop="studentId" />
      <el-table-column label="总天数" align="center" prop="totalDays" />
      <el-table-column label="出勤天数" align="center" prop="attendanceDays" />
      <el-table-column label="缺勤天数" align="center" prop="absentDays" />
      <el-table-column label="出勤率" align="center" prop="attendanceRate">
        <template slot-scope="scope">
          <span :class="getAttendanceRateClass(scope.row.attendanceRate)">
            {{ scope.row.attendanceRate }}%
          </span>
        </template>
      </el-table-column>
    </el-table>

    <!-- 状态统计图表 -->
    <el-card class="mt20" v-if="statisticsData && statisticsData.statusStatistics">
      <div slot="header">
        <span>考勤状态统计</span>
      </div>
      <div id="statusChart" style="height: 300px;"></div>
    </el-card>

    <!-- 异常处理对话框 -->
    <el-dialog title="考勤异常处理" :visible.sync="exceptionDialogVisible" width="500px" append-to-body>
      <el-form ref="exceptionForm" :model="exceptionForm" :rules="exceptionRules" label-width="100px">
        <el-form-item label="异常类型" prop="exceptionType">
          <el-select v-model="exceptionForm.exceptionType" placeholder="请选择异常类型">
            <el-option label="迟到" value="late" />
            <el-option label="早退" value="early_leave" />
            <el-option label="请假" value="leave" />
            <el-option label="旷工" value="absent" />
          </el-select>
        </el-form-item>
        <el-form-item label="处理原因" prop="reason">
          <el-input v-model="exceptionForm.reason" type="textarea" placeholder="请输入处理原因" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitException">确 定</el-button>
        <el-button @click="cancelException">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 生成报表对话框 -->
    <el-dialog title="生成月度报表" :visible.sync="reportDialogVisible" width="400px" append-to-body>
      <el-form ref="reportForm" :model="reportForm" label-width="80px">
        <el-form-item label="年份">
          <el-select v-model="reportForm.year" placeholder="请选择年份">
            <el-option
              v-for="year in yearOptions"
              :key="year"
              :label="year"
              :value="year"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="月份">
          <el-select v-model="reportForm.month" placeholder="请选择月份">
            <el-option
              v-for="month in monthOptions"
              :key="month"
              :label="month + '月'"
              :value="month"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitGenerateReport">确 定</el-button>
        <el-button @click="cancelGenerateReport">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  getStudentAttendanceStatistics, 
  getTeacherAttendanceStatistics, 
  getClassAttendanceStatistics,
  confirmAttendanceBatch,
  handleAttendanceException,
  generateMonthlyReport
} from "@/api/kg/attendance/statistics";
import { listClass } from "@/api/kg/student/class";
import { listStudent } from "@/api/kg/student/info";
import { listTeacher } from "@/api/kg/teacher/info";
import * as echarts from 'echarts';

export default {
  name: "AttendanceStatistics",
  dicts: ['kg_attendance_status'],
  data() {
    return {
      // 查询参数
      queryParams: {
        statisticsType: 'student',
        classId: null,
        studentId: null,
        teacherId: null,
        startDate: null,
        endDate: null
      },
      // 日期范围
      dateRange: [],
      // 加载状态
      loading: false,
      // 统计数据
      statisticsData: null,
      // 考勤详情
      attendanceDetails: [],
      // 班级统计
      classStatistics: [],
      // 选中的记录
      ids: [],
      multiple: true,
      // 基础数据
      classList: [],
      studentList: [],
      teacherList: [],
      // 异常处理对话框
      exceptionDialogVisible: false,
      exceptionForm: {
        attendanceId: null,
        exceptionType: '',
        reason: ''
      },
      exceptionRules: {
        exceptionType: [
          { required: true, message: "异常类型不能为空", trigger: "change" }
        ],
        reason: [
          { required: true, message: "处理原因不能为空", trigger: "blur" }
        ]
      },
      // 报表生成对话框
      reportDialogVisible: false,
      reportForm: {
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1
      },
      yearOptions: [],
      monthOptions: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12]
    };
  },
  created() {
    this.initDateRange();
    this.initYearOptions();
    this.getClassList();
    this.getStudentList();
    this.getTeacherList();
  },
  methods: {
    /** 初始化日期范围（默认当月） */
    initDateRange() {
      const now = new Date();
      const year = now.getFullYear();
      const month = now.getMonth();
      const startDate = new Date(year, month, 1);
      const endDate = new Date(year, month + 1, 0);
      
      this.dateRange = [startDate, endDate];
      this.queryParams.startDate = this.formatDate(startDate);
      this.queryParams.endDate = this.formatDate(endDate);
    },
    
    /** 初始化年份选项 */
    initYearOptions() {
      const currentYear = new Date().getFullYear();
      for (let i = currentYear - 2; i <= currentYear + 1; i++) {
        this.yearOptions.push(i);
      }
    },
    
    /** 格式化日期 */
    formatDate(date) {
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');
      return `${year}-${month}-${day}`;
    },
    
    /** 日期范围改变 */
    handleDateChange(dateRange) {
      if (dateRange && dateRange.length === 2) {
        this.queryParams.startDate = this.formatDate(dateRange[0]);
        this.queryParams.endDate = this.formatDate(dateRange[1]);
      } else {
        this.queryParams.startDate = null;
        this.queryParams.endDate = null;
      }
    },
    
    /** 查询统计数据 */
    handleQuery() {
      if (!this.queryParams.startDate || !this.queryParams.endDate) {
        this.$message.warning('请选择统计日期范围');
        return;
      }
      
      this.loading = true;
      
      let apiCall;
      switch (this.queryParams.statisticsType) {
        case 'student':
          apiCall = getStudentAttendanceStatistics(this.queryParams);
          break;
        case 'teacher':
          apiCall = getTeacherAttendanceStatistics(this.queryParams);
          break;
        case 'class':
          if (!this.queryParams.classId) {
            this.$message.warning('请选择班级');
            this.loading = false;
            return;
          }
          apiCall = getClassAttendanceStatistics(this.queryParams);
          break;
        default:
          this.loading = false;
          return;
      }
      
      apiCall.then(response => {
        this.statisticsData = response.data;
        
        if (this.queryParams.statisticsType === 'student') {
          this.attendanceDetails = response.data.attendanceDetails || [];
        } else if (this.queryParams.statisticsType === 'class') {
          this.classStatistics = response.data.studentStatistics || [];
        }
        
        this.$nextTick(() => {
          this.renderStatusChart();
        });
        
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    
    /** 重置查询 */
    resetQuery() {
      this.resetForm("queryForm");
      this.initDateRange();
      this.statisticsData = null;
      this.attendanceDetails = [];
      this.classStatistics = [];
    },
    
    /** 获取班级列表 */
    getClassList() {
      listClass().then(response => {
        this.classList = response.rows;
      });
    },
    
    /** 获取学生列表 */
    getStudentList() {
      listStudent().then(response => {
        this.studentList = response.rows;
      });
    },
    
    /** 获取教师列表 */
    getTeacherList() {
      listTeacher().then(response => {
        this.teacherList = response.rows;
      });
    },
    
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.attendanceId);
      this.multiple = !selection.length;
    },
    
    /** 批量确认考勤 */
    handleConfirmBatch() {
      if (this.ids.length === 0) {
        this.$message.warning('请选择要确认的考勤记录');
        return;
      }
      
      this.$confirm('是否确认选中的考勤记录?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        confirmAttendanceBatch(this.ids).then(response => {
          this.$message.success("确认成功");
          this.handleQuery();
        });
      });
    },
    
    /** 异常处理 */
    handleException(row) {
      this.exceptionForm.attendanceId = row.attendanceId;
      this.exceptionForm.exceptionType = '';
      this.exceptionForm.reason = '';
      this.exceptionDialogVisible = true;
    },
    
    /** 提交异常处理 */
    submitException() {
      this.$refs["exceptionForm"].validate(valid => {
        if (valid) {
          handleAttendanceException(
            this.exceptionForm.attendanceId,
            this.exceptionForm.exceptionType,
            this.exceptionForm.reason
          ).then(response => {
            this.$message.success("处理成功");
            this.exceptionDialogVisible = false;
            this.handleQuery();
          });
        }
      });
    },
    
    /** 取消异常处理 */
    cancelException() {
      this.exceptionDialogVisible = false;
    },
    
    /** 导出报表 */
    handleExportReport() {
      // TODO: 实现导出功能
      this.$message.info("导出功能开发中...");
    },
    
    /** 生成月报 */
    handleGenerateReport() {
      this.reportDialogVisible = true;
    },
    
    /** 提交生成报表 */
    submitGenerateReport() {
      generateMonthlyReport(this.reportForm.year, this.reportForm.month).then(response => {
        this.$message.success("报表生成成功");
        this.reportDialogVisible = false;
        // TODO: 下载报表文件
      });
    },
    
    /** 取消生成报表 */
    cancelGenerateReport() {
      this.reportDialogVisible = false;
    },
    
    /** 获取出勤率样式类 */
    getAttendanceRateClass(rate) {
      if (rate >= 90) return 'text-success';
      if (rate >= 70) return 'text-warning';
      return 'text-danger';
    },
    
    /** 渲染状态统计图表 */
    renderStatusChart() {
      if (!this.statisticsData || !this.statisticsData.statusStatistics) {
        return;
      }
      
      const chartDom = document.getElementById('statusChart');
      if (!chartDom) return;
      
      const myChart = echarts.init(chartDom);
      const statusData = this.statisticsData.statusStatistics;
      
      const data = Object.keys(statusData).map(key => ({
        name: this.getStatusName(key),
        value: statusData[key]
      }));
      
      const option = {
        title: {
          text: '考勤状态分布',
          left: 'center'
        },
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b}: {c} ({d}%)'
        },
        legend: {
          orient: 'vertical',
          left: 'left'
        },
        series: [
          {
            name: '考勤状态',
            type: 'pie',
            radius: '50%',
            data: data,
            emphasis: {
              itemStyle: {
                shadowBlur: 10,
                shadowOffsetX: 0,
                shadowColor: 'rgba(0, 0, 0, 0.5)'
              }
            }
          }
        ]
      };
      
      myChart.setOption(option);
    },
    
    /** 获取状态名称 */
    getStatusName(status) {
      const statusMap = {
        'present': '正常',
        'late': '迟到',
        'early_leave': '早退',
        'absent': '缺勤',
        'leave': '请假'
      };
      return statusMap[status] || status;
    }
  }
};
</script>

<style scoped>
.statistics-card {
  background: #fff;
  border-radius: 4px;
  padding: 20px;
  text-align: center;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.card-title {
  font-size: 14px;
  color: #666;
  margin-bottom: 10px;
}

.card-value {
  font-size: 24px;
  font-weight: bold;
  color: #333;
}

.text-success {
  color: #67c23a !important;
}

.text-warning {
  color: #e6a23c !important;
}

.text-danger {
  color: #f56c6c !important;
}

.mb20 {
  margin-bottom: 20px;
}

.mt20 {
  margin-top: 20px;
}
</style>

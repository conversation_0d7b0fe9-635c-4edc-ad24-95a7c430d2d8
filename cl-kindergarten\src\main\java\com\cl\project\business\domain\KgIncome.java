package com.cl.project.business.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 收入记录对象 kg_income
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgIncome extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 收入ID */
    private Long incomeId;

    /** 收入类型（tuition园费、course托管费、other其他） */
    @Excel(name = "收入类型", readConverterExp = "t=uition园费、course托管费、other其他")
    private String incomeType;

    /** 收入日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "收入日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date incomeDate;

    /** 收入金额 */
    @Excel(name = "收入金额")
    private BigDecimal amount;

    /** 付款人ID（学生ID），关联kg_student.student_id */
    @Excel(name = "付款人ID", readConverterExp = "学=生ID")
    private Long payerId;

    /** 付款人姓名 */
    @Excel(name = "付款人姓名")
    private String payerName;

    /** 支付方式（wechat微信、alipay支付宝、cash现金、bank银行转账） */
    @Excel(name = "支付方式", readConverterExp = "w=echat微信、alipay支付宝、cash现金、bank银行转账")
    private String paymentMethod;

    /** 交易流水号 */
    @Excel(name = "交易流水号")
    private String transactionNo;

    /** 收入描述 */
    @Excel(name = "收入描述")
    private String description;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    public void setIncomeId(Long incomeId) 
    {
        this.incomeId = incomeId;
    }

    public Long getIncomeId() 
    {
        return incomeId;
    }
    public void setIncomeType(String incomeType) 
    {
        this.incomeType = incomeType;
    }

    public String getIncomeType() 
    {
        return incomeType;
    }
    public void setIncomeDate(Date incomeDate) 
    {
        this.incomeDate = incomeDate;
    }

    public Date getIncomeDate() 
    {
        return incomeDate;
    }
    public void setAmount(BigDecimal amount) 
    {
        this.amount = amount;
    }

    public BigDecimal getAmount() 
    {
        return amount;
    }
    public void setPayerId(Long payerId) 
    {
        this.payerId = payerId;
    }

    public Long getPayerId() 
    {
        return payerId;
    }
    public void setPayerName(String payerName) 
    {
        this.payerName = payerName;
    }

    public String getPayerName() 
    {
        return payerName;
    }
    public void setPaymentMethod(String paymentMethod) 
    {
        this.paymentMethod = paymentMethod;
    }

    public String getPaymentMethod() 
    {
        return paymentMethod;
    }
    public void setTransactionNo(String transactionNo) 
    {
        this.transactionNo = transactionNo;
    }

    public String getTransactionNo() 
    {
        return transactionNo;
    }
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("incomeId", getIncomeId())
            .append("incomeType", getIncomeType())
            .append("incomeDate", getIncomeDate())
            .append("amount", getAmount())
            .append("payerId", getPayerId())
            .append("payerName", getPayerName())
            .append("paymentMethod", getPaymentMethod())
            .append("transactionNo", getTransactionNo())
            .append("description", getDescription())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

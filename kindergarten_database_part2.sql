-- =====================================================
-- 幼儿园管理系统数据库结构 - 第二部分
-- 托管服务、费用管理、工资管理表
-- =====================================================

-- =====================================================
-- 3. 托管服务管理表
-- =====================================================

-- 3.1 托管课程表
-- 功能: 定义托管课程的基本信息，包括价格、时长、人数限制等
-- 关联关系:
--   - kg_course_enrollment.course_id (报名记录)
--   - kg_course_attendance.course_id (考勤记录)
--   - kg_course_bill_detail.course_id (费用明细)
DROP TABLE IF EXISTS `kg_course`;
CREATE TABLE `kg_course` (
  `course_id` bigint NOT NULL AUTO_INCREMENT COMMENT '课程ID',
  `course_name` varchar(50) NOT NULL COMMENT '课程名称',
  `course_type` varchar(20) COMMENT '课程类型（英语、美术、全脑、军警等）',
  `price_per_session` decimal(8,2) DEFAULT 0.00 COMMENT '单节课价格',
  `duration` int DEFAULT 60 COMMENT '课程时长（分钟）',
  `min_students` int DEFAULT 8 COMMENT '最少开班人数',
  `max_students` int DEFAULT 20 COMMENT '最大班级人数',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`course_id`),
  KEY `idx_com_id` (`com_id`),
  KEY `idx_course_type` (`course_type`)
) ENGINE=InnoDB COMMENT='托管课程表';

-- 3.2 托管报名记录表
-- 功能: 记录学生托管课程报名信息，包括课时管理、费用管理等
-- 关联关系:
--   - kg_student.student_id (学生信息)
--   - kg_course.course_id (课程信息)
--   - kg_class.class_id (班级信息)
--   - kg_course_attendance.enrollment_id (考勤记录)
--   - kg_course_bill_detail (费用计算)
DROP TABLE IF EXISTS `kg_course_enrollment`;
CREATE TABLE `kg_course_enrollment` (
  `enrollment_id` bigint NOT NULL AUTO_INCREMENT COMMENT '报名ID',
  `student_id` bigint NOT NULL COMMENT '幼儿ID，关联kg_student.student_id',
  `course_id` bigint NOT NULL COMMENT '课程ID，关联kg_course.course_id',
  `class_id` bigint COMMENT '班级ID，关联kg_class.class_id',
  `enrollment_date` date NOT NULL COMMENT '报名日期',
  `total_sessions` int DEFAULT 0 COMMENT '总课时数',
  `used_sessions` int DEFAULT 0 COMMENT '已用课时数',
  `remaining_sessions` int DEFAULT 0 COMMENT '剩余课时数',
  `gift_sessions` int DEFAULT 0 COMMENT '赠送课时数',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '总金额',
  `paid_amount` decimal(10,2) DEFAULT 0.00 COMMENT '已付金额',
  `status` varchar(20) DEFAULT 'active' COMMENT '状态（active活跃、suspended暂停、completed完成、cancelled取消）',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`enrollment_id`),
  KEY `idx_student_course` (`student_id`, `course_id`),
  KEY `idx_com_id` (`com_id`),
  KEY `idx_status` (`status`)
) ENGINE=InnoDB COMMENT='托管报名记录表';

-- 3.3 托管考勤记录表
-- 功能: 记录托管课程的考勤情况，用于课时扣除和费用计算
-- 关联关系:
--   - kg_course_enrollment.enrollment_id (报名记录)
--   - kg_student.student_id (学生信息)
--   - kg_course.course_id (课程信息)
--   - kg_teacher.teacher_id (授课教师)
--   - kg_course_bill_detail (费用计算)
DROP TABLE IF EXISTS `kg_course_attendance`;
CREATE TABLE `kg_course_attendance` (
  `attendance_id` bigint NOT NULL AUTO_INCREMENT COMMENT '考勤ID',
  `enrollment_id` bigint NOT NULL COMMENT '报名ID，关联kg_course_enrollment.enrollment_id',
  `student_id` bigint NOT NULL COMMENT '幼儿ID，关联kg_student.student_id',
  `course_id` bigint NOT NULL COMMENT '课程ID，关联kg_course.course_id',
  `teacher_id` bigint COMMENT '授课教师ID，关联kg_teacher.teacher_id',
  `attendance_date` date NOT NULL COMMENT '上课日期',
  `start_time` datetime COMMENT '开始时间',
  `end_time` datetime COMMENT '结束时间',
  `attendance_status` varchar(20) DEFAULT 'present' COMMENT '考勤状态（present出勤、absent缺勤、late迟到、early早退）',
  `check_in_method` varchar(20) DEFAULT 'face' COMMENT '签到方式（face人脸、manual手动）',
  `is_confirmed` tinyint DEFAULT 0 COMMENT '是否确认（0未确认 1已确认）',
  `confirmed_by` bigint COMMENT '确认人ID，关联kg_teacher.teacher_id',
  `confirmed_time` datetime COMMENT '确认时间',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`attendance_id`),
  KEY `idx_enrollment_date` (`enrollment_id`, `attendance_date`),
  KEY `idx_student_course_date` (`student_id`, `course_id`, `attendance_date`),
  KEY `idx_com_id` (`com_id`),
  KEY `idx_teacher_date` (`teacher_id`, `attendance_date`)
) ENGINE=InnoDB COMMENT='托管考勤记录表';

-- =====================================================
-- 4. 费用管理表
-- =====================================================

-- 4.1 园费配置表
-- 功能: 配置不同班级类型的园费标准，支持按时间生效
-- 关联关系:
--   - kg_tuition_bill (园费计算依据)
DROP TABLE IF EXISTS `kg_tuition_config`;
CREATE TABLE `kg_tuition_config` (
  `config_id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `class_type` varchar(20) NOT NULL COMMENT '班级类型（托班、小班、中班、大班）',
  `meal_fee_per_day` decimal(8,2) DEFAULT 20.00 COMMENT '每日餐费',
  `education_fee_per_month` decimal(10,2) DEFAULT 1200.00 COMMENT '每月保教费',
  `attendance_threshold` decimal(4,2) DEFAULT 0.50 COMMENT '出勤率阈值（超过此值收全额保教费）',
  `half_education_fee` decimal(10,2) DEFAULT 600.00 COMMENT '半额保教费',
  `effective_date` date NOT NULL COMMENT '生效日期',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`),
  KEY `idx_class_type_date` (`class_type`, `effective_date`),
  KEY `idx_com_id` (`com_id`)
) ENGINE=InnoDB COMMENT='园费配置表';

-- 4.2 园费账单表
-- 功能: 生成学生每月园费账单，包括餐费和保教费的计算
-- 关联关系:
--   - kg_student.student_id (学生信息)
--   - kg_class.class_id (班级信息)
--   - kg_student_attendance (出勤统计)
--   - kg_tuition_config (费用标准)
--   - kg_income (收入记录)
DROP TABLE IF EXISTS `kg_tuition_bill`;
CREATE TABLE `kg_tuition_bill` (
  `bill_id` bigint NOT NULL AUTO_INCREMENT COMMENT '账单ID',
  `student_id` bigint NOT NULL COMMENT '幼儿ID，关联kg_student.student_id',
  `class_id` bigint NOT NULL COMMENT '班级ID，关联kg_class.class_id',
  `bill_year` int NOT NULL COMMENT '账单年份',
  `bill_month` int NOT NULL COMMENT '账单月份',
  `total_days` int DEFAULT 0 COMMENT '当月总天数',
  `attendance_days` int DEFAULT 0 COMMENT '出勤天数',
  `attendance_rate` decimal(5,2) DEFAULT 0.00 COMMENT '出勤率',
  `meal_fee_total` decimal(10,2) DEFAULT 0.00 COMMENT '餐费总额',
  `meal_fee_used` decimal(10,2) DEFAULT 0.00 COMMENT '已用餐费',
  `meal_fee_balance` decimal(10,2) DEFAULT 0.00 COMMENT '餐费余额',
  `education_fee_total` decimal(10,2) DEFAULT 0.00 COMMENT '保教费总额',
  `education_fee_used` decimal(10,2) DEFAULT 0.00 COMMENT '已用保教费',
  `education_fee_balance` decimal(10,2) DEFAULT 0.00 COMMENT '保教费余额',
  `total_balance` decimal(10,2) DEFAULT 0.00 COMMENT '总余额',
  `next_month_prepay` decimal(10,2) DEFAULT 0.00 COMMENT '下月预交费用',
  `actual_payment` decimal(10,2) DEFAULT 0.00 COMMENT '实际缴费金额',
  `bill_status` varchar(20) DEFAULT 'generated' COMMENT '账单状态（generated已生成、sent已发送、paid已支付）',
  `sent_time` datetime COMMENT '发送时间',
  `paid_time` datetime COMMENT '支付时间',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`bill_id`),
  UNIQUE KEY `uk_student_month` (`student_id`, `bill_year`, `bill_month`),
  KEY `idx_class_month` (`class_id`, `bill_year`, `bill_month`),
  KEY `idx_com_id` (`com_id`),
  KEY `idx_bill_status` (`bill_status`)
) ENGINE=InnoDB COMMENT='园费账单表';

-- 4.3 托管费账单表
-- 功能: 生成学生每月托管费账单
-- 关联关系:
--   - kg_student.student_id (学生信息)
--   - kg_course_bill_detail.bill_id (账单明细)
--   - kg_course_attendance (考勤统计)
--   - kg_income (收入记录)
DROP TABLE IF EXISTS `kg_course_bill`;
CREATE TABLE `kg_course_bill` (
  `bill_id` bigint NOT NULL AUTO_INCREMENT COMMENT '账单ID',
  `student_id` bigint NOT NULL COMMENT '幼儿ID，关联kg_student.student_id',
  `bill_year` int NOT NULL COMMENT '账单年份',
  `bill_month` int NOT NULL COMMENT '账单月份',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '总金额',
  `gift_sessions` int DEFAULT 0 COMMENT '赠送课时数',
  `gift_course_name` varchar(100) COMMENT '赠送课程名称',
  `bill_status` varchar(20) DEFAULT 'generated' COMMENT '账单状态（generated已生成、sent已发送、paid已支付）',
  `sent_time` datetime COMMENT '发送时间',
  `paid_time` datetime COMMENT '支付时间',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`bill_id`),
  UNIQUE KEY `uk_student_month` (`student_id`, `bill_year`, `bill_month`),
  KEY `idx_com_id` (`com_id`),
  KEY `idx_bill_status` (`bill_status`)
) ENGINE=InnoDB COMMENT='托管费账单表';

-- 4.4 托管费账单明细表
-- 功能: 记录托管费账单的详细课程费用
-- 关联关系:
--   - kg_course_bill.bill_id (主账单)
--   - kg_course.course_id (课程信息)
--   - kg_course_attendance (考勤统计)
DROP TABLE IF EXISTS `kg_course_bill_detail`;
CREATE TABLE `kg_course_bill_detail` (
  `detail_id` bigint NOT NULL AUTO_INCREMENT COMMENT '明细ID',
  `bill_id` bigint NOT NULL COMMENT '账单ID，关联kg_course_bill.bill_id',
  `course_id` bigint NOT NULL COMMENT '课程ID，关联kg_course.course_id',
  `course_name` varchar(50) COMMENT '课程名称',
  `sessions_count` int DEFAULT 0 COMMENT '课时数',
  `price_per_session` decimal(8,2) DEFAULT 0.00 COMMENT '单节课价格',
  `total_amount` decimal(10,2) DEFAULT 0.00 COMMENT '小计金额',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  PRIMARY KEY (`detail_id`),
  KEY `idx_bill_id` (`bill_id`),
  KEY `idx_course_id` (`course_id`),
  KEY `idx_com_id` (`com_id`)
) ENGINE=InnoDB COMMENT='托管费账单明细表';

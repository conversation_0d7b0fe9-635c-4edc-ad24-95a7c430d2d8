import request from '@/utils/request.js'

// 查询教师考勤概览列表
export function getTeacherAttendanceOverview(params) {
  return request.get('/business/teacher-attendance/overview', params)
}

// 查询教师考勤记录列表
export function getTeacherAttendanceList(params) {
  return request.get('/business/teacher-attendance/list', params)
}

// 获取教师考勤详情
export function getTeacherAttendanceDetail(attendanceId) {
  return request.get(`/business/teacher-attendance/${attendanceId}`)
}

// 新增教师考勤记录
export function addTeacherAttendance(data) {
  return request.post('/business/teacher-attendance', data)
}

// 修改教师考勤记录
export function updateTeacherAttendance(data) {
  return request.put('/business/teacher-attendance', data)
}

// 删除教师考勤记录
export function deleteTeacherAttendance(attendanceIds) {
  return request.delete(`/business/teacher-attendance/${attendanceIds}`)
}

// 批量教师签到
export function batchTeacherCheckin(data) {
  return request.post('/business/teacher-attendance/batchCheckin', data)
}

// 批量确认教师考勤
export function batchConfirmAttendance(data) {
  return request.post('/business/teacher-attendance/batchConfirm', data)
}

// 导出教师考勤记录
export function exportTeacherAttendance(params) {
  return request.get('/business/teacher-attendance/export', params)
}

// 获取所有教师列表
export function getAllTeacherList() {
  return request.get('/business/teacher/allList')
}

// 同步钉钉考勤数据
export function syncAttendanceByUsers(data) {
  // 如果userIdList为空数组，则调用同步所有用户的接口
  if (!data.userIdList || data.userIdList.length === 0) {
    return request.post('/business/dingtalk/syncAttendance', data)
  }
  return request.post('/business/dingtalk/syncAttendanceByUsers', data)
}
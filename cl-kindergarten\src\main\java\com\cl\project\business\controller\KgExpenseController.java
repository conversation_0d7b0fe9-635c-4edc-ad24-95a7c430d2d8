package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgExpense;
import com.cl.project.business.service.IKgExpenseService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 支出记录Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/expense")
public class KgExpenseController extends BaseController
{
    @Autowired
    private IKgExpenseService kgExpenseService;

    /**
     * 查询支出记录列表
     */
    @SaCheckPermission("business:expense:list")
    @GetMapping("/list")
    public TableDataInfo list(KgExpense kgExpense)
    {
        startPage();
        List<KgExpense> list = kgExpenseService.selectKgExpenseList(kgExpense);
        return getDataTable(list);
    }

    /**
     * 导出支出记录列表
     */
    @SaCheckPermission("business:expense:export")
    @Log(title = "支出记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgExpense kgExpense)
    {
        List<KgExpense> list = kgExpenseService.selectKgExpenseList(kgExpense);
        ExcelUtil<KgExpense> util = new ExcelUtil<KgExpense>(KgExpense.class);
        return util.exportExcel(list, "expense");
    }

    /**
     * 获取支出记录详细信息
     */
    @SaCheckPermission("business:expense:query")
    @GetMapping(value = "/{expenseId}")
    public AjaxResult getInfo(@PathVariable("expenseId") Long expenseId)
    {
        return AjaxResult.success(kgExpenseService.selectKgExpenseById(expenseId));
    }

    /**
     * 新增支出记录
     */
    @SaCheckPermission("business:expense:add")
    @Log(title = "支出记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgExpense kgExpense)
    {
        return toAjax(kgExpenseService.insertKgExpense(kgExpense));
    }

    /**
     * 修改支出记录
     */
    @SaCheckPermission("business:expense:edit")
    @Log(title = "支出记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgExpense kgExpense)
    {
        return toAjax(kgExpenseService.updateKgExpense(kgExpense));
    }

    /**
     * 删除支出记录
     */
    @SaCheckPermission("business:expense:remove")
    @Log(title = "支出记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{expenseIds}")
    public AjaxResult remove(@PathVariable Long[] expenseIds)
    {
        return toAjax(kgExpenseService.deleteKgExpenseByIds(expenseIds));
    }
}

package com.cl.project.business.domain;

import java.util.Date;

import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 学生考勤记录对象 kg_student_attendance
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgStudentAttendance extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 考勤ID */
    private Long attendanceId;

    /** 幼儿ID，关联kg_student.student_id */
    @Excel(name = "幼儿ID，关联kg_student.student_id")
    private Long studentId;

    /** 班级ID，关联kg_class.class_id */
    @Excel(name = "班级ID，关联kg_class.class_id")
    private Long classId;

    /** 考勤日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "考勤日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date attendanceDate;

    /** 签到时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "签到时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date checkInTime;

    /** 考勤状态（1-出勤，2-迟到，3-缺勤，4-请假，5-早退，6-病假，8-休假） */
    @Excel(name = "考勤状态", readConverterExp = "1=出勤,2=迟到,3=缺勤,4=请假,5=早退,6=病假,8=休假")
    private String attendanceStatus;

    /** 缺勤原因 */
    @Excel(name = "缺勤原因")
    private String absenceReason;

    /** 病假详情（病名、症状、治疗、用药等） */
    @Excel(name = "病假详情", readConverterExp = "病=名、症状、治疗、用药等")
    private String sickDetail;

    /** 请假详情（时长、地点等） */
    @Excel(name = "请假详情", readConverterExp = "时=长、地点等")
    private String leaveDetail;

    /** 签到方式（face人脸、manual手动） */
    @Excel(name = "签到方式", readConverterExp = "f=ace人脸、manual手动")
    private String checkInMethod;

    /** 操作员ID，关联kg_teacher.teacher_id */
    @Excel(name = "操作员ID，关联kg_teacher.teacher_id")
    private Long operatorId;

    /** 是否确认（0未确认 1已确认） */
    @Excel(name = "是否确认", readConverterExp = "0=未确认,1=已确认")
    private Integer isConfirmed;

    /** 确认人ID，关联kg_teacher.teacher_id */
    @Excel(name = "确认人ID，关联kg_teacher.teacher_id")
    private Long confirmedBy;

    /** 确认时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date confirmedTime;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    // ========== 关联对象字段，用于前端显示 ==========
    /** 学生姓名 - 关联显示 */
    private String studentName;
    
    /** 班级名称 - 关联显示 */
    private String className;
    
    /** 操作员姓名 - 关联显示 */
    private String operatorName;
    
    /** 确认人姓名 - 关联显示 */
    private String confirmedByName;

    public void setAttendanceId(Long attendanceId) 
    {
        this.attendanceId = attendanceId;
    }

    public Long getAttendanceId() 
    {
        return attendanceId;
    }
    public void setStudentId(Long studentId) 
    {
        this.studentId = studentId;
    }

    public Long getStudentId() 
    {
        return studentId;
    }
    public void setClassId(Long classId) 
    {
        this.classId = classId;
    }

    public Long getClassId() 
    {
        return classId;
    }
    public void setAttendanceDate(Date attendanceDate) 
    {
        this.attendanceDate = attendanceDate;
    }

    public Date getAttendanceDate() 
    {
        return attendanceDate;
    }
    public void setCheckInTime(Date checkInTime) 
    {
        this.checkInTime = checkInTime;
    }

    public Date getCheckInTime() 
    {
        return checkInTime;
    }
    public void setAttendanceStatus(String attendanceStatus) 
    {
        this.attendanceStatus = attendanceStatus;
    }

    public String getAttendanceStatus() 
    {
        return attendanceStatus;
    }
    public void setAbsenceReason(String absenceReason) 
    {
        this.absenceReason = absenceReason;
    }

    public String getAbsenceReason() 
    {
        return absenceReason;
    }
    public void setSickDetail(String sickDetail) 
    {
        this.sickDetail = sickDetail;
    }

    public String getSickDetail() 
    {
        return sickDetail;
    }
    public void setLeaveDetail(String leaveDetail) 
    {
        this.leaveDetail = leaveDetail;
    }

    public String getLeaveDetail() 
    {
        return leaveDetail;
    }
    public void setCheckInMethod(String checkInMethod) 
    {
        this.checkInMethod = checkInMethod;
    }

    public String getCheckInMethod() 
    {
        return checkInMethod;
    }
    public void setOperatorId(Long operatorId) 
    {
        this.operatorId = operatorId;
    }

    public Long getOperatorId() 
    {
        return operatorId;
    }
    public void setIsConfirmed(Integer isConfirmed) 
    {
        this.isConfirmed = isConfirmed;
    }

    public Integer getIsConfirmed() 
    {
        return isConfirmed;
    }
    public void setConfirmedBy(Long confirmedBy) 
    {
        this.confirmedBy = confirmedBy;
    }

    public Long getConfirmedBy() 
    {
        return confirmedBy;
    }
    public void setConfirmedTime(Date confirmedTime) 
    {
        this.confirmedTime = confirmedTime;
    }

    public Date getConfirmedTime() 
    {
        return confirmedTime;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    // ========== 关联对象字段的 getter/setter ==========
    public String getStudentName() {
        return studentName;
    }

    public void setStudentName(String studentName) {
        this.studentName = studentName;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    public String getOperatorName() {
        return operatorName;
    }

    public void setOperatorName(String operatorName) {
        this.operatorName = operatorName;
    }

    public String getConfirmedByName() {
        return confirmedByName;
    }

    public void setConfirmedByName(String confirmedByName) {
        this.confirmedByName = confirmedByName;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("attendanceId", getAttendanceId())
            .append("studentId", getStudentId())
            .append("classId", getClassId())
            .append("attendanceDate", getAttendanceDate())
            .append("checkInTime", getCheckInTime())
            .append("attendanceStatus", getAttendanceStatus())
            .append("absenceReason", getAbsenceReason())
            .append("sickDetail", getSickDetail())
            .append("leaveDetail", getLeaveDetail())
            .append("checkInMethod", getCheckInMethod())
            .append("operatorId", getOperatorId())
            .append("isConfirmed", getIsConfirmed())
            .append("confirmedBy", getConfirmedBy())
            .append("confirmedTime", getConfirmedTime())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

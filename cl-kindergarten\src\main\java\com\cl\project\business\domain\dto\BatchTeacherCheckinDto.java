package com.cl.project.business.domain.dto;

import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 教师批量签到DTO
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public class BatchTeacherCheckinDto 
{
    /** 教师ID列表 */
    @NotEmpty(message = "教师ID列表不能为空")
    private List<Long> teacherIds;
    
    /** 考勤日期 */
    @NotNull(message = "考勤日期不能为空")
    private String attendanceDate;
    
    /** 考勤状态 (1已签到 2已签退 3缺勤 4请假) */
    @NotNull(message = "考勤状态不能为空")
    private String attendanceStatus;
    
    /** 备注 */
    private String remark;

    public List<Long> getTeacherIds() {
        return teacherIds;
    }

    public void setTeacherIds(List<Long> teacherIds) {
        this.teacherIds = teacherIds;
    }

    public String getAttendanceDate() {
        return attendanceDate;
    }

    public void setAttendanceDate(String attendanceDate) {
        this.attendanceDate = attendanceDate;
    }

    public String getAttendanceStatus() {
        return attendanceStatus;
    }

    public void setAttendanceStatus(String attendanceStatus) {
        this.attendanceStatus = attendanceStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "BatchTeacherCheckinDto{" +
                "teacherIds=" + teacherIds +
                ", attendanceDate='" + attendanceDate + '\'' +
                ", attendanceStatus='" + attendanceStatus + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}

package com.cl.project.business.service;

import java.time.LocalDate;
import java.util.List;
import java.util.Map;

/**
 * 考勤统计Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IKgAttendanceStatisticsService 
{
    /**
     * 获取学生考勤统计
     * 
     * @param classId 班级ID（可选）
     * @param studentId 学生ID（可选）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果
     */
    Map<String, Object> getStudentAttendanceStatistics(Long classId, Long studentId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取教师考勤统计
     * 
     * @param teacherId 教师ID（可选）
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果
     */
    Map<String, Object> getTeacherAttendanceStatistics(Long teacherId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取班级考勤统计
     * 
     * @param classId 班级ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 统计结果
     */
    Map<String, Object> getClassAttendanceStatistics(Long classId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取考勤汇总报表
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 汇总统计
     */
    Map<String, Object> getAttendanceSummary(LocalDate startDate, LocalDate endDate);

    /**
     * 批量确认考勤记录
     * 
     * @param attendanceIds 考勤记录ID列表
     * @return 确认数量
     */
    int confirmAttendanceBatch(List<Long> attendanceIds);

    /**
     * 生成月度考勤报表
     * 
     * @param year 年份
     * @param month 月份
     * @return 报表文件路径
     */
    String generateMonthlyReport(Integer year, Integer month);

    /**
     * 考勤异常处理
     * 
     * @param attendanceId 考勤记录ID
     * @param exceptionType 异常类型
     * @param reason 处理原因
     * @return 处理结果
     */
    int handleAttendanceException(Long attendanceId, String exceptionType, String reason);

    /**
     * 计算学生出勤率
     * 
     * @param studentId 学生ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 出勤率（百分比）
     */
    Double calculateStudentAttendanceRate(Long studentId, LocalDate startDate, LocalDate endDate);

    /**
     * 计算班级平均出勤率
     * 
     * @param classId 班级ID
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 平均出勤率（百分比）
     */
    Double calculateClassAverageAttendanceRate(Long classId, LocalDate startDate, LocalDate endDate);

    /**
     * 获取考勤异常记录
     * 
     * @param startDate 开始日期
     * @param endDate 结束日期
     * @return 异常记录列表
     */
    List<Map<String, Object>> getAttendanceExceptions(LocalDate startDate, LocalDate endDate);

    /**
     * 获取学生考勤详情
     * 
     * @param studentId 学生ID
     * @param month 月份（格式：yyyy-MM）
     * @return 考勤详情
     */
    Map<String, Object> getStudentAttendanceDetail(Long studentId, String month);
}

package com.cl.project.business.domain;

import java.math.BigDecimal;

import com.cl.framework.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 托管费账单明细对象 kg_course_bill_detail
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgCourseBillDetail extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 明细ID */
    private Long detailId;

    /** 账单ID，关联kg_course_bill.bill_id */
    @Excel(name = "账单ID，关联kg_course_bill.bill_id")
    private Long billId;

    /** 课程ID，关联kg_course.course_id */
    @Excel(name = "课程ID，关联kg_course.course_id")
    private Long courseId;

    /** 课程名称 */
    @Excel(name = "课程名称")
    private String courseName;

    /** 课时数 */
    @Excel(name = "课时数")
    private Long sessionsCount;

    /** 单节课价格 */
    @Excel(name = "单节课价格")
    private BigDecimal pricePerSession;

    /** 小计金额 */
    @Excel(name = "小计金额")
    private BigDecimal totalAmount;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    public void setDetailId(Long detailId) 
    {
        this.detailId = detailId;
    }

    public Long getDetailId() 
    {
        return detailId;
    }
    public void setBillId(Long billId) 
    {
        this.billId = billId;
    }

    public Long getBillId() 
    {
        return billId;
    }
    public void setCourseId(Long courseId) 
    {
        this.courseId = courseId;
    }

    public Long getCourseId() 
    {
        return courseId;
    }
    public void setCourseName(String courseName) 
    {
        this.courseName = courseName;
    }

    public String getCourseName() 
    {
        return courseName;
    }
    public void setSessionsCount(Long sessionsCount) 
    {
        this.sessionsCount = sessionsCount;
    }

    public Long getSessionsCount() 
    {
        return sessionsCount;
    }
    public void setPricePerSession(BigDecimal pricePerSession) 
    {
        this.pricePerSession = pricePerSession;
    }

    public BigDecimal getPricePerSession() 
    {
        return pricePerSession;
    }
    public void setTotalAmount(BigDecimal totalAmount) 
    {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getTotalAmount() 
    {
        return totalAmount;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("detailId", getDetailId())
            .append("billId", getBillId())
            .append("courseId", getCourseId())
            .append("courseName", getCourseName())
            .append("sessionsCount", getSessionsCount())
            .append("pricePerSession", getPricePerSession())
            .append("totalAmount", getTotalAmount())
            .append("comId", getComId())
            .toString();
    }
}

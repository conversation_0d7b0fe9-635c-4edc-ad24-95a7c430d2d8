package com.cl.project.business.mapper;

import java.util.List;
import com.cl.project.business.domain.KgStudent;

/**
 * 幼儿信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface KgStudentMapper 
{
    /**
     * 查询幼儿信息
     * 
     * @param studentId 幼儿信息ID
     * @return 幼儿信息
     */
    public KgStudent selectKgStudentById(Long studentId);

    /**
     * 查询幼儿信息列表
     * 
     * @param kgStudent 幼儿信息
     * @return 幼儿信息集合
     */
    public List<KgStudent> selectKgStudentList(KgStudent kgStudent);

    /**
     * 新增幼儿信息
     * 
     * @param kgStudent 幼儿信息
     * @return 结果
     */
    public int insertKgStudent(KgStudent kgStudent);

    /**
     * 修改幼儿信息
     * 
     * @param kgStudent 幼儿信息
     * @return 结果
     */
    public int updateKgStudent(KgStudent kgStudent);

    /**
     * 删除幼儿信息
     * 
     * @param studentId 幼儿信息ID
     * @return 结果
     */
    public int deleteKgStudentById(Long studentId);

    /**
     * 批量删除幼儿信息
     * 
     * @param studentIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteKgStudentByIds(Long[] studentIds);
    
    /**
     * 根据钉钉用户ID查询学生信息
     * 
     * @param dingtalkUserId 钉钉用户ID
     * @return 学生信息
     */
    public KgStudent selectKgStudentByDingtalkUserId(String dingtalkUserId);
}

package com.cl.project.business.mapper;

import java.util.List;
import com.cl.project.business.domain.KgCourseAttendance;

/**
 * 托管考勤记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface KgCourseAttendanceMapper 
{
    /**
     * 查询托管考勤记录
     * 
     * @param attendanceId 托管考勤记录ID
     * @return 托管考勤记录
     */
    public KgCourseAttendance selectKgCourseAttendanceById(Long attendanceId);

    /**
     * 查询托管考勤记录列表
     * 
     * @param kgCourseAttendance 托管考勤记录
     * @return 托管考勤记录集合
     */
    public List<KgCourseAttendance> selectKgCourseAttendanceList(KgCourseAttendance kgCourseAttendance);

    /**
     * 新增托管考勤记录
     * 
     * @param kgCourseAttendance 托管考勤记录
     * @return 结果
     */
    public int insertKgCourseAttendance(KgCourseAttendance kgCourseAttendance);

    /**
     * 修改托管考勤记录
     * 
     * @param kgCourseAttendance 托管考勤记录
     * @return 结果
     */
    public int updateKgCourseAttendance(KgCourseAttendance kgCourseAttendance);

    /**
     * 删除托管考勤记录
     * 
     * @param attendanceId 托管考勤记录ID
     * @return 结果
     */
    public int deleteKgCourseAttendanceById(Long attendanceId);

    /**
     * 批量删除托管考勤记录
     * 
     * @param attendanceIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteKgCourseAttendanceByIds(Long[] attendanceIds);
}

package com.cl.project.business.service;

import java.util.List;
import com.cl.project.business.domain.KgTimeConfig;

/**
 * 时间段配置Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IKgTimeConfigService 
{
    /**
     * 查询时间段配置
     * 
     * @param configId 时间段配置ID
     * @return 时间段配置
     */
    public KgTimeConfig selectKgTimeConfigById(Long configId);

    /**
     * 查询时间段配置列表
     * 
     * @param kgTimeConfig 时间段配置
     * @return 时间段配置集合
     */
    public List<KgTimeConfig> selectKgTimeConfigList(KgTimeConfig kgTimeConfig);

    /**
     * 新增时间段配置
     * 
     * @param kgTimeConfig 时间段配置
     * @return 结果
     */
    public int insertKgTimeConfig(KgTimeConfig kgTimeConfig);

    /**
     * 修改时间段配置
     * 
     * @param kgTimeConfig 时间段配置
     * @return 结果
     */
    public int updateKgTimeConfig(KgTimeConfig kgTimeConfig);

    /**
     * 批量删除时间段配置
     * 
     * @param configIds 需要删除的时间段配置ID
     * @return 结果
     */
    public int deleteKgTimeConfigByIds(Long[] configIds);

    /**
     * 删除时间段配置信息
     * 
     * @param configId 时间段配置ID
     * @return 结果
     */
    public int deleteKgTimeConfigById(Long configId);
}

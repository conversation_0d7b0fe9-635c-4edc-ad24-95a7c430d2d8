<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgCourseMapper">
    
    <resultMap type="KgCourse" id="KgCourseResult">
        <result property="courseId"    column="course_id"    />
        <result property="courseName"    column="course_name"    />
        <result property="courseType"    column="course_type"    />
        <result property="pricePerSession"    column="price_per_session"    />
        <result property="duration"    column="duration"    />
        <result property="minStudents"    column="min_students"    />
        <result property="maxStudents"    column="max_students"    />
        <result property="status"    column="status"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgCourseVo">
        select course_id, course_name, course_type, price_per_session, duration, min_students, max_students, status, com_id, create_by, create_time, update_by, update_time, remark from kg_course
    </sql>

    <select id="selectKgCourseList" parameterType="KgCourse" resultMap="KgCourseResult">
        <include refid="selectKgCourseVo"/>
        <where>  
            <if test="courseName != null  and courseName != ''"> and course_name like concat('%', #{courseName}, '%')</if>
            <if test="courseType != null  and courseType != ''"> and course_type = #{courseType}</if>
            <if test="pricePerSession != null "> and price_per_session = #{pricePerSession}</if>
            <if test="duration != null "> and duration = #{duration}</if>
            <if test="minStudents != null "> and min_students = #{minStudents}</if>
            <if test="maxStudents != null "> and max_students = #{maxStudents}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgCourseById" parameterType="Long" resultMap="KgCourseResult">
        <include refid="selectKgCourseVo"/>
        where course_id = #{courseId}
    </select>
        
    <insert id="insertKgCourse" parameterType="KgCourse" useGeneratedKeys="true" keyProperty="courseId">
        insert into kg_course
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="courseName != null and courseName != ''">course_name,</if>
            <if test="courseType != null">course_type,</if>
            <if test="pricePerSession != null">price_per_session,</if>
            <if test="duration != null">duration,</if>
            <if test="minStudents != null">min_students,</if>
            <if test="maxStudents != null">max_students,</if>
            <if test="status != null">status,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="courseName != null and courseName != ''">#{courseName},</if>
            <if test="courseType != null">#{courseType},</if>
            <if test="pricePerSession != null">#{pricePerSession},</if>
            <if test="duration != null">#{duration},</if>
            <if test="minStudents != null">#{minStudents},</if>
            <if test="maxStudents != null">#{maxStudents},</if>
            <if test="status != null">#{status},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgCourse" parameterType="KgCourse">
        update kg_course
        <trim prefix="SET" suffixOverrides=",">
            <if test="courseName != null and courseName != ''">course_name = #{courseName},</if>
            <if test="courseType != null">course_type = #{courseType},</if>
            <if test="pricePerSession != null">price_per_session = #{pricePerSession},</if>
            <if test="duration != null">duration = #{duration},</if>
            <if test="minStudents != null">min_students = #{minStudents},</if>
            <if test="maxStudents != null">max_students = #{maxStudents},</if>
            <if test="status != null">status = #{status},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where course_id = #{courseId}
    </update>

    <delete id="deleteKgCourseById" parameterType="Long">
        delete from kg_course where course_id = #{courseId}
    </delete>

    <delete id="deleteKgCourseByIds" parameterType="String">
        delete from kg_course where course_id in 
        <foreach item="courseId" collection="array" open="(" separator="," close=")">
            #{courseId}
        </foreach>
    </delete>
    
</mapper>
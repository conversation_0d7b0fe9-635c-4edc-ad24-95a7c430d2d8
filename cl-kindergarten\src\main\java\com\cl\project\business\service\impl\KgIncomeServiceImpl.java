package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgIncomeMapper;
import com.cl.project.business.domain.KgIncome;
import com.cl.project.business.service.IKgIncomeService;

/**
 * 收入记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgIncomeServiceImpl implements IKgIncomeService 
{
    @Autowired
    private KgIncomeMapper kgIncomeMapper;

    /**
     * 查询收入记录
     * 
     * @param incomeId 收入记录ID
     * @return 收入记录
     */
    @Override
    public KgIncome selectKgIncomeById(Long incomeId)
    {
        return kgIncomeMapper.selectKgIncomeById(incomeId);
    }

    /**
     * 查询收入记录列表
     * 
     * @param kgIncome 收入记录
     * @return 收入记录
     */
    @Override
    public List<KgIncome> selectKgIncomeList(KgIncome kgIncome)
    {
        return kgIncomeMapper.selectKgIncomeList(kgIncome);
    }

    /**
     * 新增收入记录
     * 
     * @param kgIncome 收入记录
     * @return 结果
     */
    @Override
    public int insertKgIncome(KgIncome kgIncome)
    {
        kgIncome.setCreateTime(DateUtils.getNowDate());
        return kgIncomeMapper.insertKgIncome(kgIncome);
    }

    /**
     * 修改收入记录
     * 
     * @param kgIncome 收入记录
     * @return 结果
     */
    @Override
    public int updateKgIncome(KgIncome kgIncome)
    {
        kgIncome.setUpdateTime(DateUtils.getNowDate());
        return kgIncomeMapper.updateKgIncome(kgIncome);
    }

    /**
     * 批量删除收入记录
     * 
     * @param incomeIds 需要删除的收入记录ID
     * @return 结果
     */
    @Override
    public int deleteKgIncomeByIds(Long[] incomeIds)
    {
        return kgIncomeMapper.deleteKgIncomeByIds(incomeIds);
    }

    /**
     * 删除收入记录信息
     * 
     * @param incomeId 收入记录ID
     * @return 结果
     */
    @Override
    public int deleteKgIncomeById(Long incomeId)
    {
        return kgIncomeMapper.deleteKgIncomeById(incomeId);
    }
}

package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgTeacherMapper;
import com.cl.project.business.domain.KgTeacher;
import com.cl.project.business.service.IKgTeacherService;

/**
 * 教师信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgTeacherServiceImpl implements IKgTeacherService 
{
    @Autowired
    private KgTeacherMapper kgTeacherMapper;

    /**
     * 查询教师信息
     * 
     * @param teacherId 教师信息ID
     * @return 教师信息
     */
    @Override
    public KgTeacher selectKgTeacherById(Long teacherId)
    {
        return kgTeacherMapper.selectKgTeacherById(teacherId);
    }

    /**
     * 根据钉钉用户ID查询教师信息
     * 
     * @param dingtalkUserId 钉钉用户ID
     * @return 教师信息
     */
    @Override
    public KgTeacher selectKgTeacherByDingtalkUserId(String dingtalkUserId)
    {
        return kgTeacherMapper.selectKgTeacherByDingtalkUserId(dingtalkUserId);
    }

    /**
     * 查询教师信息列表
     * 
     * @param kgTeacher 教师信息
     * @return 教师信息
     */
    @Override
    public List<KgTeacher> selectKgTeacherList(KgTeacher kgTeacher)
    {
        return kgTeacherMapper.selectKgTeacherList(kgTeacher);
    }

    /**
     * 新增教师信息
     * 
     * @param kgTeacher 教师信息
     * @return 结果
     */
    @Override
    public int insertKgTeacher(KgTeacher kgTeacher)
    {
        kgTeacher.setCreateTime(DateUtils.getNowDate());
        return kgTeacherMapper.insertKgTeacher(kgTeacher);
    }

    /**
     * 修改教师信息
     * 
     * @param kgTeacher 教师信息
     * @return 结果
     */
    @Override
    public int updateKgTeacher(KgTeacher kgTeacher)
    {
        kgTeacher.setUpdateTime(DateUtils.getNowDate());
        return kgTeacherMapper.updateKgTeacher(kgTeacher);
    }

    /**
     * 批量删除教师信息
     * 
     * @param teacherIds 需要删除的教师信息ID
     * @return 结果
     */
    @Override
    public int deleteKgTeacherByIds(Long[] teacherIds)
    {
        return kgTeacherMapper.deleteKgTeacherByIds(teacherIds);
    }

    /**
     * 删除教师信息信息
     * 
     * @param teacherId 教师信息ID
     * @return 结果
     */
    @Override
    public int deleteKgTeacherById(Long teacherId)
    {
        return kgTeacherMapper.deleteKgTeacherById(teacherId);
    }
}

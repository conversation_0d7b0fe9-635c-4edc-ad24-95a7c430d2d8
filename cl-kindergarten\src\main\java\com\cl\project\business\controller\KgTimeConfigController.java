package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgTimeConfig;
import com.cl.project.business.service.IKgTimeConfigService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 时间段配置Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/time-config")
public class KgTimeConfigController extends BaseController
{
    @Autowired
    private IKgTimeConfigService kgTimeConfigService;

    /**
     * 查询时间段配置列表
     */
    @SaCheckPermission("business:config:list")
    @GetMapping("/list")
    public TableDataInfo list(KgTimeConfig kgTimeConfig)
    {
        startPage();
        List<KgTimeConfig> list = kgTimeConfigService.selectKgTimeConfigList(kgTimeConfig);
        return getDataTable(list);
    }

    /**
     * 导出时间段配置列表
     */
    @SaCheckPermission("business:config:export")
    @Log(title = "时间段配置", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgTimeConfig kgTimeConfig)
    {
        List<KgTimeConfig> list = kgTimeConfigService.selectKgTimeConfigList(kgTimeConfig);
        ExcelUtil<KgTimeConfig> util = new ExcelUtil<KgTimeConfig>(KgTimeConfig.class);
        return util.exportExcel(list, "config");
    }

    /**
     * 获取时间段配置详细信息
     */
    @SaCheckPermission("business:config:query")
    @GetMapping(value = "/{configId}")
    public AjaxResult getInfo(@PathVariable("configId") Long configId)
    {
        return AjaxResult.success(kgTimeConfigService.selectKgTimeConfigById(configId));
    }

    /**
     * 新增时间段配置
     */
    @SaCheckPermission("business:config:add")
    @Log(title = "时间段配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgTimeConfig kgTimeConfig)
    {
        return toAjax(kgTimeConfigService.insertKgTimeConfig(kgTimeConfig));
    }

    /**
     * 修改时间段配置
     */
    @SaCheckPermission("business:config:edit")
    @Log(title = "时间段配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgTimeConfig kgTimeConfig)
    {
        return toAjax(kgTimeConfigService.updateKgTimeConfig(kgTimeConfig));
    }

    /**
     * 删除时间段配置
     */
    @SaCheckPermission("business:config:remove")
    @Log(title = "时间段配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{configIds}")
    public AjaxResult remove(@PathVariable Long[] configIds)
    {
        return toAjax(kgTimeConfigService.deleteKgTimeConfigByIds(configIds));
    }
}

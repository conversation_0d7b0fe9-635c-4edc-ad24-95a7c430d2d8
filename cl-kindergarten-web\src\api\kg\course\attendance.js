import request from '@/utils/request'

// 查询托管考勤列表
export function listCourseAttendance(query) {
  return request({
    url: '/business/course-attendance/list',
    method: 'get',
    params: query
  })
}

// 查询托管考勤详细
export function getCourseAttendance(attendanceId) {
  return request({
    url: '/business/course-attendance/' + attendanceId,
    method: 'get'
  })
}

// 新增托管考勤
export function addCourseAttendance(data) {
  return request({
    url: '/business/course-attendance',
    method: 'post',
    data: data
  })
}

// 修改托管考勤
export function updateCourseAttendance(data) {
  return request({
    url: '/business/course-attendance',
    method: 'put',
    data: data
  })
}

// 删除托管考勤
export function delCourseAttendance(attendanceId) {
  return request({
    url: '/business/course-attendance/' + attendanceId,
    method: 'delete'
  })
}

// 导出托管考勤
export function exportCourseAttendance(query) {
  return request({
    url: '/business/course-attendance/export',
    method: 'get',
    params: query
  })
}

// 托管签到
export function courseCheckin(data) {
  return request({
    url: '/business/course-attendance/checkin',
    method: 'post',
    data: data
  })
}

// 托管确认
export function confirmCourseAttendance(attendanceIds) {
  return request({
    url: '/business/course-attendance/confirm',
    method: 'post',
    data: { attendanceIds: attendanceIds }
  })
}

<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>学生详细信息</span>
        <div style="float: right;">
          <el-button type="primary" size="small" @click="handleEdit">编辑</el-button>
          <el-button size="small" @click="handleBack">返回</el-button>
        </div>
      </div>
      
      <!-- 基本信息 -->
      <div class="detail-section">
        <h4 class="section-title">基本信息</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>学生编号：</label>
              <span>{{ studentInfo.studentCode }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>学生姓名：</label>
              <span>{{ studentInfo.studentName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>性别：</label>
              <span>{{ studentInfo.gender === '0' ? '男' : '女' }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>出生日期：</label>
              <span>{{ studentInfo.birthDate }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>身份证号：</label>
              <span>{{ studentInfo.idCard || '未填写' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>联系电话：</label>
              <span>{{ studentInfo.phone || '未填写' }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>所属班级：</label>
              <span>{{ studentInfo.className || '未分配' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>入园日期：</label>
              <span>{{ studentInfo.enrollmentDate }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>学生状态：</label>
              <el-tag :type="getStatusType(studentInfo.status)">
                {{ getStatusText(studentInfo.status) }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="detail-item">
              <label>家庭住址：</label>
              <span>{{ studentInfo.address || '未填写' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 家长信息 -->
      <div class="detail-section">
        <h4 class="section-title">家长信息</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>家长姓名：</label>
              <span>{{ studentInfo.parentName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>家长电话：</label>
              <span>{{ studentInfo.parentPhone }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>微信绑定：</label>
              <el-tag :type="studentInfo.wechatOpenid ? 'success' : 'info'">
                {{ studentInfo.wechatOpenid ? '已绑定' : '未绑定' }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>紧急联系人：</label>
              <span>{{ studentInfo.emergencyContact || '未填写' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>紧急联系电话：</label>
              <span>{{ studentInfo.emergencyPhone || '未填写' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>人脸识别：</label>
              <el-tag :type="studentInfo.faceId ? 'success' : 'info'">
                {{ studentInfo.faceId ? '已录入' : '未录入' }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 考勤统计 -->
      <div class="detail-section">
        <h4 class="section-title">考勤统计</h4>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-number">{{ attendanceStats.totalDays }}</div>
              <div class="stat-label">总出勤天数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-number">{{ attendanceStats.presentDays }}</div>
              <div class="stat-label">正常出勤</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-number">{{ attendanceStats.absentDays }}</div>
              <div class="stat-label">缺勤天数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-number">{{ attendanceStats.attendanceRate }}%</div>
              <div class="stat-label">出勤率</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 费用统计 -->
      <div class="detail-section">
        <h4 class="section-title">费用统计</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-number">¥{{ feeStats.totalAmount }}</div>
              <div class="stat-label">总应缴费用</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-number">¥{{ feeStats.paidAmount }}</div>
              <div class="stat-label">已缴费用</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-number">¥{{ feeStats.unpaidAmount }}</div>
              <div class="stat-label">未缴费用</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 备注信息 -->
      <div class="detail-section">
        <h4 class="section-title">备注信息</h4>
        <div class="detail-item">
          <span>{{ studentInfo.remark || '无备注信息' }}</span>
        </div>
      </div>

      <!-- 系统信息 -->
      <div class="detail-section">
        <h4 class="section-title">系统信息</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>创建人：</label>
              <span>{{ studentInfo.createBy }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ studentInfo.createTime }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>更新时间：</label>
              <span>{{ studentInfo.updateTime }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 相关记录 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>最近考勤记录</span>
            <el-button type="text" style="float: right;" @click="$router.push('/kg/attendance/student')">
              查看更多
            </el-button>
          </div>
          <el-table :data="recentAttendance" style="width: 100%">
            <el-table-column prop="attendanceDate" label="日期" width="100" />
            <el-table-column prop="attendanceStatus" label="状态" width="80">
              <template slot-scope="scope">
                <el-tag :type="getAttendanceStatusType(scope.row.attendanceStatus)" size="mini">
                  {{ getAttendanceStatusText(scope.row.attendanceStatus) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="checkInTime" label="签到时间" />
          </el-table>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>费用记录</span>
            <el-button type="text" style="float: right;" @click="$router.push('/kg/finance/tuition')">
              查看更多
            </el-button>
          </div>
          <el-table :data="recentFees" style="width: 100%">
            <el-table-column prop="billMonth" label="账单月份" width="100" />
            <el-table-column prop="amount" label="金额" width="80" />
            <el-table-column prop="paymentStatus" label="状态">
              <template slot-scope="scope">
                <el-tag :type="scope.row.paymentStatus === 'paid' ? 'success' : 'warning'" size="mini">
                  {{ scope.row.paymentStatus === 'paid' ? '已缴费' : '未缴费' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script>
import { getStudent } from "@/api/kg/student/info";

export default {
  name: "StudentDetail",
  data() {
    return {
      // 学生信息
      studentInfo: {
        studentId: undefined,
        studentCode: '',
        studentName: '',
        gender: '',
        birthDate: '',
        idCard: '',
        phone: '',
        parentName: '',
        parentPhone: '',
        emergencyContact: '',
        emergencyPhone: '',
        address: '',
        className: '',
        enrollmentDate: '',
        status: '',
        faceId: '',
        wechatOpenid: '',
        remark: '',
        createBy: '',
        createTime: '',
        updateTime: ''
      },
      // 考勤统计
      attendanceStats: {
        totalDays: 0,
        presentDays: 0,
        absentDays: 0,
        attendanceRate: 0
      },
      // 费用统计
      feeStats: {
        totalAmount: 0,
        paidAmount: 0,
        unpaidAmount: 0
      },
      // 最近考勤记录
      recentAttendance: [],
      // 最近费用记录
      recentFees: []
    };
  },
  created() {
    const studentId = this.$route.query.studentId || this.$route.params.studentId;
    if (studentId) {
      this.getStudentInfo(studentId);
      this.loadAttendanceStats(studentId);
      this.loadFeeStats(studentId);
      this.loadRecentRecords(studentId);
    }
  },
  methods: {
    // 获取学生信息
    getStudentInfo(studentId) {
      getStudent(studentId).then(response => {
        this.studentInfo = response.data;
      });
    },
    
    // 加载考勤统计
    loadAttendanceStats(studentId) {
      // 模拟数据，实际应该调用API
      this.attendanceStats = {
        totalDays: 120,
        presentDays: 110,
        absentDays: 10,
        attendanceRate: 91.7
      };
    },
    
    // 加载费用统计
    loadFeeStats(studentId) {
      // 模拟数据，实际应该调用API
      this.feeStats = {
        totalAmount: 12000,
        paidAmount: 10000,
        unpaidAmount: 2000
      };
    },
    
    // 加载最近记录
    loadRecentRecords(studentId) {
      // 模拟数据，实际应该调用API
      this.recentAttendance = [
        { attendanceDate: '2025-07-29', attendanceStatus: 'present', checkInTime: '08:30' },
        { attendanceDate: '2025-07-28', attendanceStatus: 'present', checkInTime: '08:25' },
        { attendanceDate: '2025-07-27', attendanceStatus: 'late', checkInTime: '09:10' },
        { attendanceDate: '2025-07-26', attendanceStatus: 'absent', checkInTime: '' }
      ];
      
      this.recentFees = [
        { billMonth: '2025-07', amount: '2000', paymentStatus: 'unpaid' },
        { billMonth: '2025-06', amount: '2000', paymentStatus: 'paid' },
        { billMonth: '2025-05', amount: '2000', paymentStatus: 'paid' }
      ];
    },
    
    // 获取状态类型
    getStatusType(status) {
      const statusMap = {
        '0': 'success',
        '1': 'danger',
        '2': 'warning'
      };
      return statusMap[status] || 'info';
    },
    
    // 获取状态文本
    getStatusText(status) {
      const statusMap = {
        '0': '在园',
        '1': '退园',
        '2': '请假'
      };
      return statusMap[status] || '未知';
    },
    
    // 获取考勤状态类型
    getAttendanceStatusType(status) {
      const statusMap = {
        'present': 'success',
        'absent': 'danger',
        'late': 'warning',
        'early': 'warning'
      };
      return statusMap[status] || 'info';
    },
    
    // 获取考勤状态文本
    getAttendanceStatusText(status) {
      const statusMap = {
        'present': '出勤',
        'absent': '缺勤',
        'late': '迟到',
        'early': '早退'
      };
      return statusMap[status] || '未知';
    },
    
    // 编辑学生
    handleEdit() {
      this.$router.push({
        path: '/kg/student/info/form',
        query: { studentId: this.studentInfo.studentId }
      });
    },
    
    // 返回列表
    handleBack() {
      this.$router.push('/kg/student/info');
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.box-card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.detail-section {
  margin-bottom: 30px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #f0f0f0;
}

.detail-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  
  label {
    font-weight: 500;
    color: #606266;
    min-width: 100px;
    margin-right: 10px;
  }
  
  span {
    color: #303133;
    flex: 1;
  }
}

.stat-card {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  
  .stat-number {
    font-size: 24px;
    font-weight: 600;
    color: #409eff;
    margin-bottom: 8px;
  }
  
  .stat-label {
    font-size: 14px;
    color: #606266;
  }
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgTuitionConfigMapper">
    
    <resultMap type="KgTuitionConfig" id="KgTuitionConfigResult">
        <result property="configId"    column="config_id"    />
        <result property="classType"    column="class_type"    />
        <result property="mealFeePerDay"    column="meal_fee_per_day"    />
        <result property="educationFeePerMonth"    column="education_fee_per_month"    />
        <result property="attendanceThreshold"    column="attendance_threshold"    />
        <result property="halfEducationFee"    column="half_education_fee"    />
        <result property="effectiveDate"    column="effective_date"    />
        <result property="status"    column="status"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgTuitionConfigVo">
        select config_id, class_type, meal_fee_per_day, education_fee_per_month, attendance_threshold, half_education_fee, effective_date, status, com_id, create_by, create_time, update_by, update_time, remark from kg_tuition_config
    </sql>

    <select id="selectKgTuitionConfigList" parameterType="KgTuitionConfig" resultMap="KgTuitionConfigResult">
        <include refid="selectKgTuitionConfigVo"/>
        <where>  
            <if test="classType != null  and classType != ''"> and class_type = #{classType}</if>
            <if test="mealFeePerDay != null "> and meal_fee_per_day = #{mealFeePerDay}</if>
            <if test="educationFeePerMonth != null "> and education_fee_per_month = #{educationFeePerMonth}</if>
            <if test="attendanceThreshold != null "> and attendance_threshold = #{attendanceThreshold}</if>
            <if test="halfEducationFee != null "> and half_education_fee = #{halfEducationFee}</if>
            <if test="effectiveDate != null "> and effective_date = #{effectiveDate}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgTuitionConfigById" parameterType="Long" resultMap="KgTuitionConfigResult">
        <include refid="selectKgTuitionConfigVo"/>
        where config_id = #{configId}
    </select>
        
    <insert id="insertKgTuitionConfig" parameterType="KgTuitionConfig" useGeneratedKeys="true" keyProperty="configId">
        insert into kg_tuition_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="classType != null and classType != ''">class_type,</if>
            <if test="mealFeePerDay != null">meal_fee_per_day,</if>
            <if test="educationFeePerMonth != null">education_fee_per_month,</if>
            <if test="attendanceThreshold != null">attendance_threshold,</if>
            <if test="halfEducationFee != null">half_education_fee,</if>
            <if test="effectiveDate != null">effective_date,</if>
            <if test="status != null">status,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="classType != null and classType != ''">#{classType},</if>
            <if test="mealFeePerDay != null">#{mealFeePerDay},</if>
            <if test="educationFeePerMonth != null">#{educationFeePerMonth},</if>
            <if test="attendanceThreshold != null">#{attendanceThreshold},</if>
            <if test="halfEducationFee != null">#{halfEducationFee},</if>
            <if test="effectiveDate != null">#{effectiveDate},</if>
            <if test="status != null">#{status},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgTuitionConfig" parameterType="KgTuitionConfig">
        update kg_tuition_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="classType != null and classType != ''">class_type = #{classType},</if>
            <if test="mealFeePerDay != null">meal_fee_per_day = #{mealFeePerDay},</if>
            <if test="educationFeePerMonth != null">education_fee_per_month = #{educationFeePerMonth},</if>
            <if test="attendanceThreshold != null">attendance_threshold = #{attendanceThreshold},</if>
            <if test="halfEducationFee != null">half_education_fee = #{halfEducationFee},</if>
            <if test="effectiveDate != null">effective_date = #{effectiveDate},</if>
            <if test="status != null">status = #{status},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where config_id = #{configId}
    </update>

    <delete id="deleteKgTuitionConfigById" parameterType="Long">
        delete from kg_tuition_config where config_id = #{configId}
    </delete>

    <delete id="deleteKgTuitionConfigByIds" parameterType="String">
        delete from kg_tuition_config where config_id in 
        <foreach item="configId" collection="array" open="(" separator="," close=")">
            #{configId}
        </foreach>
    </delete>
    
</mapper>
package com.cl.project.business.service;

import java.util.List;
import com.cl.project.business.domain.KgIncome;

/**
 * 收入记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IKgIncomeService 
{
    /**
     * 查询收入记录
     * 
     * @param incomeId 收入记录ID
     * @return 收入记录
     */
    public KgIncome selectKgIncomeById(Long incomeId);

    /**
     * 查询收入记录列表
     * 
     * @param kgIncome 收入记录
     * @return 收入记录集合
     */
    public List<KgIncome> selectKgIncomeList(KgIncome kgIncome);

    /**
     * 新增收入记录
     * 
     * @param kgIncome 收入记录
     * @return 结果
     */
    public int insertKgIncome(KgIncome kgIncome);

    /**
     * 修改收入记录
     * 
     * @param kgIncome 收入记录
     * @return 结果
     */
    public int updateKgIncome(KgIncome kgIncome);

    /**
     * 批量删除收入记录
     * 
     * @param incomeIds 需要删除的收入记录ID
     * @return 结果
     */
    public int deleteKgIncomeByIds(Long[] incomeIds);

    /**
     * 删除收入记录信息
     * 
     * @param incomeId 收入记录ID
     * @return 结果
     */
    public int deleteKgIncomeById(Long incomeId);
}

package com.cl.project.business.domain;

import java.util.Date;

import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 用户绑定申请记录对象 kg_user_bind_request
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgUserBindRequest extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 申请ID */
    private Long requestId;

    /** 微信openid */
    @Excel(name = "微信openid")
    private String openid;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phone;

    /** 申请绑定的用户类型（teacher教师、admin管理员） */
    @Excel(name = "申请绑定的用户类型", readConverterExp = "t=eacher教师、admin管理员")
    private String userType;

    /** 真实姓名 */
    @Excel(name = "真实姓名")
    private String realName;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String idCard;

    /** 验证码 */
    @Excel(name = "验证码")
    private String verificationCode;

    /** 申请状态（pending待审核、approved已通过、rejected已拒绝） */
    @Excel(name = "申请状态", readConverterExp = "p=ending待审核、approved已通过、rejected已拒绝")
    private String requestStatus;

    /** 申请时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "申请时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date requestTime;

    /** 审核人ID */
    @Excel(name = "审核人ID")
    private Long approvedBy;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date approvalTime;

    /** 审核意见 */
    @Excel(name = "审核意见")
    private String approvalReason;

    /** 匹配到的系统用户ID */
    @Excel(name = "匹配到的系统用户ID")
    private Long matchedUserId;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    public void setRequestId(Long requestId) 
    {
        this.requestId = requestId;
    }

    public Long getRequestId() 
    {
        return requestId;
    }
    public void setOpenid(String openid) 
    {
        this.openid = openid;
    }

    public String getOpenid() 
    {
        return openid;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setUserType(String userType) 
    {
        this.userType = userType;
    }

    public String getUserType() 
    {
        return userType;
    }
    public void setRealName(String realName) 
    {
        this.realName = realName;
    }

    public String getRealName() 
    {
        return realName;
    }
    public void setIdCard(String idCard) 
    {
        this.idCard = idCard;
    }

    public String getIdCard() 
    {
        return idCard;
    }
    public void setVerificationCode(String verificationCode) 
    {
        this.verificationCode = verificationCode;
    }

    public String getVerificationCode() 
    {
        return verificationCode;
    }
    public void setRequestStatus(String requestStatus) 
    {
        this.requestStatus = requestStatus;
    }

    public String getRequestStatus() 
    {
        return requestStatus;
    }
    public void setRequestTime(Date requestTime) 
    {
        this.requestTime = requestTime;
    }

    public Date getRequestTime() 
    {
        return requestTime;
    }
    public void setApprovedBy(Long approvedBy) 
    {
        this.approvedBy = approvedBy;
    }

    public Long getApprovedBy() 
    {
        return approvedBy;
    }
    public void setApprovalTime(Date approvalTime) 
    {
        this.approvalTime = approvalTime;
    }

    public Date getApprovalTime() 
    {
        return approvalTime;
    }
    public void setApprovalReason(String approvalReason) 
    {
        this.approvalReason = approvalReason;
    }

    public String getApprovalReason() 
    {
        return approvalReason;
    }
    public void setMatchedUserId(Long matchedUserId) 
    {
        this.matchedUserId = matchedUserId;
    }

    public Long getMatchedUserId() 
    {
        return matchedUserId;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("requestId", getRequestId())
            .append("openid", getOpenid())
            .append("phone", getPhone())
            .append("userType", getUserType())
            .append("realName", getRealName())
            .append("idCard", getIdCard())
            .append("verificationCode", getVerificationCode())
            .append("requestStatus", getRequestStatus())
            .append("requestTime", getRequestTime())
            .append("approvedBy", getApprovedBy())
            .append("approvalTime", getApprovalTime())
            .append("approvalReason", getApprovalReason())
            .append("matchedUserId", getMatchedUserId())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

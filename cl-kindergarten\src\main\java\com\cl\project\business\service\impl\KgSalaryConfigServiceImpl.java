package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgSalaryConfigMapper;
import com.cl.project.business.domain.KgSalaryConfig;
import com.cl.project.business.service.IKgSalaryConfigService;

/**
 * 工资配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgSalaryConfigServiceImpl implements IKgSalaryConfigService 
{
    @Autowired
    private KgSalaryConfigMapper kgSalaryConfigMapper;

    /**
     * 查询工资配置
     * 
     * @param configId 工资配置ID
     * @return 工资配置
     */
    @Override
    public KgSalaryConfig selectKgSalaryConfigById(Long configId)
    {
        return kgSalaryConfigMapper.selectKgSalaryConfigById(configId);
    }

    /**
     * 查询工资配置列表
     * 
     * @param kgSalaryConfig 工资配置
     * @return 工资配置
     */
    @Override
    public List<KgSalaryConfig> selectKgSalaryConfigList(KgSalaryConfig kgSalaryConfig)
    {
        return kgSalaryConfigMapper.selectKgSalaryConfigList(kgSalaryConfig);
    }

    /**
     * 新增工资配置
     * 
     * @param kgSalaryConfig 工资配置
     * @return 结果
     */
    @Override
    public int insertKgSalaryConfig(KgSalaryConfig kgSalaryConfig)
    {
        kgSalaryConfig.setCreateTime(DateUtils.getNowDate());
        return kgSalaryConfigMapper.insertKgSalaryConfig(kgSalaryConfig);
    }

    /**
     * 修改工资配置
     * 
     * @param kgSalaryConfig 工资配置
     * @return 结果
     */
    @Override
    public int updateKgSalaryConfig(KgSalaryConfig kgSalaryConfig)
    {
        kgSalaryConfig.setUpdateTime(DateUtils.getNowDate());
        return kgSalaryConfigMapper.updateKgSalaryConfig(kgSalaryConfig);
    }

    /**
     * 批量删除工资配置
     * 
     * @param configIds 需要删除的工资配置ID
     * @return 结果
     */
    @Override
    public int deleteKgSalaryConfigByIds(Long[] configIds)
    {
        return kgSalaryConfigMapper.deleteKgSalaryConfigByIds(configIds);
    }

    /**
     * 删除工资配置信息
     * 
     * @param configId 工资配置ID
     * @return 结果
     */
    @Override
    public int deleteKgSalaryConfigById(Long configId)
    {
        return kgSalaryConfigMapper.deleteKgSalaryConfigById(configId);
    }
}

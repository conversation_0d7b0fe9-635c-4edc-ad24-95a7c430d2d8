<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgUserBindRequestMapper">
    
    <resultMap type="KgUserBindRequest" id="KgUserBindRequestResult">
        <result property="requestId"    column="request_id"    />
        <result property="openid"    column="openid"    />
        <result property="phone"    column="phone"    />
        <result property="userType"    column="user_type"    />
        <result property="realName"    column="real_name"    />
        <result property="idCard"    column="id_card"    />
        <result property="verificationCode"    column="verification_code"    />
        <result property="requestStatus"    column="request_status"    />
        <result property="requestTime"    column="request_time"    />
        <result property="approvedBy"    column="approved_by"    />
        <result property="approvalTime"    column="approval_time"    />
        <result property="approvalReason"    column="approval_reason"    />
        <result property="matchedUserId"    column="matched_user_id"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgUserBindRequestVo">
        select request_id, openid, phone, user_type, real_name, id_card, verification_code, request_status, request_time, approved_by, approval_time, approval_reason, matched_user_id, com_id, create_by, create_time, update_by, update_time, remark from kg_user_bind_request
    </sql>

    <select id="selectKgUserBindRequestList" parameterType="KgUserBindRequest" resultMap="KgUserBindRequestResult">
        <include refid="selectKgUserBindRequestVo"/>
        <where>  
            <if test="openid != null  and openid != ''"> and openid = #{openid}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="userType != null  and userType != ''"> and user_type = #{userType}</if>
            <if test="realName != null  and realName != ''"> and real_name like concat('%', #{realName}, '%')</if>
            <if test="idCard != null  and idCard != ''"> and id_card = #{idCard}</if>
            <if test="verificationCode != null  and verificationCode != ''"> and verification_code = #{verificationCode}</if>
            <if test="requestStatus != null  and requestStatus != ''"> and request_status = #{requestStatus}</if>
            <if test="requestTime != null "> and request_time = #{requestTime}</if>
            <if test="approvedBy != null "> and approved_by = #{approvedBy}</if>
            <if test="approvalTime != null "> and approval_time = #{approvalTime}</if>
            <if test="approvalReason != null  and approvalReason != ''"> and approval_reason = #{approvalReason}</if>
            <if test="matchedUserId != null "> and matched_user_id = #{matchedUserId}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgUserBindRequestById" parameterType="Long" resultMap="KgUserBindRequestResult">
        <include refid="selectKgUserBindRequestVo"/>
        where request_id = #{requestId}
    </select>
        
    <insert id="insertKgUserBindRequest" parameterType="KgUserBindRequest" useGeneratedKeys="true" keyProperty="requestId">
        insert into kg_user_bind_request
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="openid != null and openid != ''">openid,</if>
            <if test="phone != null and phone != ''">phone,</if>
            <if test="userType != null and userType != ''">user_type,</if>
            <if test="realName != null">real_name,</if>
            <if test="idCard != null">id_card,</if>
            <if test="verificationCode != null">verification_code,</if>
            <if test="requestStatus != null">request_status,</if>
            <if test="requestTime != null">request_time,</if>
            <if test="approvedBy != null">approved_by,</if>
            <if test="approvalTime != null">approval_time,</if>
            <if test="approvalReason != null">approval_reason,</if>
            <if test="matchedUserId != null">matched_user_id,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="openid != null and openid != ''">#{openid},</if>
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="userType != null and userType != ''">#{userType},</if>
            <if test="realName != null">#{realName},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="verificationCode != null">#{verificationCode},</if>
            <if test="requestStatus != null">#{requestStatus},</if>
            <if test="requestTime != null">#{requestTime},</if>
            <if test="approvedBy != null">#{approvedBy},</if>
            <if test="approvalTime != null">#{approvalTime},</if>
            <if test="approvalReason != null">#{approvalReason},</if>
            <if test="matchedUserId != null">#{matchedUserId},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgUserBindRequest" parameterType="KgUserBindRequest">
        update kg_user_bind_request
        <trim prefix="SET" suffixOverrides=",">
            <if test="openid != null and openid != ''">openid = #{openid},</if>
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="userType != null and userType != ''">user_type = #{userType},</if>
            <if test="realName != null">real_name = #{realName},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="verificationCode != null">verification_code = #{verificationCode},</if>
            <if test="requestStatus != null">request_status = #{requestStatus},</if>
            <if test="requestTime != null">request_time = #{requestTime},</if>
            <if test="approvedBy != null">approved_by = #{approvedBy},</if>
            <if test="approvalTime != null">approval_time = #{approvalTime},</if>
            <if test="approvalReason != null">approval_reason = #{approvalReason},</if>
            <if test="matchedUserId != null">matched_user_id = #{matchedUserId},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where request_id = #{requestId}
    </update>

    <delete id="deleteKgUserBindRequestById" parameterType="Long">
        delete from kg_user_bind_request where request_id = #{requestId}
    </delete>

    <delete id="deleteKgUserBindRequestByIds" parameterType="String">
        delete from kg_user_bind_request where request_id in 
        <foreach item="requestId" collection="array" open="(" separator="," close=")">
            #{requestId}
        </foreach>
    </delete>
    
</mapper>
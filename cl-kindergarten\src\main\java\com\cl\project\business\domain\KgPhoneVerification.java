package com.cl.project.business.domain;

import java.util.Date;

import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 手机号验证码对象 kg_phone_verification
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgPhoneVerification extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 验证ID */
    private Long verificationId;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phone;

    /** 验证码 */
    @Excel(name = "验证码")
    private String verificationCode;

    /** 验证码类型（bind_user绑定用户、login登录、reset_password重置密码） */
    @Excel(name = "验证码类型", readConverterExp = "b=ind_user绑定用户、login登录、reset_password重置密码")
    private String codeType;

    /** 微信openid */
    @Excel(name = "微信openid")
    private String openid;

    /** 发送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发送时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date sendTime;

    /** 过期时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "过期时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expireTime;

    /** 是否已使用（0否 1是） */
    @Excel(name = "是否已使用", readConverterExp = "0=否,1=是")
    private Long isUsed;

    /** 使用时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "使用时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date usedTime;

    /** IP地址 */
    @Excel(name = "IP地址")
    private String ipAddress;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    public void setVerificationId(Long verificationId) 
    {
        this.verificationId = verificationId;
    }

    public Long getVerificationId() 
    {
        return verificationId;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setVerificationCode(String verificationCode) 
    {
        this.verificationCode = verificationCode;
    }

    public String getVerificationCode() 
    {
        return verificationCode;
    }
    public void setCodeType(String codeType) 
    {
        this.codeType = codeType;
    }

    public String getCodeType() 
    {
        return codeType;
    }
    public void setOpenid(String openid) 
    {
        this.openid = openid;
    }

    public String getOpenid() 
    {
        return openid;
    }
    public void setSendTime(Date sendTime) 
    {
        this.sendTime = sendTime;
    }

    public Date getSendTime() 
    {
        return sendTime;
    }
    public void setExpireTime(Date expireTime) 
    {
        this.expireTime = expireTime;
    }

    public Date getExpireTime() 
    {
        return expireTime;
    }
    public void setIsUsed(Long isUsed) 
    {
        this.isUsed = isUsed;
    }

    public Long getIsUsed() 
    {
        return isUsed;
    }
    public void setUsedTime(Date usedTime) 
    {
        this.usedTime = usedTime;
    }

    public Date getUsedTime() 
    {
        return usedTime;
    }
    public void setIpAddress(String ipAddress) 
    {
        this.ipAddress = ipAddress;
    }

    public String getIpAddress() 
    {
        return ipAddress;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("verificationId", getVerificationId())
            .append("phone", getPhone())
            .append("verificationCode", getVerificationCode())
            .append("codeType", getCodeType())
            .append("openid", getOpenid())
            .append("sendTime", getSendTime())
            .append("expireTime", getExpireTime())
            .append("isUsed", getIsUsed())
            .append("usedTime", getUsedTime())
            .append("ipAddress", getIpAddress())
            .append("comId", getComId())
            .append("createTime", getCreateTime())
            .toString();
    }
}

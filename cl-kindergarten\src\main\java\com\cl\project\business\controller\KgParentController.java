package com.cl.project.business.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgParent;
import com.cl.project.business.service.IKgParentService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;
import com.cl.common.utils.StringUtils;

/**
 * 家长信息Controller
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@RestController
@RequestMapping("/business/parent")
public class KgParentController extends BaseController
{
    @Autowired
    private IKgParentService kgParentService;

    /**
     * 查询家长信息列表
     */
    @SaCheckPermission("kg:parent:info:list")
    @GetMapping("/list")
    public TableDataInfo list(KgParent kgParent)
    {
        startPage();
        List<KgParent> list = kgParentService.selectKgParentList(kgParent);
        return getDataTable(list);
    }

    /**
     * 导出家长信息列表
     */
    @SaCheckPermission("kg:parent:info:list")
    @Log(title = "家长信息", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgParent kgParent)
    {
        List<KgParent> list = kgParentService.selectKgParentList(kgParent);
        ExcelUtil<KgParent> util = new ExcelUtil<KgParent>(KgParent.class);
        return util.exportExcel(list, "parent");
    }

    /**
     * 获取家长信息详细信息
     */
    @SaCheckPermission("kg:parent:info:list")
    @GetMapping(value = "/{parentId}")
    public AjaxResult getInfo(@PathVariable("parentId") Long parentId)
    {
        return AjaxResult.success(kgParentService.selectKgParentByParentId(parentId));
    }

    /**
     * 新增家长信息
     */
    @SaCheckPermission("kg:parent:info:add")
    @Log(title = "家长信息", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgParent kgParent)
    {
        // 校验手机号唯一性
        if (!"0".equals(kgParentService.checkParentPhoneUnique(kgParent)))
        {
            return AjaxResult.error("新增家长'" + kgParent.getParentName() + "'失败，手机号码已存在");
        }
        return toAjax(kgParentService.insertKgParent(kgParent));
    }

    /**
     * 修改家长信息
     */
    @SaCheckPermission("kg:parent:info:edit")
    @Log(title = "家长信息", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgParent kgParent)
    {
        // 校验手机号唯一性
        if (!"0".equals(kgParentService.checkParentPhoneUnique(kgParent)))
        {
            return AjaxResult.error("修改家长'" + kgParent.getParentName() + "'失败，手机号码已存在");
        }
        return toAjax(kgParentService.updateKgParent(kgParent));
    }

    /**
     * 删除家长信息
     */
    @SaCheckPermission("kg:parent:info:remove")
    @Log(title = "家长信息", businessType = BusinessType.DELETE)
	@DeleteMapping("/{parentIds}")
    public AjaxResult remove(@PathVariable Long[] parentIds)
    {
        return toAjax(kgParentService.deleteKgParentByParentIds(parentIds));
    }

    /**
     * 根据手机号查询家长信息
     */
    @SaCheckPermission("kg:parent:info:list")
    @GetMapping("/phone/{parentPhone}")
    public AjaxResult getByPhone(@PathVariable("parentPhone") String parentPhone)
    {
        KgParent parent = kgParentService.selectKgParentByPhone(parentPhone);
        return AjaxResult.success(parent);
    }

    /**
     * 根据微信openid查询家长信息
     */
    @SaCheckPermission("kg:parent:info:list")
    @GetMapping("/wechat/{wechatOpenid}")
    public AjaxResult getByWechatOpenid(@PathVariable("wechatOpenid") String wechatOpenid)
    {
        KgParent parent = kgParentService.selectKgParentByWechatOpenid(wechatOpenid);
        return AjaxResult.success(parent);
    }

    /**
     * 批量绑定微信
     */
    @SaCheckPermission("kg:parent:info:edit")
    @Log(title = "家长微信绑定", businessType = BusinessType.UPDATE)
    @PutMapping("/wechat/bind")
    public AjaxResult batchBindWechat(@RequestBody KgParent kgParent)
    {
        Long[] parentIds = {kgParent.getParentId()};
        return toAjax(kgParentService.batchBindWechat(parentIds, kgParent.getWechatOpenid()));
    }

    /**
     * 批量解绑微信
     */
    @SaCheckPermission("kg:parent:info:edit")
    @Log(title = "家长微信解绑", businessType = BusinessType.UPDATE)
    @PutMapping("/wechat/unbind")
    public AjaxResult batchUnbindWechat(@RequestBody Long[] parentIds)
    {
        return toAjax(kgParentService.batchUnbindWechat(parentIds));
    }

    /**
     * 校验家长手机号
     */
    @GetMapping("/checkParentPhoneUnique")
    public AjaxResult checkParentPhoneUnique(KgParent kgParent)
    {
        String uniqueFlag = kgParentService.checkParentPhoneUnique(kgParent);
        return AjaxResult.success("0".equals(uniqueFlag));
    }

    /**
     * 下载家长信息导入模板
     */
    @PostMapping("/importTemplate")
    public AjaxResult importTemplate()
    {
        ExcelUtil<KgParent> util = new ExcelUtil<KgParent>(KgParent.class);
        return util.importTemplateExcel("家长信息数据");
    }

    /**
     * 导入家长信息
     */
    @SaCheckPermission("kg:parent:info:add")
    @Log(title = "家长信息", businessType = BusinessType.IMPORT)
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, boolean updateSupport) throws Exception
    {
        ExcelUtil<KgParent> util = new ExcelUtil<KgParent>(KgParent.class);
        List<KgParent> parentList = util.importExcel(file.getInputStream());
        String operName = "admin"; // 使用默认用户名
        String message = kgParentService.importParent(parentList, updateSupport, operName);
        return AjaxResult.success(message);
    }

    /**
     * 获取家长的子女信息
     */
    @SaCheckPermission("kg:parent:info:list")
    @GetMapping("/{parentId}/children")
    public AjaxResult getChildren(@PathVariable("parentId") Long parentId)
    {
        try {
            List<com.cl.project.business.domain.KgStudent> children = kgParentService.getParentChildren(parentId);
            return AjaxResult.success(children);
        } catch (Exception e) {
            return AjaxResult.error("获取子女信息失败");
        }
    }

    /**
     * 获取家长的费用统计
     */
    @SaCheckPermission("kg:parent:info:list")
    @GetMapping("/{parentId}/feeStats")
    public AjaxResult getFeeStats(@PathVariable("parentId") Long parentId)
    {
        try {
            java.util.Map<String, Object> feeStats = kgParentService.getParentFeeStats(parentId);
            return AjaxResult.success(feeStats);
        } catch (Exception e) {
            return AjaxResult.error("获取费用统计失败");
        }
    }

    /**
     * 获取家长的消息记录
     */
    @SaCheckPermission("kg:parent:info:list")
    @GetMapping("/{parentId}/messages")
    public AjaxResult getMessages(@PathVariable("parentId") Long parentId)
    {
        try {
            startPage(); // 支持分页
            List<com.cl.project.business.domain.KgMessagePush> messages = kgParentService.getParentMessages(parentId);
            return AjaxResult.success(getDataTable(messages));
        } catch (Exception e) {
            return AjaxResult.error("获取消息记录失败");
        }
    }
}

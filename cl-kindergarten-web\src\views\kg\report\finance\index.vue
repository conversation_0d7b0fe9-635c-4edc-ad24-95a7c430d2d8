<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px">
      <el-form-item label="报表类型" prop="reportType">
        <el-select v-model="queryParams.reportType" placeholder="请选择报表类型" clearable size="small">
          <el-option label="收入报表" value="income" />
          <el-option label="支出报表" value="expense" />
          <el-option label="利润报表" value="profit" />
          <el-option label="费用明细" value="fee_detail" />
        </el-select>
      </el-form-item>
      <el-form-item label="统计周期" prop="period">
        <el-select v-model="queryParams.period" placeholder="请选择统计周期" clearable size="small">
          <el-option label="按日统计" value="daily" />
          <el-option label="按月统计" value="monthly" />
          <el-option label="按季度统计" value="quarterly" />
          <el-option label="按年统计" value="yearly" />
        </el-select>
      </el-form-item>
      <el-form-item label="统计日期" prop="dateRange">
        <el-date-picker
          v-model="queryParams.dateRange"
          type="daterange"
          range-separator="至"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
          value-format="yyyy-MM-dd"
          size="small">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="费用类型" prop="feeType" v-if="queryParams.reportType === 'fee_detail'">
        <el-select v-model="queryParams.feeType" placeholder="请选择费用类型" clearable size="small">
          <el-option label="园费" value="tuition" />
          <el-option label="托管费" value="course_fee" />
          <el-option label="餐费" value="meal_fee" />
          <el-option label="其他费用" value="other_fee" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button 
          type="primary" 
          icon="el-icon-search" 
          size="mini" 
          @click="handleQuery"
          v-hasPermi="['kg:report:finance:list']"
        >查询</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
        <el-button 
          type="success" 
          icon="el-icon-download" 
          size="mini" 
          @click="handleExport"
          v-hasPermi="['kg:report:finance:export']"
        >导出</el-button>
        <el-button type="warning" icon="el-icon-printer" size="mini" @click="handlePrint">打印报表</el-button>
      </el-form-item>
    </el-form>

    <!-- 财务概览卡片 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="6">
        <el-card class="summary-card">
          <div class="summary-item">
            <div class="summary-value" style="color: #67C23A;">¥{{ formatMoney(summary.totalIncome) }}</div>
            <div class="summary-label">总收入</div>
            <div class="summary-trend" :class="summary.incomeGrowth >= 0 ? 'trend-up' : 'trend-down'">
              <i :class="summary.incomeGrowth >= 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
              {{ Math.abs(summary.incomeGrowth) }}%
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="summary-card">
          <div class="summary-item">
            <div class="summary-value" style="color: #F56C6C;">¥{{ formatMoney(summary.totalExpense) }}</div>
            <div class="summary-label">总支出</div>
            <div class="summary-trend" :class="summary.expenseGrowth >= 0 ? 'trend-down' : 'trend-up'">
              <i :class="summary.expenseGrowth >= 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
              {{ Math.abs(summary.expenseGrowth) }}%
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="summary-card">
          <div class="summary-item">
            <div class="summary-value" style="color: #409EFF;">¥{{ formatMoney(summary.netProfit) }}</div>
            <div class="summary-label">净利润</div>
            <div class="summary-trend" :class="summary.profitGrowth >= 0 ? 'trend-up' : 'trend-down'">
              <i :class="summary.profitGrowth >= 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
              {{ Math.abs(summary.profitGrowth) }}%
            </div>
          </div>
        </el-card>
      </el-col>
      <el-col :span="6">
        <el-card class="summary-card">
          <div class="summary-item">
            <div class="summary-value" style="color: #E6A23C;">{{ summary.profitRate }}%</div>
            <div class="summary-label">利润率</div>
            <div class="summary-trend" :class="summary.profitRateGrowth >= 0 ? 'trend-up' : 'trend-down'">
              <i :class="summary.profitRateGrowth >= 0 ? 'el-icon-top' : 'el-icon-bottom'"></i>
              {{ Math.abs(summary.profitRateGrowth) }}%
            </div>
          </div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 图表区域 -->
    <el-row :gutter="20" class="mb20">
      <el-col :span="16">
        <el-card title="收支趋势图">
          <div ref="incomeExpenseChart" style="height: 350px;"></div>
        </el-card>
      </el-col>
      <el-col :span="8">
        <el-card title="收入结构">
          <div ref="incomeStructureChart" style="height: 350px;"></div>
        </el-card>
      </el-col>
    </el-row>

    <!-- 详细数据表格 -->
    <el-card title="详细财务数据">
      <el-table v-loading="loading" :data="reportList" border show-summary :summary-method="getSummaries">
        <el-table-column label="统计期间" align="center" prop="period" />
        <el-table-column label="园费收入" align="center" prop="tuitionIncome">
          <template slot-scope="scope">
            <span style="color: #67C23A;">¥{{ formatMoney(scope.row.tuitionIncome) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="托管费收入" align="center" prop="courseFeeIncome">
          <template slot-scope="scope">
            <span style="color: #67C23A;">¥{{ formatMoney(scope.row.courseFeeIncome) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="其他收入" align="center" prop="otherIncome">
          <template slot-scope="scope">
            <span style="color: #67C23A;">¥{{ formatMoney(scope.row.otherIncome) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="总收入" align="center" prop="totalIncome">
          <template slot-scope="scope">
            <span style="color: #67C23A; font-weight: bold;">¥{{ formatMoney(scope.row.totalIncome) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="人员支出" align="center" prop="salaryExpense">
          <template slot-scope="scope">
            <span style="color: #F56C6C;">¥{{ formatMoney(scope.row.salaryExpense) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="运营支出" align="center" prop="operatingExpense">
          <template slot-scope="scope">
            <span style="color: #F56C6C;">¥{{ formatMoney(scope.row.operatingExpense) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="总支出" align="center" prop="totalExpense">
          <template slot-scope="scope">
            <span style="color: #F56C6C; font-weight: bold;">¥{{ formatMoney(scope.row.totalExpense) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="净利润" align="center" prop="netProfit">
          <template slot-scope="scope">
            <span :style="{ color: scope.row.netProfit >= 0 ? '#67C23A' : '#F56C6C', fontWeight: 'bold' }">
              ¥{{ formatMoney(scope.row.netProfit) }}
            </span>
          </template>
        </el-table-column>
        <el-table-column label="利润率" align="center" prop="profitRate">
          <template slot-scope="scope">
            <el-tag :type="getProfitRateType(scope.row.profitRate)">
              {{ scope.row.profitRate }}%
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
          <template slot-scope="scope">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-view"
              @click="handleViewDetail(scope.row)"
              v-hasPermi="['kg:report:finance:query']"
            >查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="total>0"
        :total="total"
        :page.sync="queryParams.pageNum"
        :limit.sync="queryParams.pageSize"
        @pagination="getList"
      />
    </el-card>

    <!-- 财务明细对话框 -->
    <el-dialog title="财务明细" :visible.sync="detailDialog" width="90%" append-to-body>
      <el-tabs v-model="activeTab">
        <el-tab-pane label="收入明细" name="income">
          <el-table :data="incomeDetailList" border>
            <el-table-column label="日期" align="center" prop="date" />
            <el-table-column label="学生姓名" align="center" prop="studentName" />
            <el-table-column label="收费项目" align="center" prop="feeItem" />
            <el-table-column label="收费金额" align="center" prop="amount">
              <template slot-scope="scope">
                <span style="color: #67C23A;">¥{{ formatMoney(scope.row.amount) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="缴费方式" align="center" prop="paymentMethod" />
            <el-table-column label="收费状态" align="center" prop="status">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.kg_payment_status" :value="scope.row.status"/>
              </template>
            </el-table-column>
          </el-table>
        </el-tab-pane>
        <el-tab-pane label="支出明细" name="expense">
          <el-table :data="expenseDetailList" border>
            <el-table-column label="日期" align="center" prop="date" />
            <el-table-column label="支出类型" align="center" prop="expenseType" />
            <el-table-column label="支出项目" align="center" prop="expenseItem" />
            <el-table-column label="支出金额" align="center" prop="amount">
              <template slot-scope="scope">
                <span style="color: #F56C6C;">¥{{ formatMoney(scope.row.amount) }}</span>
              </template>
            </el-table-column>
            <el-table-column label="经办人" align="center" prop="handler" />
            <el-table-column label="备注" align="center" prop="remark" />
          </el-table>
        </el-tab-pane>
      </el-tabs>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getFinanceReport, getFinanceDetail, exportFinanceReport } from "@/api/kg/report/finance";
import * as echarts from 'echarts';

export default {
  name: "FinanceReport",
  dicts: ['kg_payment_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 总条数
      total: 0,
      // 报表数据
      reportList: [],
      // 收入明细数据
      incomeDetailList: [],
      // 支出明细数据
      expenseDetailList: [],
      // 详情对话框
      detailDialog: false,
      // 当前选中的标签页
      activeTab: 'income',
      // 财务概览
      summary: {
        totalIncome: 0,
        totalExpense: 0,
        netProfit: 0,
        profitRate: 0,
        incomeGrowth: 0,
        expenseGrowth: 0,
        profitGrowth: 0,
        profitRateGrowth: 0
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        reportType: 'income',
        period: 'monthly',
        dateRange: [],
        feeType: null
      },
      // 图表实例
      incomeExpenseChart: null,
      incomeStructureChart: null
    };
  },
  created() {
    this.setDefaultDateRange();
    this.getList();
  },
  mounted() {
    this.initCharts();
  },
  beforeDestroy() {
    if (this.incomeExpenseChart) {
      this.incomeExpenseChart.dispose();
    }
    if (this.incomeStructureChart) {
      this.incomeStructureChart.dispose();
    }
  },
  methods: {
    /** 设置默认日期范围（最近12个月） */
    setDefaultDateRange() {
      const end = new Date();
      const start = new Date();
      start.setFullYear(start.getFullYear() - 1);
      this.queryParams.dateRange = [
        start.toISOString().split('T')[0],
        end.toISOString().split('T')[0]
      ];
    },
    /** 查询报表数据 */
    getList() {
      this.loading = true;
      getFinanceReport(this.queryParams).then(response => {
        this.reportList = response.rows;
        this.total = response.total;
        this.summary = response.summary;
        this.loading = false;
        this.updateCharts(response.chartData);
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.setDefaultDateRange();
      this.handleQuery();
    },
    /** 查看明细 */
    handleViewDetail(row) {
      const params = {
        period: row.period,
        reportType: this.queryParams.reportType
      };
      getFinanceDetail(params).then(response => {
        this.incomeDetailList = response.incomeDetails;
        this.expenseDetailList = response.expenseDetails;
        this.detailDialog = true;
      });
    },
    /** 导出报表 */
    handleExport() {
      this.$confirm('是否确认导出财务报表?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        return exportFinanceReport(this.queryParams);
      }).then(response => {
        this.download(response.msg);
      }).catch(() => {});
    },
    /** 打印报表 */
    handlePrint() {
      window.print();
    },
    /** 格式化金额 */
    formatMoney(amount) {
      if (amount == null) return '0.00';
      return Number(amount).toLocaleString('zh-CN', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });
    },
    /** 获取利润率标签类型 */
    getProfitRateType(rate) {
      if (rate >= 20) return 'success';
      if (rate >= 10) return '';
      if (rate >= 0) return 'warning';
      return 'danger';
    },
    /** 表格合计行 */
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        if (['tuitionIncome', 'courseFeeIncome', 'otherIncome', 'totalIncome', 
             'salaryExpense', 'operatingExpense', 'totalExpense', 'netProfit'].includes(column.property)) {
          const values = data.map(item => Number(item[column.property]));
          if (!values.every(value => isNaN(value))) {
            const sum = values.reduce((prev, curr) => {
              const value = Number(curr);
              if (!isNaN(value)) {
                return prev + curr;
              } else {
                return prev;
              }
            }, 0);
            sums[index] = '¥' + this.formatMoney(sum);
          } else {
            sums[index] = '-';
          }
        } else {
          sums[index] = '-';
        }
      });
      return sums;
    },
    /** 初始化图表 */
    initCharts() {
      this.incomeExpenseChart = echarts.init(this.$refs.incomeExpenseChart);
      this.incomeStructureChart = echarts.init(this.$refs.incomeStructureChart);
    },
    /** 更新图表数据 */
    updateCharts(chartData) {
      if (!chartData) return;
      
      // 收支趋势图
      const incomeExpenseOption = {
        title: { text: '收支趋势' },
        tooltip: { trigger: 'axis' },
        legend: { data: ['收入', '支出', '利润'] },
        xAxis: {
          type: 'category',
          data: chartData.periods
        },
        yAxis: {
          type: 'value',
          axisLabel: { formatter: '¥{value}' }
        },
        series: [
          {
            name: '收入',
            data: chartData.incomeData,
            type: 'bar',
            itemStyle: { color: '#67C23A' }
          },
          {
            name: '支出',
            data: chartData.expenseData,
            type: 'bar',
            itemStyle: { color: '#F56C6C' }
          },
          {
            name: '利润',
            data: chartData.profitData,
            type: 'line',
            itemStyle: { color: '#409EFF' }
          }
        ]
      };
      
      // 收入结构饼图
      const incomeStructureOption = {
        title: { text: '收入结构', left: 'center' },
        tooltip: { trigger: 'item' },
        series: [{
          name: '收入结构',
          type: 'pie',
          radius: '60%',
          data: chartData.incomeStructure,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }]
      };
      
      this.incomeExpenseChart.setOption(incomeExpenseOption);
      this.incomeStructureChart.setOption(incomeStructureOption);
    }
  }
};
</script>

<style scoped>
.summary-card {
  text-align: center;
}

.summary-item {
  padding: 20px;
}

.summary-value {
  font-size: 24px;
  font-weight: bold;
}

.summary-label {
  font-size: 14px;
  color: #666;
  margin: 8px 0;
}

.summary-trend {
  font-size: 12px;
}

.trend-up {
  color: #67C23A;
}

.trend-down {
  color: #F56C6C;
}

.mb20 {
  margin-bottom: 20px;
}
</style>

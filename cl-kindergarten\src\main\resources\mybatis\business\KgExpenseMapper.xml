<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgExpenseMapper">
    
    <resultMap type="KgExpense" id="KgExpenseResult">
        <result property="expenseId"    column="expense_id"    />
        <result property="expenseTypeId"    column="expense_type_id"    />
        <result property="expenseDate"    column="expense_date"    />
        <result property="amount"    column="amount"    />
        <result property="payee"    column="payee"    />
        <result property="paymentMethod"    column="payment_method"    />
        <result property="invoiceNo"    column="invoice_no"    />
        <result property="description"    column="description"    />
        <result property="approverId"    column="approver_id"    />
        <result property="approvalStatus"    column="approval_status"    />
        <result property="approvalTime"    column="approval_time"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgExpenseVo">
        select expense_id, expense_type_id, expense_date, amount, payee, payment_method, invoice_no, description, approver_id, approval_status, approval_time, com_id, create_by, create_time, update_by, update_time, remark from kg_expense
    </sql>

    <select id="selectKgExpenseList" parameterType="KgExpense" resultMap="KgExpenseResult">
        <include refid="selectKgExpenseVo"/>
        <where>  
            <if test="expenseTypeId != null "> and expense_type_id = #{expenseTypeId}</if>
            <if test="expenseDate != null "> and expense_date = #{expenseDate}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="payee != null  and payee != ''"> and payee = #{payee}</if>
            <if test="paymentMethod != null  and paymentMethod != ''"> and payment_method = #{paymentMethod}</if>
            <if test="invoiceNo != null  and invoiceNo != ''"> and invoice_no = #{invoiceNo}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="approverId != null "> and approver_id = #{approverId}</if>
            <if test="approvalStatus != null  and approvalStatus != ''"> and approval_status = #{approvalStatus}</if>
            <if test="approvalTime != null "> and approval_time = #{approvalTime}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgExpenseById" parameterType="Long" resultMap="KgExpenseResult">
        <include refid="selectKgExpenseVo"/>
        where expense_id = #{expenseId}
    </select>
        
    <insert id="insertKgExpense" parameterType="KgExpense" useGeneratedKeys="true" keyProperty="expenseId">
        insert into kg_expense
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="expenseTypeId != null">expense_type_id,</if>
            <if test="expenseDate != null">expense_date,</if>
            <if test="amount != null">amount,</if>
            <if test="payee != null">payee,</if>
            <if test="paymentMethod != null">payment_method,</if>
            <if test="invoiceNo != null">invoice_no,</if>
            <if test="description != null">description,</if>
            <if test="approverId != null">approver_id,</if>
            <if test="approvalStatus != null">approval_status,</if>
            <if test="approvalTime != null">approval_time,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="expenseTypeId != null">#{expenseTypeId},</if>
            <if test="expenseDate != null">#{expenseDate},</if>
            <if test="amount != null">#{amount},</if>
            <if test="payee != null">#{payee},</if>
            <if test="paymentMethod != null">#{paymentMethod},</if>
            <if test="invoiceNo != null">#{invoiceNo},</if>
            <if test="description != null">#{description},</if>
            <if test="approverId != null">#{approverId},</if>
            <if test="approvalStatus != null">#{approvalStatus},</if>
            <if test="approvalTime != null">#{approvalTime},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgExpense" parameterType="KgExpense">
        update kg_expense
        <trim prefix="SET" suffixOverrides=",">
            <if test="expenseTypeId != null">expense_type_id = #{expenseTypeId},</if>
            <if test="expenseDate != null">expense_date = #{expenseDate},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="payee != null">payee = #{payee},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="invoiceNo != null">invoice_no = #{invoiceNo},</if>
            <if test="description != null">description = #{description},</if>
            <if test="approverId != null">approver_id = #{approverId},</if>
            <if test="approvalStatus != null">approval_status = #{approvalStatus},</if>
            <if test="approvalTime != null">approval_time = #{approvalTime},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where expense_id = #{expenseId}
    </update>

    <delete id="deleteKgExpenseById" parameterType="Long">
        delete from kg_expense where expense_id = #{expenseId}
    </delete>

    <delete id="deleteKgExpenseByIds" parameterType="String">
        delete from kg_expense where expense_id in 
        <foreach item="expenseId" collection="array" open="(" separator="," close=")">
            #{expenseId}
        </foreach>
    </delete>
    
</mapper>
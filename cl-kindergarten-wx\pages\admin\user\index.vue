<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">用户管理</text>
				</view>
			</view>
		</view>
				<!-- 新增用户按钮 -->
		<view class="add-user-section">
			<view class="add-user-btn" @click="addUser">
				<view class="add-btn-icon">
					<u-icon name="plus" color="#ffffff" size="24"></u-icon>
				</view>
				<text class="add-btn-text">新增用户</text>
			</view>
		</view>
		<!-- 角色筛选 -->
		<view class="filter-section">
			<view class="filter-title">角色筛选</view>
			<view class="filter-buttons">
				<view
					v-for="(role, index) in roleFilter"
					:key="index"
					class="filter-btn"
					:class="{ active: selectedRole === role.value }"
					@click="filterByRole(role.value)"
				>
					<text class="filter-icon">{{ role.icon }}</text>
					<text class="filter-text">{{ role.label }}</text>
				</view>
			</view>
		</view>



		<!-- 用户列表 -->
		<view class="user-list">
			<view v-for="user in filteredUserList" :key="user.id" class="user-item-card">
				<view class="user-header">
					<view class="user-avatar" :class="user.role">{{ user.roleIcon }}</view>
					<view class="user-basic-info">
						<text class="user-name">{{ user.name }}</text>
						<view class="user-meta">
							<text class="role-badge" :class="user.role">{{ user.roleName }}</text>
							<text class="status-badge" :class="user.status">
								{{ getStatusText(user.status) }}
							</text>
						</view>
					</view>
					<view class="user-indicator">
						<text class="last-login-time">{{ formatLastLogin(user.lastLogin) }}</text>
					</view>
				</view>

				<view class="user-details">
					<view class="detail-row">
						<view class="detail-item">
							<text class="detail-label">👤 用户账号</text>
							<text class="detail-value">{{ user.username }}</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">📱 联系电话</text>
							<text class="detail-value">{{ user.phone }}</text>
						</view>
					</view>
					<view class="detail-row">
						<view class="detail-item">
							<text class="detail-label">🕐 最后登录</text>
							<text class="detail-value">{{ user.lastLogin }}</text>
						</view>
						<view class="detail-item">
							<text class="detail-label">🔐 账户状态</text>
							<text class="detail-value" :class="user.status">{{ getStatusText(user.status) }}</text>
						</view>
					</view>
				</view>

				<view class="user-actions">
					<button class="action-btn primary" @click="editUser(user)">
						<text class="btn-icon">✏️</text>
						<text class="btn-text">编辑用户</text>
					</button>
					<button class="action-btn secondary" @click="resetPassword(user)">
						<text class="btn-icon">🔑</text>
						<text class="btn-text">重置密码</text>
					</button>
				</view>
			</view>
		</view>
	</view>
</template>

<script>
import { toast, useRouter } from '@/utils/utils.js'

export default {
	data() {
		return {
			selectedRole: 'all',
			roleFilter: [
				{ label: '全部', value: 'all', icon: '👥' },
				{ label: '管理员', value: 'admin', icon: '👨‍💼' },
				{ label: '教师', value: 'teacher', icon: '👨‍🏫' },
				{ label: '家长', value: 'parent', icon: '👨‍👩‍👧‍👦' }
			],
			userList: [
				{
					id: 1,
					name: '园长',
					username: 'admin',
					role: 'admin',
					roleName: '管理员',
					roleIcon: '👨‍💼',
					phone: '13800138000',
					lastLogin: '2024-11-30 09:15',
					status: 'active'
				},
				{
					id: 2,
					name: '张老师',
					username: 'teacher001',
					role: 'teacher',
					roleName: '教师',
					roleIcon: '👩‍🏫',
					phone: '13800138001',
					lastLogin: '2024-11-30 08:30',
					status: 'active'
				},
				{
					id: 3,
					name: '李老师',
					username: 'teacher002',
					role: 'teacher',
					roleName: '教师',
					roleIcon: '👨‍🏫',
					phone: '13800138002',
					lastLogin: '2024-11-29 17:45',
					status: 'active'
				},
				{
					id: 4,
					name: '张小明家长',
					username: 'parent001',
					role: 'parent',
					roleName: '家长',
					roleIcon: '👨‍👩‍👧‍👦',
					phone: '13800138101',
					lastLogin: '2024-11-29 20:30',
					status: 'active'
				},
				{
					id: 5,
					name: '李小红家长',
					username: 'parent002',
					role: 'parent',
					roleName: '家长',
					roleIcon: '👨‍👩‍👧‍👦',
					phone: '13800138102',
					lastLogin: '2024-11-28 19:15',
					status: 'inactive'
				}
			]
		}
	},
	computed: {
		filteredUserList() {
			if (this.selectedRole === 'all') {
				return this.userList
			}
			return this.userList.filter(user => user.role === this.selectedRole)
		},

		activeUserCount() {
			return this.userList.filter(user => user.status === 'active').length
		},

		teacherCount() {
			return this.userList.filter(user => user.role === 'teacher').length
		}
	},
	methods: {
		goBack() {
			uni.navigateBack()
		},
		
		addUser() {
			useRouter('/pages/admin/user/add', {}, 'navigateTo')
		},
		
		filterByRole(role) {
			this.selectedRole = role
		},
		
		getStatusText(status) {
			const statusMap = {
				active: '正常',
				inactive: '禁用'
			}
			return statusMap[status] || '未知'
		},
		
		resetPassword(user) {
			uni.showModal({
				title: '重置密码',
				content: `确定要重置 ${user.name} 的密码吗？`,
				success: (res) => {
					if (res.confirm) {
						toast(`${user.name} 密码重置成功，新密码已发送`)
					}
				}
			})
		},
		
		editUser(user) {
			toast(`编辑用户: ${user.name}`)
			// useRouter('/pages/admin/user/edit', { id: user.id }, 'navigateTo')
		},

		// 格式化最后登录时间
		formatLastLogin(lastLogin) {
			const now = new Date()
			const loginTime = new Date(lastLogin)
			const diffMs = now - loginTime
			const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
			const diffDays = Math.floor(diffHours / 24)

			if (diffDays > 0) {
				return `${diffDays}天前`
			} else if (diffHours > 0) {
				return `${diffHours}小时前`
			} else {
				return '刚刚'
			}
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 0 30rpx;
	padding-top: var(--status-bar-height, 44rpx);
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.header-content {
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.nav-left, .nav-right {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.15);
	backdrop-filter: blur(10rpx);
	border: 1rpx solid rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
		background: rgba(255, 255, 255, 0.25);
		box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.15);
	}
}

.header-title {
	flex: 1;
	text-align: center;
}

.title-text {
	font-size: 36rpx;
	font-weight: 700;
	color: #ffffff;
}

/* 统计卡片 */
.stats-section {
	margin: 30rpx;
	display: flex;
	gap: 20rpx;
}

.stat-card {
	flex: 1;
	background: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx 20rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 16rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
}

.stat-icon {
	font-size: 32rpx;
}

.stat-info {
	text-align: center;
}

.stat-number {
	display: block;
	font-size: 36rpx;
	font-weight: 700;
	color: #333333;
	margin-bottom: 8rpx;
}

.stat-label {
	display: block;
	font-size: 24rpx;
	color: #666666;
	font-weight: 500;
}

/* 筛选区域 */
.filter-section {
	margin: 0 30rpx 30rpx;
	background: #ffffff;
	border-radius: 20rpx;
	padding: 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
}

.filter-title {
	font-size: 28rpx;
	font-weight: 600;
	color: #333333;
	margin-bottom: 24rpx;
}

.filter-buttons {
	display: flex;
	gap: 16rpx;
	flex-wrap: wrap;
}

.filter-btn {
	padding: 20rpx 30rpx;
	border-radius: 50rpx;
	background: #f8f9fa;
	border: 2rpx solid transparent;
	transition: all 0.3s ease;
	display: flex;
	align-items: center;
	gap: 12rpx;

	&.active {
		background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
		border-color: #667eea;
		transform: translateY(-2rpx);
		box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.3);
	}
}

.filter-icon {
	font-size: 24rpx;
}

.filter-text {
	font-size: 26rpx;
	font-weight: 500;
	color: #666666;

	.filter-btn.active & {
		color: #ffffff;
	}
}

/* 新增用户按钮 */
.add-user-section {
	margin: 20rpx 30rpx 30rpx;
}

.add-user-btn {
	background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
	border-radius: 20rpx;
	padding: 24rpx 30rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	box-shadow: 0 8rpx 30rpx rgba(40, 167, 69, 0.3);
	transition: all 0.3s ease;

	&:active {
		transform: translateY(-2rpx);
		box-shadow: 0 12rpx 40rpx rgba(40, 167, 69, 0.4);
	}
}

.add-btn-icon {
	width: 48rpx;
	height: 48rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
}

.add-btn-text {
	font-size: 32rpx;
	font-weight: 600;
	color: #ffffff;
}

/* 用户列表 */
.user-list {
	padding: 0 30rpx 40rpx;
}

.user-item-card {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 0;
	margin-bottom: 20rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
	border: 1rpx solid rgba(255, 255, 255, 0.8);
	overflow: hidden;
	transition: all 0.3s ease;

	&:active {
		transform: translateY(-4rpx);
		box-shadow: 0 16rpx 40rpx rgba(0, 0, 0, 0.12);
	}
}

.user-header {
	padding: 24rpx;
	display: flex;
	align-items: center;
	gap: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.user-avatar {
	width: 80rpx;
	height: 80rpx;
	border-radius: 20rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 32rpx;
	box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.1);

	&.admin {
		background: linear-gradient(135deg, #e3f2fd, #bbdefb);
	}

	&.teacher {
		background: linear-gradient(135deg, #fff3e0, #ffcc02);
	}

	&.parent {
		background: linear-gradient(135deg, #f3e5f5, #e1bee7);
	}
}

.user-basic-info {
	flex: 1;
}

.user-name {
	font-size: 36rpx;
	font-weight: 700;
	color: #333;
	margin-bottom: 8rpx;
}

.user-meta {
	display: flex;
	gap: 12rpx;
}

.role-badge {
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 20rpx;
	font-weight: 600;

	&.admin {
		background: #e3f2fd;
		color: #1976d2;
	}

	&.teacher {
		background: #fff3e0;
		color: #f57c00;
	}

	&.parent {
		background: #f3e5f5;
		color: #7b1fa2;
	}
}

.status-badge {
	padding: 4rpx 12rpx;
	border-radius: 12rpx;
	font-size: 20rpx;
	font-weight: 600;

	&.active {
		background: #e8f5e8;
		color: #4CAF50;
	}

	&.inactive {
		background: #ffebee;
		color: #f44336;
	}
}

.user-indicator {
	display: flex;
	flex-direction: column;
	align-items: flex-end;
}

.last-login-time {
	font-size: 20rpx;
	color: #999;
	background: #f5f5f5;
	padding: 4rpx 8rpx;
	border-radius: 8rpx;
}

.user-details {
	padding: 24rpx;
	background: #fafafa;
}

.detail-row {
	display: flex;
	gap: 20rpx;
	margin-bottom: 16rpx;

	&:last-child {
		margin-bottom: 0;
	}
}

.detail-item {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 6rpx;
}

.detail-label {
	font-size: 22rpx;
	color: #666;
	font-weight: 500;
}

.detail-value {
	font-size: 26rpx;
	color: #333;
	font-weight: 600;

	&.active {
		color: #4CAF50;
	}

	&.inactive {
		color: #f44336;
	}
}

.user-actions {
	padding: 20rpx 24rpx;
	display: flex;
	gap: 16rpx;
	background: white;
}

.action-btn {
	flex: 1;
	border: none;
	border-radius: 12rpx;
	padding: 20rpx 16rpx;
	display: flex;
	flex-direction: column;
	align-items: center;
	gap: 8rpx;
	transition: all 0.3s ease;

	&.primary {
		background: linear-gradient(135deg, #2196F3, #1976D2);
		color: white;
		box-shadow: 0 4rpx 12rpx rgba(33, 150, 243, 0.3);

		&:active {
			transform: translateY(2rpx);
			box-shadow: 0 2rpx 8rpx rgba(33, 150, 243, 0.3);
		}
	}

	&.secondary {
		background: linear-gradient(135deg, #ff9800, #f57c00);
		color: white;
		box-shadow: 0 4rpx 12rpx rgba(255, 152, 0, 0.3);

		&:active {
			transform: translateY(2rpx);
			box-shadow: 0 2rpx 8rpx rgba(255, 152, 0, 0.3);
		}
	}
}

.btn-icon {
	font-size: 24rpx;
}

.btn-text {
	font-size: 22rpx;
	font-weight: 600;
}
</style>

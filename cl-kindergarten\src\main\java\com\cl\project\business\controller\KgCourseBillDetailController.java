package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgCourseBillDetail;
import com.cl.project.business.service.IKgCourseBillDetailService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 托管费账单明细Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/detail")
public class KgCourseBillDetailController extends BaseController
{
    @Autowired
    private IKgCourseBillDetailService kgCourseBillDetailService;

    /**
     * 查询托管费账单明细列表
     */
    @SaCheckPermission("business:detail:list")
    @GetMapping("/list")
    public TableDataInfo list(KgCourseBillDetail kgCourseBillDetail)
    {
        startPage();
        List<KgCourseBillDetail> list = kgCourseBillDetailService.selectKgCourseBillDetailList(kgCourseBillDetail);
        return getDataTable(list);
    }

    /**
     * 导出托管费账单明细列表
     */
    @SaCheckPermission("business:detail:export")
    @Log(title = "托管费账单明细", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgCourseBillDetail kgCourseBillDetail)
    {
        List<KgCourseBillDetail> list = kgCourseBillDetailService.selectKgCourseBillDetailList(kgCourseBillDetail);
        ExcelUtil<KgCourseBillDetail> util = new ExcelUtil<KgCourseBillDetail>(KgCourseBillDetail.class);
        return util.exportExcel(list, "detail");
    }

    /**
     * 获取托管费账单明细详细信息
     */
    @SaCheckPermission("business:detail:query")
    @GetMapping(value = "/{detailId}")
    public AjaxResult getInfo(@PathVariable("detailId") Long detailId)
    {
        return AjaxResult.success(kgCourseBillDetailService.selectKgCourseBillDetailById(detailId));
    }

    /**
     * 新增托管费账单明细
     */
    @SaCheckPermission("business:detail:add")
    @Log(title = "托管费账单明细", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgCourseBillDetail kgCourseBillDetail)
    {
        return toAjax(kgCourseBillDetailService.insertKgCourseBillDetail(kgCourseBillDetail));
    }

    /**
     * 修改托管费账单明细
     */
    @SaCheckPermission("business:detail:edit")
    @Log(title = "托管费账单明细", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgCourseBillDetail kgCourseBillDetail)
    {
        return toAjax(kgCourseBillDetailService.updateKgCourseBillDetail(kgCourseBillDetail));
    }

    /**
     * 删除托管费账单明细
     */
    @SaCheckPermission("business:detail:remove")
    @Log(title = "托管费账单明细", businessType = BusinessType.DELETE)
	@DeleteMapping("/{detailIds}")
    public AjaxResult remove(@PathVariable Long[] detailIds)
    {
        return toAjax(kgCourseBillDetailService.deleteKgCourseBillDetailByIds(detailIds));
    }
}

package com.cl.project.business.domain;

import java.util.Date;

import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 微信登录日志对象 kg_wechat_login_log
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgWechatLoginLog extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 日志ID */
    private Long logId;

    /** 微信openid */
    @Excel(name = "微信openid")
    private String openid;

    /** 用户类型（parent家长、teacher教师、admin管理员） */
    @Excel(name = "用户类型", readConverterExp = "p=arent家长、teacher教师、admin管理员")
    private String userType;

    /** 绑定的系统用户ID */
    @Excel(name = "绑定的系统用户ID")
    private Long bindUserId;

    /** 登录时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "登录时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date loginTime;

    /** 登录IP */
    @Excel(name = "登录IP")
    private String loginIp;

    /** 登录地点 */
    @Excel(name = "登录地点")
    private String loginLocation;

    /** 设备信息 */
    @Excel(name = "设备信息")
    private String deviceInfo;

    /** 登录状态（success成功、failed失败、unbound未绑定） */
    @Excel(name = "登录状态", readConverterExp = "s=uccess成功、failed失败、unbound未绑定")
    private String loginStatus;

    /** 错误信息 */
    @Excel(name = "错误信息")
    private String errorMessage;

    /** 微信session_key */
    @Excel(name = "微信session_key")
    private String sessionKey;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    public void setLogId(Long logId) 
    {
        this.logId = logId;
    }

    public Long getLogId() 
    {
        return logId;
    }
    public void setOpenid(String openid) 
    {
        this.openid = openid;
    }

    public String getOpenid() 
    {
        return openid;
    }
    public void setUserType(String userType) 
    {
        this.userType = userType;
    }

    public String getUserType() 
    {
        return userType;
    }
    public void setBindUserId(Long bindUserId) 
    {
        this.bindUserId = bindUserId;
    }

    public Long getBindUserId() 
    {
        return bindUserId;
    }
    public void setLoginTime(Date loginTime) 
    {
        this.loginTime = loginTime;
    }

    public Date getLoginTime() 
    {
        return loginTime;
    }
    public void setLoginIp(String loginIp) 
    {
        this.loginIp = loginIp;
    }

    public String getLoginIp() 
    {
        return loginIp;
    }
    public void setLoginLocation(String loginLocation) 
    {
        this.loginLocation = loginLocation;
    }

    public String getLoginLocation() 
    {
        return loginLocation;
    }
    public void setDeviceInfo(String deviceInfo) 
    {
        this.deviceInfo = deviceInfo;
    }

    public String getDeviceInfo() 
    {
        return deviceInfo;
    }
    public void setLoginStatus(String loginStatus) 
    {
        this.loginStatus = loginStatus;
    }

    public String getLoginStatus() 
    {
        return loginStatus;
    }
    public void setErrorMessage(String errorMessage) 
    {
        this.errorMessage = errorMessage;
    }

    public String getErrorMessage() 
    {
        return errorMessage;
    }
    public void setSessionKey(String sessionKey) 
    {
        this.sessionKey = sessionKey;
    }

    public String getSessionKey() 
    {
        return sessionKey;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("logId", getLogId())
            .append("openid", getOpenid())
            .append("userType", getUserType())
            .append("bindUserId", getBindUserId())
            .append("loginTime", getLoginTime())
            .append("loginIp", getLoginIp())
            .append("loginLocation", getLoginLocation())
            .append("deviceInfo", getDeviceInfo())
            .append("loginStatus", getLoginStatus())
            .append("errorMessage", getErrorMessage())
            .append("sessionKey", getSessionKey())
            .append("comId", getComId())
            .append("createTime", getCreateTime())
            .toString();
    }
}

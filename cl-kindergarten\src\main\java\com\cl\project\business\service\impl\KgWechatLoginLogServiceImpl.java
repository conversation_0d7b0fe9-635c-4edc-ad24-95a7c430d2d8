package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgWechatLoginLogMapper;
import com.cl.project.business.domain.KgWechatLoginLog;
import com.cl.project.business.service.IKgWechatLoginLogService;

/**
 * 微信登录日志Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgWechatLoginLogServiceImpl implements IKgWechatLoginLogService 
{
    @Autowired
    private KgWechatLoginLogMapper kgWechatLoginLogMapper;

    /**
     * 查询微信登录日志
     * 
     * @param logId 微信登录日志ID
     * @return 微信登录日志
     */
    @Override
    public KgWechatLoginLog selectKgWechatLoginLogById(Long logId)
    {
        return kgWechatLoginLogMapper.selectKgWechatLoginLogById(logId);
    }

    /**
     * 查询微信登录日志列表
     * 
     * @param kgWechatLoginLog 微信登录日志
     * @return 微信登录日志
     */
    @Override
    public List<KgWechatLoginLog> selectKgWechatLoginLogList(KgWechatLoginLog kgWechatLoginLog)
    {
        return kgWechatLoginLogMapper.selectKgWechatLoginLogList(kgWechatLoginLog);
    }

    /**
     * 新增微信登录日志
     * 
     * @param kgWechatLoginLog 微信登录日志
     * @return 结果
     */
    @Override
    public int insertKgWechatLoginLog(KgWechatLoginLog kgWechatLoginLog)
    {
        kgWechatLoginLog.setCreateTime(DateUtils.getNowDate());
        return kgWechatLoginLogMapper.insertKgWechatLoginLog(kgWechatLoginLog);
    }

    /**
     * 修改微信登录日志
     * 
     * @param kgWechatLoginLog 微信登录日志
     * @return 结果
     */
    @Override
    public int updateKgWechatLoginLog(KgWechatLoginLog kgWechatLoginLog)
    {
        return kgWechatLoginLogMapper.updateKgWechatLoginLog(kgWechatLoginLog);
    }

    /**
     * 批量删除微信登录日志
     * 
     * @param logIds 需要删除的微信登录日志ID
     * @return 结果
     */
    @Override
    public int deleteKgWechatLoginLogByIds(Long[] logIds)
    {
        return kgWechatLoginLogMapper.deleteKgWechatLoginLogByIds(logIds);
    }

    /**
     * 删除微信登录日志信息
     * 
     * @param logId 微信登录日志ID
     * @return 结果
     */
    @Override
    public int deleteKgWechatLoginLogById(Long logId)
    {
        return kgWechatLoginLogMapper.deleteKgWechatLoginLogById(logId);
    }
}

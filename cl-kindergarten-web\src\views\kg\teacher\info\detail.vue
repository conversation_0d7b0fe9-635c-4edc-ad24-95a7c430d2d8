<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>教师详细信息</span>
        <div style="float: right;">
          <el-button type="primary" size="small" @click="handleEdit">编辑</el-button>
          <el-button size="small" @click="handleBack">返回</el-button>
        </div>
      </div>
      
      <!-- 基本信息 -->
      <div class="detail-section">
        <h4 class="section-title">基本信息</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>教师编号：</label>
              <span>{{ teacherInfo.teacherCode }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>教师姓名：</label>
              <span>{{ teacherInfo.teacherName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>性别：</label>
              <span>{{ teacherInfo.gender === '0' ? '男' : '女' }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>联系电话：</label>
              <span>{{ teacherInfo.phone }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>身份证号：</label>
              <span>{{ teacherInfo.idCard || '未填写' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>教师状态：</label>
              <el-tag :type="teacherInfo.status === '0' ? 'success' : 'danger'">
                {{ teacherInfo.status === '0' ? '在职' : '离职' }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 职业信息 -->
      <div class="detail-section">
        <h4 class="section-title">职业信息</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>学历：</label>
              <span>{{ getEducationText(teacherInfo.education) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>专业：</label>
              <span>{{ teacherInfo.major || '未填写' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>职位：</label>
              <span>{{ getPositionText(teacherInfo.position) }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>入职日期：</label>
              <span>{{ teacherInfo.hireDate }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>基本工资：</label>
              <span>¥{{ teacherInfo.baseSalary }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>关联用户：</label>
              <span>{{ teacherInfo.userName || '未关联' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 认证信息 -->
      <div class="detail-section">
        <h4 class="section-title">认证信息</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>人脸识别：</label>
              <el-tag :type="teacherInfo.faceId ? 'success' : 'info'">
                {{ teacherInfo.faceId ? '已录入' : '未录入' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>微信绑定：</label>
              <el-tag :type="teacherInfo.isWechatBound === 1 ? 'success' : 'info'">
                {{ teacherInfo.isWechatBound === 1 ? '已绑定' : '未绑定' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>绑定时间：</label>
              <span>{{ teacherInfo.wechatBindTime || '未绑定' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 考勤统计 -->
      <div class="detail-section">
        <h4 class="section-title">考勤统计</h4>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-number">{{ attendanceStats.totalDays }}</div>
              <div class="stat-label">总工作天数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-number">{{ attendanceStats.presentDays }}</div>
              <div class="stat-label">正常出勤</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-number">{{ attendanceStats.lateDays }}</div>
              <div class="stat-label">迟到次数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-number">{{ attendanceStats.attendanceRate }}%</div>
              <div class="stat-label">出勤率</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 工资统计 -->
      <div class="detail-section">
        <h4 class="section-title">工资统计</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-number">¥{{ salaryStats.currentMonthSalary }}</div>
              <div class="stat-label">本月应发工资</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-number">¥{{ salaryStats.yearTotalSalary }}</div>
              <div class="stat-label">年度总工资</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-number">¥{{ salaryStats.averageSalary }}</div>
              <div class="stat-label">月平均工资</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 备注信息 -->
      <div class="detail-section">
        <h4 class="section-title">备注信息</h4>
        <div class="detail-item">
          <span>{{ teacherInfo.remark || '无备注信息' }}</span>
        </div>
      </div>

      <!-- 系统信息 -->
      <div class="detail-section">
        <h4 class="section-title">系统信息</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>创建人：</label>
              <span>{{ teacherInfo.createBy }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ teacherInfo.createTime }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>更新时间：</label>
              <span>{{ teacherInfo.updateTime }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 相关记录 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>最近考勤记录</span>
            <el-button type="text" style="float: right;" @click="$router.push('/kg/attendance/teacher')">
              查看更多
            </el-button>
          </div>
          <el-table :data="recentAttendance" style="width: 100%">
            <el-table-column prop="attendanceDate" label="日期" width="100" />
            <el-table-column prop="checkInTime" label="签到时间" width="100" />
            <el-table-column prop="checkOutTime" label="签退时间" width="100" />
            <el-table-column prop="workHours" label="工作时长" />
          </el-table>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>课程安排</span>
            <el-button type="text" style="float: right;" @click="$router.push('/kg/course/schedule')">
              查看更多
            </el-button>
          </div>
          <el-table :data="courseSchedule" style="width: 100%">
            <el-table-column prop="courseName" label="课程名称" />
            <el-table-column prop="className" label="班级" width="80" />
            <el-table-column prop="courseTime" label="上课时间" />
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 管理的班级 -->
    <el-card class="box-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>管理的班级</span>
        <el-button type="text" style="float: right;" @click="$router.push('/kg/student/class')">
          查看更多
        </el-button>
      </div>
      <el-table :data="managedClasses" style="width: 100%">
        <el-table-column prop="className" label="班级名称" />
        <el-table-column prop="studentCount" label="学生人数" width="100" />
        <el-table-column prop="role" label="职责" width="100">
          <template slot-scope="scope">
            <el-tag size="mini" :type="scope.row.role === 'head' ? 'primary' : 'success'">
              {{ scope.row.role === 'head' ? '班主任' : '副班主任' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="classStatus" label="班级状态" width="100">
          <template slot-scope="scope">
            <el-tag size="mini" :type="scope.row.classStatus === 'active' ? 'success' : 'info'">
              {{ scope.row.classStatus === 'active' ? '正常' : '停课' }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" />
      </el-table>
    </el-card>
  </div>
</template>

<script>
import { getTeacher } from "@/api/kg/teacher/info";

export default {
  name: "TeacherDetail",
  data() {
    return {
      // 教师信息
      teacherInfo: {
        teacherId: undefined,
        teacherCode: '',
        teacherName: '',
        gender: '',
        phone: '',
        idCard: '',
        education: '',
        major: '',
        hireDate: '',
        position: '',
        baseSalary: 0,
        faceId: '',
        wechatOpenid: '',
        isWechatBound: 0,
        wechatBindTime: '',
        status: '',
        userName: '',
        remark: '',
        createBy: '',
        createTime: '',
        updateTime: ''
      },
      // 考勤统计
      attendanceStats: {
        totalDays: 0,
        presentDays: 0,
        lateDays: 0,
        attendanceRate: 0
      },
      // 工资统计
      salaryStats: {
        currentMonthSalary: 0,
        yearTotalSalary: 0,
        averageSalary: 0
      },
      // 最近考勤记录
      recentAttendance: [],
      // 课程安排
      courseSchedule: [],
      // 管理的班级
      managedClasses: []
    };
  },
  created() {
    const teacherId = this.$route.query.teacherId || this.$route.params.teacherId;
    if (teacherId) {
      this.getTeacherInfo(teacherId);
      this.loadAttendanceStats(teacherId);
      this.loadSalaryStats(teacherId);
      this.loadRecentRecords(teacherId);
    }
  },
  methods: {
    // 获取教师信息
    getTeacherInfo(teacherId) {
      getTeacher(teacherId).then(response => {
        this.teacherInfo = response.data;
      });
    },
    
    // 加载考勤统计
    loadAttendanceStats(teacherId) {
      // 模拟数据，实际应该调用API
      this.attendanceStats = {
        totalDays: 22,
        presentDays: 20,
        lateDays: 2,
        attendanceRate: 90.9
      };
    },
    
    // 加载工资统计
    loadSalaryStats(teacherId) {
      // 模拟数据，实际应该调用API
      this.salaryStats = {
        currentMonthSalary: 5200,
        yearTotalSalary: 58000,
        averageSalary: 4833
      };
    },
    
    // 加载最近记录
    loadRecentRecords(teacherId) {
      // 模拟数据，实际应该调用API
      this.recentAttendance = [
        { attendanceDate: '2025-07-29', checkInTime: '08:00', checkOutTime: '18:00', workHours: '10小时' },
        { attendanceDate: '2025-07-28', checkInTime: '08:10', checkOutTime: '18:05', workHours: '9小时55分' },
        { attendanceDate: '2025-07-27', checkInTime: '08:05', checkOutTime: '18:00', workHours: '9小时55分' },
        { attendanceDate: '2025-07-26', checkInTime: '08:00', checkOutTime: '17:58', workHours: '9小时58分' }
      ];
      
      this.courseSchedule = [
        { courseName: '语言课', className: '大班A', courseTime: '09:00-09:30' },
        { courseName: '数学课', className: '大班A', courseTime: '10:00-10:30' },
        { courseName: '美术课', className: '大班B', courseTime: '14:00-14:30' }
      ];
      
      this.managedClasses = [
        { className: '大班A', studentCount: 25, role: 'head', classStatus: 'active', remark: '班主任' },
        { className: '大班B', studentCount: 22, role: 'assistant', classStatus: 'active', remark: '副班主任' }
      ];
    },
    
    // 获取学历文本
    getEducationText(education) {
      const educationMap = {
        'high_school': '高中及以下',
        'college': '大专',
        'bachelor': '本科',
        'master': '硕士',
        'doctor': '博士'
      };
      return educationMap[education] || '未填写';
    },
    
    // 获取职位文本
    getPositionText(position) {
      const positionMap = {
        'principal': '园长',
        'vice_principal': '副园长',
        'head_teacher': '班主任',
        'assistant_teacher': '副班主任',
        'subject_teacher': '任课教师',
        'nurse': '保育员',
        'logistics': '后勤'
      };
      return positionMap[position] || '未填写';
    },
    
    // 编辑教师
    handleEdit() {
      this.$router.push({
        path: '/kg/teacher/info/form',
        query: { teacherId: this.teacherInfo.teacherId }
      });
    },
    
    // 返回列表
    handleBack() {
      this.$router.push('/kg/teacher/info');
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.box-card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.detail-section {
  margin-bottom: 30px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #f0f0f0;
}

.detail-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  
  label {
    font-weight: 500;
    color: #606266;
    min-width: 100px;
    margin-right: 10px;
  }
  
  span {
    color: #303133;
    flex: 1;
  }
}

.stat-card {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  
  .stat-number {
    font-size: 24px;
    font-weight: 600;
    color: #409eff;
    margin-bottom: 8px;
  }
  
  .stat-label {
    font-size: 14px;
    color: #606266;
  }
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>

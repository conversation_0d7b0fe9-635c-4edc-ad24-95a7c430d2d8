package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import com.cl.common.utils.StringUtils;
import com.cl.common.exception.CustomException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgParentMapper;
import com.cl.project.business.mapper.KgStudentMapper;
import com.cl.project.business.mapper.KgClassMapper;
import com.cl.project.business.domain.KgParent;
import com.cl.project.business.domain.KgStudent;
import com.cl.project.business.domain.KgClass;
import com.cl.project.business.service.IKgParentService;

/**
 * 家长信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
@Service
public class KgParentServiceImpl implements IKgParentService 
{
    @Autowired
    private KgParentMapper kgParentMapper;
    
    @Autowired
    private KgStudentMapper kgStudentMapper;
    
    @Autowired
    private KgClassMapper kgClassMapper;

    /**
     * 查询家长信息
     * 
     * @param parentId 家长信息主键
     * @return 家长信息
     */
    @Override
    public KgParent selectKgParentByParentId(Long parentId)
    {
        return kgParentMapper.selectKgParentByParentId(parentId);
    }

    /**
     * 查询家长信息列表
     * 
     * @param kgParent 家长信息
     * @return 家长信息
     */
    @Override
    public List<KgParent> selectKgParentList(KgParent kgParent)
    {
        return kgParentMapper.selectKgParentList(kgParent);
    }

    /**
     * 新增家长信息
     * 
     * @param kgParent 家长信息
     * @return 结果
     */
    @Override
    public int insertKgParent(KgParent kgParent)
    {
        kgParent.setCreateTime(DateUtils.getNowDate());
        return kgParentMapper.insertKgParent(kgParent);
    }

    /**
     * 修改家长信息
     * 
     * @param kgParent 家长信息
     * @return 结果
     */
    @Override
    public int updateKgParent(KgParent kgParent)
    {
        kgParent.setUpdateTime(DateUtils.getNowDate());
        return kgParentMapper.updateKgParent(kgParent);
    }

    /**
     * 批量删除家长信息
     * 
     * @param parentIds 需要删除的家长信息主键
     * @return 结果
     */
    @Override
    public int deleteKgParentByParentIds(Long[] parentIds)
    {
        return kgParentMapper.deleteKgParentByParentIds(parentIds);
    }

    /**
     * 删除家长信息信息
     * 
     * @param parentId 家长信息主键
     * @return 结果
     */
    @Override
    public int deleteKgParentByParentId(Long parentId)
    {
        return kgParentMapper.deleteKgParentByParentId(parentId);
    }

    /**
     * 根据手机号查询家长信息
     * 
     * @param parentPhone 家长手机号
     * @return 家长信息
     */
    @Override
    public KgParent selectKgParentByPhone(String parentPhone)
    {
        return kgParentMapper.selectKgParentByPhone(parentPhone);
    }

    /**
     * 根据微信openid查询家长信息
     * 
     * @param wechatOpenid 微信openid
     * @return 家长信息
     */
    @Override
    public KgParent selectKgParentByWechatOpenid(String wechatOpenid)
    {
        return kgParentMapper.selectKgParentByWechatOpenid(wechatOpenid);
    }

    /**
     * 批量绑定微信
     * 
     * @param parentIds 家长ID数组
     * @param wechatOpenid 微信openid
     * @return 结果
     */
    @Override
    public int batchBindWechat(Long[] parentIds, String wechatOpenid)
    {
        if (StringUtils.isEmpty(wechatOpenid))
        {
            throw new CustomException("微信openid不能为空");
        }
        return kgParentMapper.updateParentBindStatus(parentIds, "1");
    }

    /**
     * 批量解绑微信
     * 
     * @param parentIds 家长ID数组
     * @return 结果
     */
    @Override
    public int batchUnbindWechat(Long[] parentIds)
    {
        return kgParentMapper.updateParentBindStatus(parentIds, "0");
    }

    /**
     * 校验家长手机号是否唯一
     * 
     * @param kgParent 家长信息
     * @return 结果
     */
    @Override
    public String checkParentPhoneUnique(KgParent kgParent)
    {
        Long parentId = StringUtils.isNull(kgParent.getParentId()) ? -1L : kgParent.getParentId();
        KgParent info = kgParentMapper.selectKgParentByPhone(kgParent.getParentPhone());
        if (StringUtils.isNotNull(info) && info.getParentId().longValue() != parentId.longValue())
        {
            return "1"; // 不唯一
        }
        return "0"; // 唯一
    }

    /**
     * 导入家长数据
     * 
     * @param parentList 家长数据列表
     * @param isUpdateSupport 是否更新支持
     * @param operName 操作用户
     * @return 结果
     */
    @Override
    public String importParent(List<KgParent> parentList, Boolean isUpdateSupport, String operName)
    {
        if (StringUtils.isNull(parentList) || parentList.size() == 0)
        {
            throw new CustomException("导入家长数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        for (KgParent kgParent : parentList)
        {
            try
            {
                // 验证是否存在这个家长
                KgParent p = kgParentMapper.selectKgParentByPhone(kgParent.getParentPhone());
                if (StringUtils.isNull(p))
                {
                    kgParent.setCreateBy(operName);
                    this.insertKgParent(kgParent);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、家长 " + kgParent.getParentName() + " 导入成功");
                }
                else if (isUpdateSupport)
                {
                    kgParent.setParentId(p.getParentId());
                    kgParent.setUpdateBy(operName);
                    this.updateKgParent(kgParent);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、家长 " + kgParent.getParentName() + " 更新成功");
                }
                else
                {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、家长 " + kgParent.getParentName() + " 已存在");
                }
            }
            catch (Exception e)
            {
                failureNum++;
                String msg = "<br/>" + failureNum + "、家长 " + kgParent.getParentName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
            }
        }
        if (failureNum > 0)
        {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new CustomException(failureMsg.toString());
        }
        else
        {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

    /**
     * 获取家长的子女信息
     * 
     * @param parentId 家长主键
     * @return 子女信息集合
     */
    @Override
    public List<KgStudent> getParentChildren(Long parentId)
    {
        // 获取家长信息
        KgParent parent = kgParentMapper.selectKgParentByParentId(parentId);
        if (parent == null || StringUtils.isEmpty(parent.getParentPhone())) {
            return new java.util.ArrayList<>();
        }
        
        // 根据家长电话查询子女信息
        KgStudent queryStudent = new KgStudent();
        queryStudent.setParentPhone(parent.getParentPhone());
        List<KgStudent> students = kgStudentMapper.selectKgStudentList(queryStudent);
        
        // 为每个学生设置班级名称
        for (KgStudent student : students) {
            if (student.getClassId() != null) {
                KgClass kgClass = kgClassMapper.selectKgClassById(student.getClassId());
                if (kgClass != null) {
                    student.setClassName(kgClass.getClassName());
                }
            }
        }
        
        return students;
    }

    /**
     * 获取家长的费用统计
     * 
     * @param parentId 家长主键
     * @return 费用统计信息
     */
    @Override
    public java.util.Map<String, Object> getParentFeeStats(Long parentId)
    {
        // TODO: 实现获取家长费用统计的逻辑
        // 这里需要根据费用表统计该家长的总费用、已缴费用、未缴费用
        java.util.Map<String, Object> feeStats = new java.util.HashMap<>();
        feeStats.put("totalAmount", 0);
        feeStats.put("paidAmount", 0);
        feeStats.put("unpaidAmount", 0);
        return feeStats;
    }

    /**
     * 获取家长的消息记录
     * 
     * @param parentId 家长主键
     * @return 消息记录集合
     */
    @Override
    public List<com.cl.project.business.domain.KgMessagePush> getParentMessages(Long parentId)
    {
        // TODO: 实现获取家长消息记录的逻辑
        // 这里需要根据消息推送表查询该家长的消息记录
        // 暂时返回空列表
        return new java.util.ArrayList<>();
    }
}

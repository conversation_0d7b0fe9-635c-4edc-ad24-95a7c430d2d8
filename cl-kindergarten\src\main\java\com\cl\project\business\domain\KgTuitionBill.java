package com.cl.project.business.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 园费账单对象 kg_tuition_bill
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgTuitionBill extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 账单ID */
    private Long billId;

    /** 幼儿ID，关联kg_student.student_id */
    @Excel(name = "幼儿ID，关联kg_student.student_id")
    private Long studentId;

    /** 班级ID，关联kg_class.class_id */
    @Excel(name = "班级ID，关联kg_class.class_id")
    private Long classId;

    /** 账单年份 */
    @Excel(name = "账单年份")
    private Long billYear;

    /** 账单月份 */
    @Excel(name = "账单月份")
    private Long billMonth;

    /** 当月总天数 */
    @Excel(name = "当月总天数")
    private Long totalDays;

    /** 出勤天数 */
    @Excel(name = "出勤天数")
    private Long attendanceDays;

    /** 出勤率 */
    @Excel(name = "出勤率")
    private BigDecimal attendanceRate;

    /** 餐费总额 */
    @Excel(name = "餐费总额")
    private BigDecimal mealFeeTotal;

    /** 已用餐费 */
    @Excel(name = "已用餐费")
    private BigDecimal mealFeeUsed;

    /** 餐费余额 */
    @Excel(name = "餐费余额")
    private BigDecimal mealFeeBalance;

    /** 保教费总额 */
    @Excel(name = "保教费总额")
    private BigDecimal educationFeeTotal;

    /** 已用保教费 */
    @Excel(name = "已用保教费")
    private BigDecimal educationFeeUsed;

    /** 保教费余额 */
    @Excel(name = "保教费余额")
    private BigDecimal educationFeeBalance;

    /** 总余额 */
    @Excel(name = "总余额")
    private BigDecimal totalBalance;

    /** 下月预交费用 */
    @Excel(name = "下月预交费用")
    private BigDecimal nextMonthPrepay;

    /** 实际缴费金额 */
    @Excel(name = "实际缴费金额")
    private BigDecimal actualPayment;

    /** 账单状态（generated已生成、sent已发送、paid已支付） */
    @Excel(name = "账单状态", readConverterExp = "g=enerated已生成、sent已发送、paid已支付")
    private String billStatus;

    /** 发送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发送时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date sentTime;

    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date paidTime;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    // ========== 业务逻辑所需字段 ==========
    /** 餐费 */
    private BigDecimal mealFee;
    
    /** 保教费 */
    private BigDecimal educationFee;
    
    /** 管理费 */
    private BigDecimal managementFee;
    
    /** 总费用 */
    private BigDecimal totalFee;
    
    /** 余额结转 */
    private BigDecimal balanceCarryover;
    
    /** 实际应付 */
    private BigDecimal actualPayable;
    
    /** 预交费用 */
    private BigDecimal advancePayment;

    // ========== 关联对象字段，用于前端显示 ==========
    /** 学生姓名 - 关联显示 */
    private String studentName;
    
    /** 班级名称 - 关联显示 */
    private String className;

    public void setBillId(Long billId) 
    {
        this.billId = billId;
    }

    public Long getBillId() 
    {
        return billId;
    }
    public void setStudentId(Long studentId) 
    {
        this.studentId = studentId;
    }

    public Long getStudentId() 
    {
        return studentId;
    }
    public void setClassId(Long classId) 
    {
        this.classId = classId;
    }

    public Long getClassId() 
    {
        return classId;
    }
    public void setBillYear(Long billYear) 
    {
        this.billYear = billYear;
    }

    public Long getBillYear() 
    {
        return billYear;
    }
    public void setBillMonth(Long billMonth) 
    {
        this.billMonth = billMonth;
    }

    // 兼容String类型的设置方法
    public void setBillMonth(String billMonth) 
    {
        if (billMonth != null && !billMonth.isEmpty()) {
            this.billMonth = Long.parseLong(billMonth);
        }
    }

    public Long getBillMonth() 
    {
        return billMonth;
    }
    public void setTotalDays(Long totalDays) 
    {
        this.totalDays = totalDays;
    }

    public Long getTotalDays() 
    {
        return totalDays;
    }
    public void setAttendanceDays(Long attendanceDays) 
    {
        this.attendanceDays = attendanceDays;
    }

    // 兼容Integer类型的设置方法
    public void setAttendanceDays(Integer attendanceDays) 
    {
        if (attendanceDays != null) {
            this.attendanceDays = attendanceDays.longValue();
        }
    }

    public Long getAttendanceDays() 
    {
        return attendanceDays;
    }
    public void setAttendanceRate(BigDecimal attendanceRate) 
    {
        this.attendanceRate = attendanceRate;
    }

    public BigDecimal getAttendanceRate() 
    {
        return attendanceRate;
    }
    public void setMealFeeTotal(BigDecimal mealFeeTotal) 
    {
        this.mealFeeTotal = mealFeeTotal;
    }

    public BigDecimal getMealFeeTotal() 
    {
        return mealFeeTotal;
    }
    public void setMealFeeUsed(BigDecimal mealFeeUsed) 
    {
        this.mealFeeUsed = mealFeeUsed;
    }

    public BigDecimal getMealFeeUsed() 
    {
        return mealFeeUsed;
    }
    public void setMealFeeBalance(BigDecimal mealFeeBalance) 
    {
        this.mealFeeBalance = mealFeeBalance;
    }

    public BigDecimal getMealFeeBalance() 
    {
        return mealFeeBalance;
    }
    public void setEducationFeeTotal(BigDecimal educationFeeTotal) 
    {
        this.educationFeeTotal = educationFeeTotal;
    }

    public BigDecimal getEducationFeeTotal() 
    {
        return educationFeeTotal;
    }
    public void setEducationFeeUsed(BigDecimal educationFeeUsed) 
    {
        this.educationFeeUsed = educationFeeUsed;
    }

    public BigDecimal getEducationFeeUsed() 
    {
        return educationFeeUsed;
    }
    public void setEducationFeeBalance(BigDecimal educationFeeBalance) 
    {
        this.educationFeeBalance = educationFeeBalance;
    }

    public BigDecimal getEducationFeeBalance() 
    {
        return educationFeeBalance;
    }
    public void setTotalBalance(BigDecimal totalBalance) 
    {
        this.totalBalance = totalBalance;
    }

    public BigDecimal getTotalBalance() 
    {
        return totalBalance;
    }
    public void setNextMonthPrepay(BigDecimal nextMonthPrepay) 
    {
        this.nextMonthPrepay = nextMonthPrepay;
    }

    public BigDecimal getNextMonthPrepay() 
    {
        return nextMonthPrepay;
    }
    public void setActualPayment(BigDecimal actualPayment) 
    {
        this.actualPayment = actualPayment;
    }

    public BigDecimal getActualPayment() 
    {
        return actualPayment;
    }
    public void setBillStatus(String billStatus) 
    {
        this.billStatus = billStatus;
    }

    public String getBillStatus() 
    {
        return billStatus;
    }
    public void setSentTime(Date sentTime) 
    {
        this.sentTime = sentTime;
    }

    public Date getSentTime() 
    {
        return sentTime;
    }
    public void setPaidTime(Date paidTime) 
    {
        this.paidTime = paidTime;
    }

    public Date getPaidTime() 
    {
        return paidTime;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    // ========== 业务逻辑所需字段的 getter/setter ==========
    public BigDecimal getMealFee() {
        return mealFee;
    }

    public void setMealFee(BigDecimal mealFee) {
        this.mealFee = mealFee;
    }

    public BigDecimal getEducationFee() {
        return educationFee;
    }

    public void setEducationFee(BigDecimal educationFee) {
        this.educationFee = educationFee;
    }

    public BigDecimal getManagementFee() {
        return managementFee;
    }

    public void setManagementFee(BigDecimal managementFee) {
        this.managementFee = managementFee;
    }

    public BigDecimal getTotalFee() {
        return totalFee;
    }

    public void setTotalFee(BigDecimal totalFee) {
        this.totalFee = totalFee;
    }

    public BigDecimal getBalanceCarryover() {
        return balanceCarryover;
    }

    public void setBalanceCarryover(BigDecimal balanceCarryover) {
        this.balanceCarryover = balanceCarryover;
    }

    public BigDecimal getActualPayable() {
        return actualPayable;
    }

    public void setActualPayable(BigDecimal actualPayable) {
        this.actualPayable = actualPayable;
    }

    public BigDecimal getAdvancePayment() {
        return advancePayment;
    }

    public void setAdvancePayment(BigDecimal advancePayment) {
        this.advancePayment = advancePayment;
    }

    // ========== 关联对象字段的 getter/setter ==========
    public String getStudentName() {
        return studentName;
    }

    public void setStudentName(String studentName) {
        this.studentName = studentName;
    }

    public String getClassName() {
        return className;
    }

    public void setClassName(String className) {
        this.className = className;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("billId", getBillId())
            .append("studentId", getStudentId())
            .append("classId", getClassId())
            .append("billYear", getBillYear())
            .append("billMonth", getBillMonth())
            .append("totalDays", getTotalDays())
            .append("attendanceDays", getAttendanceDays())
            .append("attendanceRate", getAttendanceRate())
            .append("mealFeeTotal", getMealFeeTotal())
            .append("mealFeeUsed", getMealFeeUsed())
            .append("mealFeeBalance", getMealFeeBalance())
            .append("educationFeeTotal", getEducationFeeTotal())
            .append("educationFeeUsed", getEducationFeeUsed())
            .append("educationFeeBalance", getEducationFeeBalance())
            .append("totalBalance", getTotalBalance())
            .append("nextMonthPrepay", getNextMonthPrepay())
            .append("actualPayment", getActualPayment())
            .append("billStatus", getBillStatus())
            .append("sentTime", getSentTime())
            .append("paidTime", getPaidTime())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

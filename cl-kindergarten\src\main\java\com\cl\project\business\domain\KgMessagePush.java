package com.cl.project.business.domain;

import java.util.Date;

import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 消息推送记录对象 kg_message_push
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgMessagePush extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 推送ID */
    private Long pushId;

    /** 消息类型（tuition_bill园费账单、course_bill托管费账单、attendance_notice考勤通知） */
    @Excel(name = "消息类型", readConverterExp = "t=uition_bill园费账单、course_bill托管费账单、attendance_notice考勤通知")
    private String messageType;

    /** 接收者类型（student学生、teacher教师、parent家长） */
    @Excel(name = "接收者类型", readConverterExp = "s=tudent学生、teacher教师、parent家长")
    private String recipientType;

    /** 接收者ID */
    @Excel(name = "接收者ID")
    private Long recipientId;

    /** 微信openid */
    @Excel(name = "微信openid")
    private String openid;

    /** 消息标题 */
    @Excel(name = "消息标题")
    private String title;

    /** 消息内容 */
    @Excel(name = "消息内容")
    private String content;

    /** 模板ID */
    @Excel(name = "模板ID")
    private String templateId;

    /** 推送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "推送时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date pushTime;

    /** 推送状态（pending待推送、success成功、failed失败） */
    @Excel(name = "推送状态", readConverterExp = "p=ending待推送、success成功、failed失败")
    private String pushStatus;

    /** 错误信息 */
    @Excel(name = "错误信息")
    private String errorMessage;

    /** 阅读状态（0未读 1已读） */
    @Excel(name = "阅读状态", readConverterExp = "0=未读,1=已读")
    private Long readStatus;

    /** 阅读时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "阅读时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date readTime;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    public void setPushId(Long pushId) 
    {
        this.pushId = pushId;
    }

    public Long getPushId() 
    {
        return pushId;
    }
    public void setMessageType(String messageType) 
    {
        this.messageType = messageType;
    }

    public String getMessageType() 
    {
        return messageType;
    }
    public void setRecipientType(String recipientType) 
    {
        this.recipientType = recipientType;
    }

    public String getRecipientType() 
    {
        return recipientType;
    }
    public void setRecipientId(Long recipientId) 
    {
        this.recipientId = recipientId;
    }

    public Long getRecipientId() 
    {
        return recipientId;
    }
    public void setOpenid(String openid) 
    {
        this.openid = openid;
    }

    public String getOpenid() 
    {
        return openid;
    }
    public void setTitle(String title) 
    {
        this.title = title;
    }

    public String getTitle() 
    {
        return title;
    }
    public void setContent(String content) 
    {
        this.content = content;
    }

    public String getContent() 
    {
        return content;
    }
    public void setTemplateId(String templateId) 
    {
        this.templateId = templateId;
    }

    public String getTemplateId() 
    {
        return templateId;
    }
    public void setPushTime(Date pushTime) 
    {
        this.pushTime = pushTime;
    }

    public Date getPushTime() 
    {
        return pushTime;
    }
    public void setPushStatus(String pushStatus) 
    {
        this.pushStatus = pushStatus;
    }

    public String getPushStatus() 
    {
        return pushStatus;
    }
    public void setErrorMessage(String errorMessage) 
    {
        this.errorMessage = errorMessage;
    }

    public String getErrorMessage() 
    {
        return errorMessage;
    }
    public void setReadStatus(Long readStatus) 
    {
        this.readStatus = readStatus;
    }

    public Long getReadStatus() 
    {
        return readStatus;
    }
    public void setReadTime(Date readTime) 
    {
        this.readTime = readTime;
    }

    public Date getReadTime() 
    {
        return readTime;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("pushId", getPushId())
            .append("messageType", getMessageType())
            .append("recipientType", getRecipientType())
            .append("recipientId", getRecipientId())
            .append("openid", getOpenid())
            .append("title", getTitle())
            .append("content", getContent())
            .append("templateId", getTemplateId())
            .append("pushTime", getPushTime())
            .append("pushStatus", getPushStatus())
            .append("errorMessage", getErrorMessage())
            .append("readStatus", getReadStatus())
            .append("readTime", getReadTime())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

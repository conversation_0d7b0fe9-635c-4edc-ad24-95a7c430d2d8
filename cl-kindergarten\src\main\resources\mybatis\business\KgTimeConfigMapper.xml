<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgTimeConfigMapper">
    
    <resultMap type="KgTimeConfig" id="KgTimeConfigResult">
        <result property="configId"    column="config_id"    />
        <result property="configName"    column="config_name"    />
        <result property="timeType"    column="time_type"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="isActive"    column="is_active"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgTimeConfigVo">
        select config_id, config_name, time_type, start_time, end_time, is_active, com_id, create_by, create_time, update_by, update_time, remark from kg_time_config
    </sql>

    <select id="selectKgTimeConfigList" parameterType="KgTimeConfig" resultMap="KgTimeConfigResult">
        <include refid="selectKgTimeConfigVo"/>
        <where>  
            <if test="configName != null  and configName != ''"> and config_name like concat('%', #{configName}, '%')</if>
            <if test="timeType != null  and timeType != ''"> and time_type = #{timeType}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="isActive != null "> and is_active = #{isActive}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgTimeConfigById" parameterType="Long" resultMap="KgTimeConfigResult">
        <include refid="selectKgTimeConfigVo"/>
        where config_id = #{configId}
    </select>
        
    <insert id="insertKgTimeConfig" parameterType="KgTimeConfig" useGeneratedKeys="true" keyProperty="configId">
        insert into kg_time_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configName != null and configName != ''">config_name,</if>
            <if test="timeType != null and timeType != ''">time_type,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="isActive != null">is_active,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configName != null and configName != ''">#{configName},</if>
            <if test="timeType != null and timeType != ''">#{timeType},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="isActive != null">#{isActive},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgTimeConfig" parameterType="KgTimeConfig">
        update kg_time_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="configName != null and configName != ''">config_name = #{configName},</if>
            <if test="timeType != null and timeType != ''">time_type = #{timeType},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="isActive != null">is_active = #{isActive},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where config_id = #{configId}
    </update>

    <delete id="deleteKgTimeConfigById" parameterType="Long">
        delete from kg_time_config where config_id = #{configId}
    </delete>

    <delete id="deleteKgTimeConfigByIds" parameterType="String">
        delete from kg_time_config where config_id in 
        <foreach item="configId" collection="array" open="(" separator="," close=")">
            #{configId}
        </foreach>
    </delete>
    
</mapper>
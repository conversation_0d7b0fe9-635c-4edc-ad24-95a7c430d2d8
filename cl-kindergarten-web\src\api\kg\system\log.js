import request from '@/utils/request'

// 查询微信登录日志列表
export function listLog(query) {
  return request({
    url: '/business/log/list',
    method: 'get',
    params: query
  })
}

// 查询微信登录日志详细
export function getLog(logId) {
  return request({
    url: '/business/log/' + logId,
    method: 'get'
  })
}

// 新增微信登录日志
export function addLog(data) {
  return request({
    url: '/business/log',
    method: 'post',
    data: data
  })
}

// 修改微信登录日志
export function updateLog(data) {
  return request({
    url: '/business/log',
    method: 'put',
    data: data
  })
}

// 删除微信登录日志
export function delLog(logId) {
  return request({
    url: '/business/log/' + logId,
    method: 'delete'
  })
}

// 导出微信登录日志
export function exportLog(query) {
  return request({
    url: '/business/log/export',
    method: 'get',
    params: query
  })
}

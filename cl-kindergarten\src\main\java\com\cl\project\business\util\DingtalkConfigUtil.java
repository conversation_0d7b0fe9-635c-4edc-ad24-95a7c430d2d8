package com.cl.project.business.util;

import com.cl.common.utils.spring.SpringUtils;
import com.cl.project.system.service.ISysConfigService;

/**
 * 钉钉配置工具类
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
public class DingtalkConfigUtil 
{
    private static final String DINGTALK_APP_KEY = "AppKey";
    private static final String DINGTALK_APP_SECRET = "AppSecret";
    
    /**
     * 获取钉钉AppKey
     */
    public static String getAppKey() 
    {
        ISysConfigService configService = SpringUtils.getBean(ISysConfigService.class);
        return configService.selectConfigByKey(DINGTALK_APP_KEY);
    }
    
    /**
     * 获取钉钉AppSecret
     */
    public static String getAppSecret() 
    {
        ISysConfigService configService = SpringUtils.getBean(ISysConfigService.class);
        return configService.selectConfigByKey(DINGTALK_APP_SECRET);
    }
}

import request from '@/utils/request'

// 查询考勤统计报表列表
export function getAttendanceReport(query) {
  return request({
    url: '/kg/report/attendance/list',
    method: 'get',
    params: query
  })
}

// 查询考勤详细记录
export function getAttendanceDetail(query) {
  return request({
    url: '/kg/report/attendance/detail',
    method: 'get',
    params: query
  })
}

// 导出考勤统计报表
export function exportAttendanceReport(query) {
  return request({
    url: '/kg/report/attendance/export',
    method: 'get',
    params: query
  })
}

// 获取考勤统计图表数据
export function getAttendanceChartData(query) {
  return request({
    url: '/kg/report/attendance/chart',
    method: 'get',
    params: query
  })
}

// 获取考勤汇总数据
export function getAttendanceSummary(query) {
  return request({
    url: '/kg/report/attendance/summary',
    method: 'get',
    params: query
  })
}

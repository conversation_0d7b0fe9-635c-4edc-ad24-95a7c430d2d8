<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
      <el-form-item label="课程名称" prop="courseName">
        <el-select v-model="queryParams.courseId" placeholder="请选择课程" clearable size="small">
          <el-option
            v-for="item in courseList"
            :key="item.courseId"
            :label="item.courseName"
            :value="item.courseId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="学生姓名" prop="studentName">
        <el-input
          v-model="queryParams.studentName"
          placeholder="请输入学生姓名"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="考勤日期" prop="attendanceDate">
        <el-date-picker clearable size="small" style="width: 200px"
          v-model="queryParams.attendanceDate"
          type="date"
          value-format="yyyy-MM-dd"
          placeholder="选择考勤日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="考勤状态" prop="attendanceStatus">
        <el-select v-model="queryParams.attendanceStatus" placeholder="请选择考勤状态" clearable size="small">
          <el-option label="已签到" value="1" />
          <el-option label="已确认" value="2" />
          <el-option label="缺勤" value="3" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleCheckin"
          v-hasPermi="['kg:course:attendance:checkin']"
        >托管签到</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-check"
          size="mini"
          @click="handleConfirm"
          v-hasPermi="['kg:course:attendance:confirm']"
        >托管确认</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['kg:course:attendance:query']"
        >导出</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="attendanceList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="课程名称" align="center" prop="courseName" />
      <el-table-column label="学生姓名" align="center" prop="studentName" />
      <el-table-column label="班级" align="center" prop="className" />
      <el-table-column label="考勤日期" align="center" prop="attendanceDate" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.attendanceDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="签到时间" align="center" prop="checkinTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.checkinTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="课程时长" align="center" prop="duration" />
      <el-table-column label="考勤状态" align="center" prop="attendanceStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.kg_course_attendance_status" :value="scope.row.attendanceStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="是否确认" align="center" prop="isConfirmed">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_yes_no" :value="scope.row.isConfirmed"/>
        </template>
      </el-table-column>
      <el-table-column label="确认人" align="center" prop="confirmedBy" />
      <el-table-column label="备注" align="center" prop="remark" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['kg:course:attendance:checkin']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['kg:course:attendance:checkin']"
          >删除</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 托管签到对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="课程" prop="courseId">
          <el-select v-model="form.courseId" placeholder="请选择课程" filterable>
            <el-option
              v-for="item in courseList"
              :key="item.courseId"
              :label="item.courseName"
              :value="item.courseId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="学生" prop="studentId">
          <el-select v-model="form.studentId" placeholder="请选择学生" filterable>
            <el-option
              v-for="item in studentList"
              :key="item.studentId"
              :label="item.studentName"
              :value="item.studentId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="考勤日期" prop="attendanceDate">
          <el-date-picker clearable size="small" style="width: 200px"
            v-model="form.attendanceDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择考勤日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="考勤状态" prop="attendanceStatus">
          <el-radio-group v-model="form.attendanceStatus">
            <el-radio label="1">已签到</el-radio>
            <el-radio label="2">已确认</el-radio>
            <el-radio label="3">缺勤</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listCourseAttendance, getCourseAttendance, delCourseAttendance, addCourseAttendance, updateCourseAttendance, exportCourseAttendance } from "@/api/kg/course/attendance";
import { listCourse } from "@/api/kg/course/manage";
import { listStudent } from "@/api/kg/student/info";

export default {
  name: "CourseAttendance",
  dicts: ['kg_course_attendance_status', 'sys_yes_no'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 托管考勤表格数据
      attendanceList: [],
      // 课程列表
      courseList: [],
      // 学生列表
      studentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        courseId: undefined,
        studentName: undefined,
        attendanceDate: undefined,
        attendanceStatus: undefined,
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        courseId: [
          { required: true, message: "课程不能为空", trigger: "change" }
        ],
        studentId: [
          { required: true, message: "学生不能为空", trigger: "change" }
        ],
        attendanceDate: [
          { required: true, message: "考勤日期不能为空", trigger: "blur" }
        ],
        attendanceStatus: [
          { required: true, message: "考勤状态不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getCourseList();
    this.getStudentList();
  },
  methods: {
    /** 查询托管考勤列表 */
    getList() {
      this.loading = true;
      listCourseAttendance(this.queryParams).then(response => {
        this.attendanceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询课程列表 */
    getCourseList() {
      listCourse().then(response => {
        this.courseList = response.rows;
      });
    },
    /** 查询学生列表 */
    getStudentList() {
      listStudent().then(response => {
        this.studentList = response.rows;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        attendanceId: undefined,
        courseId: undefined,
        studentId: undefined,
        attendanceDate: undefined,
        attendanceStatus: undefined,
        checkinTime: undefined,
        duration: undefined,
        isConfirmed: "0",
        confirmedBy: undefined,
        confirmedTime: undefined,
        remark: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.attendanceId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    /** 托管签到按钮操作 */
    handleCheckin() {
      this.reset();
      this.open = true;
      this.title = "托管签到";
      this.form.attendanceStatus = "1";
    },
    /** 托管确认按钮操作 */
    handleConfirm() {
      const attendanceIds = this.ids;
      if (attendanceIds.length === 0) {
        this.msgError("请选择要确认的考勤记录");
        return;
      }
      this.$confirm('是否确认选中的托管考勤记录?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        // 调用确认接口
        this.msgSuccess("确认成功");
        this.getList();
      }).catch(() => {});
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const attendanceId = row.attendanceId || this.ids
      getCourseAttendance(attendanceId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改托管考勤";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.attendanceId != undefined) {
            updateCourseAttendance(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getList();
              }
            });
          } else {
            addCourseAttendance(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const attendanceIds = row.attendanceId || this.ids;
      this.$confirm('是否确认删除托管考勤记录编号为"' + attendanceIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delCourseAttendance(attendanceIds);
        }).then(() => {
          this.getList();
          this.msgSuccess("删除成功");
        }).catch(function() {});
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有托管考勤数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportCourseAttendance(queryParams);
        }).then(response => {
          this.download(response.msg);
        }).catch(function() {});
    }
  }
};
</script>

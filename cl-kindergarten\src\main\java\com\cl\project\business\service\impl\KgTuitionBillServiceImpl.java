package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgTuitionBillMapper;
import com.cl.project.business.domain.KgTuitionBill;
import com.cl.project.business.service.IKgTuitionBillService;

/**
 * 园费账单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgTuitionBillServiceImpl implements IKgTuitionBillService 
{
    @Autowired
    private KgTuitionBillMapper kgTuitionBillMapper;

    /**
     * 查询园费账单
     * 
     * @param billId 园费账单ID
     * @return 园费账单
     */
    @Override
    public KgTuitionBill selectKgTuitionBillById(Long billId)
    {
        return kgTuitionBillMapper.selectKgTuitionBillById(billId);
    }

    /**
     * 查询园费账单列表
     * 
     * @param kgTuitionBill 园费账单
     * @return 园费账单
     */
    @Override
    public List<KgTuitionBill> selectKgTuitionBillList(KgTuitionBill kgTuitionBill)
    {
        return kgTuitionBillMapper.selectKgTuitionBillList(kgTuitionBill);
    }

    /**
     * 新增园费账单
     * 
     * @param kgTuitionBill 园费账单
     * @return 结果
     */
    @Override
    public int insertKgTuitionBill(KgTuitionBill kgTuitionBill)
    {
        kgTuitionBill.setCreateTime(DateUtils.getNowDate());
        return kgTuitionBillMapper.insertKgTuitionBill(kgTuitionBill);
    }

    /**
     * 修改园费账单
     * 
     * @param kgTuitionBill 园费账单
     * @return 结果
     */
    @Override
    public int updateKgTuitionBill(KgTuitionBill kgTuitionBill)
    {
        kgTuitionBill.setUpdateTime(DateUtils.getNowDate());
        return kgTuitionBillMapper.updateKgTuitionBill(kgTuitionBill);
    }

    /**
     * 批量删除园费账单
     * 
     * @param billIds 需要删除的园费账单ID
     * @return 结果
     */
    @Override
    public int deleteKgTuitionBillByIds(Long[] billIds)
    {
        return kgTuitionBillMapper.deleteKgTuitionBillByIds(billIds);
    }

    /**
     * 删除园费账单信息
     * 
     * @param billId 园费账单ID
     * @return 结果
     */
    @Override
    public int deleteKgTuitionBillById(Long billId)
    {
        return kgTuitionBillMapper.deleteKgTuitionBillById(billId);
    }
}

-- =====================================================
-- 幼儿园管理系统数据库结构 - 第四部分
-- 钉钉集成、微信小程序、系统配置、权限管理表
-- =====================================================

-- =====================================================
-- 8. 钉钉集成表
-- =====================================================

-- 8.1 钉钉打卡记录表
-- 功能: 存储从钉钉API获取的打卡数据，用于费用计算
-- 关联关系:
--   - kg_teacher.teacher_id (员工打卡)
--   - kg_student.student_id (学生打卡)
--   - kg_time_config (时间段配置)
--   - kg_student_attendance (学生考勤)
--   - kg_teacher_attendance (教师考勤)
DROP TABLE IF EXISTS `kg_dingtalk_attendance`;
CREATE TABLE `kg_dingtalk_attendance` (
  `record_id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `user_id` varchar(100) NOT NULL COMMENT '钉钉用户ID',
  `employee_id` bigint COMMENT '关联员工ID，关联kg_teacher.teacher_id',
  `student_id` bigint COMMENT '关联学生ID，关联kg_student.student_id',
  `check_time` datetime NOT NULL COMMENT '打卡时间',
  `check_type` varchar(20) NOT NULL COMMENT '打卡类型（OnDuty上班、OffDuty下班）',
  `location_result` varchar(20) COMMENT '定位结果（Normal正常、Outside范围外）',
  `location_title` varchar(100) COMMENT '位置标题',
  `location_detail` varchar(200) COMMENT '位置详情',
  `plan_check_time` datetime COMMENT '计划打卡时间',
  `is_processed` tinyint DEFAULT 0 COMMENT '是否已处理（0未处理 1已处理）',
  `processed_time` datetime COMMENT '处理时间',
  `fee_type` varchar(20) COMMENT '费用类型（tuition园费、course托管费）',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`record_id`),
  KEY `idx_user_id_time` (`user_id`, `check_time`),
  KEY `idx_employee_id` (`employee_id`),
  KEY `idx_student_id` (`student_id`),
  KEY `idx_com_id` (`com_id`),
  KEY `idx_is_processed` (`is_processed`)
) ENGINE=InnoDB COMMENT='钉钉打卡记录表';

-- =====================================================
-- 9. 系统配置表
-- =====================================================

-- 9.1 时间段配置表
-- 功能: 配置园费和托管费的计费时间段
-- 关联关系:
--   - kg_dingtalk_attendance (时间段判断)
--   - kg_student_attendance (考勤时间验证)
--   - kg_course_attendance (托管时间验证)
DROP TABLE IF EXISTS `kg_time_config`;
CREATE TABLE `kg_time_config` (
  `config_id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_name` varchar(50) NOT NULL COMMENT '配置名称',
  `time_type` varchar(20) NOT NULL COMMENT '时间类型（tuition园费、course托管费）',
  `start_time` time NOT NULL COMMENT '开始时间',
  `end_time` time NOT NULL COMMENT '结束时间',
  `is_active` tinyint DEFAULT 1 COMMENT '是否启用（0否 1是）',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`),
  KEY `idx_time_type` (`time_type`),
  KEY `idx_com_id` (`com_id`)
) ENGINE=InnoDB COMMENT='时间段配置表';

-- =====================================================
-- 10. 微信小程序表
-- =====================================================

-- 10.1 微信用户表
-- 功能: 存储微信小程序用户信息，支持多种用户类型绑定
-- 关联关系:
--   - kg_student.student_id (家长绑定学生)
--   - kg_teacher.teacher_id (教师绑定)
--   - sys_user.user_id (管理员绑定)
--   - kg_message_push.openid (消息推送)
DROP TABLE IF EXISTS `kg_wechat_user`;
CREATE TABLE `kg_wechat_user` (
  `wechat_user_id` bigint NOT NULL AUTO_INCREMENT COMMENT '微信用户ID',
  `openid` varchar(100) NOT NULL COMMENT '微信openid',
  `unionid` varchar(100) COMMENT '微信unionid',
  `nickname` varchar(100) COMMENT '微信昵称',
  `avatar_url` varchar(200) COMMENT '头像地址',
  `gender` tinyint COMMENT '性别（0未知、1男、2女）',
  `city` varchar(50) COMMENT '城市',
  `province` varchar(50) COMMENT '省份',
  `country` varchar(50) COMMENT '国家',
  `language` varchar(20) COMMENT '语言',
  `user_type` varchar(20) DEFAULT 'parent' COMMENT '用户类型（parent家长、teacher教师、admin管理员）',
  `bind_user_id` bigint COMMENT '绑定的系统用户ID（教师、管理员）',
  `student_id` bigint COMMENT '关联学生ID（家长用户），关联kg_student.student_id',
  `phone` varchar(20) COMMENT '手机号',
  `bind_status` varchar(20) DEFAULT 'unbound' COMMENT '绑定状态（unbound未绑定、pending待审核、bound已绑定、rejected已拒绝）',
  `bind_time` datetime COMMENT '绑定时间',
  `approved_by` bigint COMMENT '审核人ID',
  `approval_time` datetime COMMENT '审核时间',
  `is_subscribed` tinyint DEFAULT 0 COMMENT '是否关注公众号（0否 1是）',
  `subscribe_time` datetime COMMENT '关注时间',
  `unsubscribe_time` datetime COMMENT '取消关注时间',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`wechat_user_id`),
  UNIQUE KEY `uk_openid` (`openid`),
  KEY `idx_student_id` (`student_id`),
  KEY `idx_com_id` (`com_id`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_bind_user_id` (`bind_user_id`),
  KEY `idx_phone` (`phone`)
) ENGINE=InnoDB COMMENT='微信用户表';

-- 10.2 用户绑定申请记录表
-- 功能: 记录教师和管理员的微信绑定申请
-- 关联关系:
--   - kg_wechat_user.openid (微信用户)
--   - kg_teacher.teacher_id (匹配的教师)
--   - sys_user.user_id (匹配的用户)
DROP TABLE IF EXISTS `kg_user_bind_request`;
CREATE TABLE `kg_user_bind_request` (
  `request_id` bigint NOT NULL AUTO_INCREMENT COMMENT '申请ID',
  `openid` varchar(100) NOT NULL COMMENT '微信openid',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `user_type` varchar(20) NOT NULL COMMENT '申请绑定的用户类型（teacher教师、admin管理员）',
  `real_name` varchar(50) COMMENT '真实姓名',
  `id_card` varchar(18) COMMENT '身份证号',
  `verification_code` varchar(10) COMMENT '验证码',
  `request_status` varchar(20) DEFAULT 'pending' COMMENT '申请状态（pending待审核、approved已通过、rejected已拒绝）',
  `request_time` datetime NOT NULL COMMENT '申请时间',
  `approved_by` bigint COMMENT '审核人ID',
  `approval_time` datetime COMMENT '审核时间',
  `approval_reason` varchar(200) COMMENT '审核意见',
  `matched_user_id` bigint COMMENT '匹配到的系统用户ID',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`request_id`),
  KEY `idx_openid` (`openid`),
  KEY `idx_phone` (`phone`),
  KEY `idx_user_type` (`user_type`),
  KEY `idx_com_id` (`com_id`)
) ENGINE=InnoDB COMMENT='用户绑定申请记录表';

-- 10.3 手机号验证码表
-- 功能: 存储手机验证码，用于用户绑定和登录验证
-- 关联关系:
--   - kg_user_bind_request (绑定验证)
--   - kg_wechat_user (登录验证)
DROP TABLE IF EXISTS `kg_phone_verification`;
CREATE TABLE `kg_phone_verification` (
  `verification_id` bigint NOT NULL AUTO_INCREMENT COMMENT '验证ID',
  `phone` varchar(20) NOT NULL COMMENT '手机号',
  `verification_code` varchar(10) NOT NULL COMMENT '验证码',
  `code_type` varchar(20) NOT NULL COMMENT '验证码类型（bind_user绑定用户、login登录、reset_password重置密码）',
  `openid` varchar(100) COMMENT '微信openid',
  `send_time` datetime NOT NULL COMMENT '发送时间',
  `expire_time` datetime NOT NULL COMMENT '过期时间',
  `is_used` tinyint DEFAULT 0 COMMENT '是否已使用（0否 1是）',
  `used_time` datetime COMMENT '使用时间',
  `ip_address` varchar(50) COMMENT 'IP地址',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`verification_id`),
  KEY `idx_phone_type` (`phone`, `code_type`),
  KEY `idx_openid` (`openid`),
  KEY `idx_send_time` (`send_time`)
) ENGINE=InnoDB COMMENT='手机号验证码表';

-- 10.4 消息推送记录表
-- 功能: 记录微信小程序和公众号的消息推送
-- 关联关系:
--   - kg_wechat_user.openid (推送目标)
--   - kg_tuition_bill (园费账单推送)
--   - kg_course_bill (托管费账单推送)
--   - kg_student_attendance (考勤通知推送)
DROP TABLE IF EXISTS `kg_message_push`;
CREATE TABLE `kg_message_push` (
  `push_id` bigint NOT NULL AUTO_INCREMENT COMMENT '推送ID',
  `message_type` varchar(50) NOT NULL COMMENT '消息类型（tuition_bill园费账单、course_bill托管费账单、attendance_notice考勤通知）',
  `recipient_type` varchar(20) NOT NULL COMMENT '接收者类型（student学生、teacher教师、parent家长）',
  `recipient_id` bigint NOT NULL COMMENT '接收者ID',
  `openid` varchar(100) COMMENT '微信openid',
  `title` varchar(100) NOT NULL COMMENT '消息标题',
  `content` text NOT NULL COMMENT '消息内容',
  `template_id` varchar(100) COMMENT '模板ID',
  `push_time` datetime NOT NULL COMMENT '推送时间',
  `push_status` varchar(20) DEFAULT 'pending' COMMENT '推送状态（pending待推送、success成功、failed失败）',
  `error_message` varchar(500) COMMENT '错误信息',
  `read_status` tinyint DEFAULT 0 COMMENT '阅读状态（0未读 1已读）',
  `read_time` datetime COMMENT '阅读时间',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`push_id`),
  KEY `idx_recipient` (`recipient_type`, `recipient_id`),
  KEY `idx_push_time` (`push_time`),
  KEY `idx_com_id` (`com_id`),
  KEY `idx_openid` (`openid`)
) ENGINE=InnoDB COMMENT='消息推送记录表';

-- 10.5 微信登录日志表
-- 功能: 记录微信小程序的登录日志
-- 关联关系:
--   - kg_wechat_user.openid (登录用户)
DROP TABLE IF EXISTS `kg_wechat_login_log`;
CREATE TABLE `kg_wechat_login_log` (
  `log_id` bigint NOT NULL AUTO_INCREMENT COMMENT '日志ID',
  `openid` varchar(100) NOT NULL COMMENT '微信openid',
  `user_type` varchar(20) COMMENT '用户类型（parent家长、teacher教师、admin管理员）',
  `bind_user_id` bigint COMMENT '绑定的系统用户ID',
  `login_time` datetime NOT NULL COMMENT '登录时间',
  `login_ip` varchar(50) COMMENT '登录IP',
  `login_location` varchar(100) COMMENT '登录地点',
  `device_info` varchar(200) COMMENT '设备信息',
  `login_status` varchar(20) DEFAULT 'success' COMMENT '登录状态（success成功、failed失败、unbound未绑定）',
  `error_message` varchar(200) COMMENT '错误信息',
  `session_key` varchar(100) COMMENT '微信session_key',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  PRIMARY KEY (`log_id`),
  KEY `idx_openid_time` (`openid`, `login_time`),
  KEY `idx_user_id` (`bind_user_id`),
  KEY `idx_com_id` (`com_id`)
) ENGINE=InnoDB COMMENT='微信登录日志表';

-- =====================================================
-- 11. 移动端权限配置表
-- =====================================================

-- 11.1 移动端权限配置表
-- 功能: 配置移动端的权限控制规则，包括时间、位置、审批等限制
-- 关联关系:
--   - sys_role (角色权限)
--   - kg_teacher (教师权限验证)
DROP TABLE IF EXISTS `kg_mobile_permission_config`;
CREATE TABLE `kg_mobile_permission_config` (
  `config_id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `role_key` varchar(100) NOT NULL COMMENT '角色标识',
  `permission_type` varchar(50) NOT NULL COMMENT '权限类型（attendance考勤、course课程、finance财务、student学生）',
  `data_scope` varchar(20) DEFAULT 'self' COMMENT '数据范围（all全部、class班级、course课程、self本人）',
  `time_restriction` json COMMENT '时间限制配置',
  `location_restriction` tinyint DEFAULT 0 COMMENT '是否启用位置限制（0否 1是）',
  `approval_required` tinyint DEFAULT 0 COMMENT '是否需要审批（0否 1是）',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`),
  KEY `idx_role_type` (`role_key`, `permission_type`),
  KEY `idx_com_id` (`com_id`)
) ENGINE=InnoDB COMMENT='移动端权限配置表';

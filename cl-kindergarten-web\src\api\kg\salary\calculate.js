import request from '@/utils/request'

// 查询工资计算列表
export function listSalaryCalculate(query) {
  return request({
    url: '/business/salary/list',
    method: 'get',
    params: query
  })
}

// 查询工资计算详细
export function getSalaryCalculate(salaryId) {
  return request({
    url: '/business/salary/' + salaryId,
    method: 'get'
  })
}

// 修改工资计算
export function updateSalaryCalculate(data) {
  return request({
    url: '/business/salary',
    method: 'put',
    data: data
  })
}

// 工资计算
export function calculateSalary(data) {
  return request({
    url: '/business/salary/calculate',
    method: 'post',
    data: data
  })
}

// 工资确认
export function confirmSalary(salaryIds) {
  return request({
    url: '/business/salary/confirm',
    method: 'post',
    data: { salaryIds: salaryIds }
  })
}

// 工资发放
export function paySalary(salaryIds) {
  return request({
    url: '/business/salary/pay',
    method: 'post',
    data: { salaryIds: salaryIds }
  })
}

// 导出工资计算
export function exportSalaryCalculate(query) {
  return request({
    url: '/business/salary/export',
    method: 'get',
    params: query
  })
}

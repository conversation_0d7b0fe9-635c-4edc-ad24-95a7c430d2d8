<template>
  <div class="app-container">
    <!-- 查询条件 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="100px">
      <el-form-item label="计算类型" prop="calculationType">
        <el-select v-model="queryParams.calculationType" placeholder="请选择计算类型" clearable size="small">
          <el-option label="单个学生" value="single" />
          <el-option label="班级批量" value="class" />
          <el-option label="全园批量" value="all" />
        </el-select>
      </el-form-item>
      <el-form-item label="班级" prop="classId" v-if="queryParams.calculationType !== 'all'">
        <el-select v-model="queryParams.classId" placeholder="请选择班级" clearable size="small" @change="handleClassChange">
          <el-option
            v-for="item in classList"
            :key="item.classId"
            :label="item.className"
            :value="item.classId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="学生" prop="studentId" v-if="queryParams.calculationType === 'single'">
        <el-select v-model="queryParams.studentId" placeholder="请选择学生" clearable size="small" filterable>
          <el-option
            v-for="item in studentList"
            :key="item.studentId"
            :label="item.studentName"
            :value="item.studentId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="计算月份">
        <el-date-picker
          v-model="calculationMonth"
          type="month"
          placeholder="选择月份"
          size="small"
          @change="handleMonthChange"
        ></el-date-picker>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleCalculate">开始计算</el-button>
        <el-button type="success" icon="el-icon-view" size="mini" @click="handlePreview">预览</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 计算规则展示 -->
    <el-card class="mb20" v-if="calculationRules">
      <div slot="header">
        <span>费用计算规则</span>
      </div>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="餐费计算">{{ calculationRules.calculationRules.mealFeeRule }}</el-descriptions-item>
        <el-descriptions-item label="保教费计算">{{ calculationRules.calculationRules.educationFeeRule }}</el-descriptions-item>
        <el-descriptions-item label="管理费计算">{{ calculationRules.calculationRules.managementFeeRule }}</el-descriptions-item>
        <el-descriptions-item label="出勤率计算">{{ calculationRules.calculationRules.attendanceRateRule }}</el-descriptions-item>
      </el-descriptions>
    </el-card>

    <!-- 操作按钮 -->
    <el-row :gutter="10" class="mb8" v-if="calculationResults.length > 0">
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-check"
          size="mini"
          :disabled="multiple"
          @click="handleGenerateBills"
          v-hasPermi="['kg:finance:tuition:send']"
        >生成账单</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出结果</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="info"
          icon="el-icon-refresh"
          size="mini"
          @click="handleRecalculateAll"
          v-hasPermi="['kg:finance:tuition:calculate']"
        >重新计算</el-button>
      </el-col>
    </el-row>

    <!-- 计算结果表格 -->
    <el-table 
      v-loading="loading" 
      :data="calculationResults" 
      @selection-change="handleSelectionChange"
      :summary-method="getSummaries"
      show-summary
    >
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="学生姓名" align="center" prop="studentInfo.studentName" width="100" />
      <el-table-column label="班级" align="center" prop="studentInfo.className" width="80" />
      <el-table-column label="出勤天数" align="center" width="80">
        <template slot-scope="scope">
          {{ scope.row.attendanceStats ? scope.row.attendanceStats.attendanceDays : '-' }}
        </template>
      </el-table-column>
      <el-table-column label="出勤率" align="center" width="80">
        <template slot-scope="scope">
          <span :class="getAttendanceRateClass(scope.row.attendanceStats ? scope.row.attendanceStats.attendanceRate : 0)">
            {{ scope.row.attendanceStats ? scope.row.attendanceStats.attendanceRate : 0 }}%
          </span>
        </template>
      </el-table-column>
      <el-table-column label="餐费" align="center" width="80">
        <template slot-scope="scope">
          <span class="text-primary">¥{{ scope.row.feeBreakdown ? scope.row.feeBreakdown.mealFee : 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="保教费" align="center" width="80">
        <template slot-scope="scope">
          <span class="text-success">¥{{ scope.row.feeBreakdown ? scope.row.feeBreakdown.educationFee : 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="管理费" align="center" width="80">
        <template slot-scope="scope">
          <span class="text-info">¥{{ scope.row.feeBreakdown ? scope.row.feeBreakdown.managementFee : 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="费用小计" align="center" width="100">
        <template slot-scope="scope">
          <span class="text-warning">¥{{ scope.row.feeBreakdown ? scope.row.feeBreakdown.totalFee : 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="余额结转" align="center" width="80">
        <template slot-scope="scope">
          <span class="text-success">¥{{ scope.row.feeBreakdown ? scope.row.feeBreakdown.balanceCarryover : 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实际应缴" align="center" width="100">
        <template slot-scope="scope">
          <span class="text-danger font-weight-bold">¥{{ scope.row.feeBreakdown ? scope.row.feeBreakdown.actualPayable : 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="下月预交" align="center" width="100">
        <template slot-scope="scope">
          <span class="text-info">¥{{ scope.row.feeBreakdown ? scope.row.feeBreakdown.advancePayment : 0 }}</span>
        </template>
      </el-table-column>
      <el-table-column label="计算状态" align="center" width="80">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.error" type="danger">计算失败</el-tag>
          <el-tag v-else type="success">计算成功</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="120">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleViewDetail(scope.row)"
          >详情</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleAdjust(scope.row)"
            v-hasPermi="['kg:finance:tuition:calculate']"
          >调整</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 分页 -->
    <pagination
      v-show="total > 0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 费用详情对话框 -->
    <el-dialog title="费用计算详情" :visible.sync="detailDialogVisible" width="800px" append-to-body>
      <div v-if="selectedCalculation">
        <el-descriptions title="学生信息" :column="3" border>
          <el-descriptions-item label="学生姓名">{{ selectedCalculation.studentInfo.studentName }}</el-descriptions-item>
          <el-descriptions-item label="班级">{{ selectedCalculation.studentInfo.className }}</el-descriptions-item>
          <el-descriptions-item label="学生状态">{{ getStudentStatusText(selectedCalculation.studentInfo.status) }}</el-descriptions-item>
        </el-descriptions>
        
        <el-descriptions title="考勤统计" :column="4" border class="mt20">
          <el-descriptions-item label="总天数">{{ selectedCalculation.attendanceStats.totalDays }}</el-descriptions-item>
          <el-descriptions-item label="出勤天数">{{ selectedCalculation.attendanceStats.attendanceDays }}</el-descriptions-item>
          <el-descriptions-item label="缺勤天数">{{ selectedCalculation.attendanceStats.absentDays }}</el-descriptions-item>
          <el-descriptions-item label="出勤率">{{ selectedCalculation.attendanceStats.attendanceRate }}%</el-descriptions-item>
        </el-descriptions>
        
        <el-descriptions title="费用明细" :column="2" border class="mt20">
          <el-descriptions-item label="餐费">¥{{ selectedCalculation.feeBreakdown.mealFee }}</el-descriptions-item>
          <el-descriptions-item label="保教费">¥{{ selectedCalculation.feeBreakdown.educationFee }}</el-descriptions-item>
          <el-descriptions-item label="管理费">¥{{ selectedCalculation.feeBreakdown.managementFee }}</el-descriptions-item>
          <el-descriptions-item label="费用小计">¥{{ selectedCalculation.feeBreakdown.totalFee }}</el-descriptions-item>
          <el-descriptions-item label="余额结转">¥{{ selectedCalculation.feeBreakdown.balanceCarryover }}</el-descriptions-item>
          <el-descriptions-item label="实际应缴">¥{{ selectedCalculation.feeBreakdown.actualPayable }}</el-descriptions-item>
        </el-descriptions>
      </div>
    </el-dialog>

    <!-- 费用调整对话框 -->
    <el-dialog title="费用调整" :visible.sync="adjustDialogVisible" width="500px" append-to-body>
      <el-form ref="adjustForm" :model="adjustForm" :rules="adjustRules" label-width="100px">
        <el-form-item label="费用类型" prop="feeType">
          <el-select v-model="adjustForm.feeType" placeholder="请选择费用类型">
            <el-option label="餐费" value="meal" />
            <el-option label="保教费" value="education" />
            <el-option label="管理费" value="management" />
          </el-select>
        </el-form-item>
        <el-form-item label="调整金额" prop="adjustAmount">
          <el-input-number 
            v-model="adjustForm.adjustAmount" 
            :precision="2" 
            :step="1" 
            placeholder="正数为增加，负数为减少"
            style="width: 100%"
          />
        </el-form-item>
        <el-form-item label="调整原因" prop="reason">
          <el-input v-model="adjustForm.reason" type="textarea" placeholder="请输入调整原因" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitAdjust">确 定</el-button>
        <el-button @click="cancelAdjust">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { 
  calculateMonthlyTuition,
  calculateClassTuitionBatch,
  calculateAllTuitionBatch,
  previewTuitionCalculation,
  getTuitionCalculationRules,
  generateTuitionBills,
  adjustFee,
  getTuitionStatistics
} from "@/api/kg/finance/tuition/calculation";
import { listClass } from "@/api/kg/student/class";
import { listStudent } from "@/api/kg/student/info";

export default {
  name: "TuitionCalculation",
  data() {
    return {
      // 查询参数
      queryParams: {
        calculationType: 'single',
        classId: null,
        studentId: null,
        year: new Date().getFullYear(),
        month: new Date().getMonth() + 1,
        pageNum: 1,
        pageSize: 10
      },
      // 计算月份
      calculationMonth: new Date(),
      // 加载状态
      loading: false,
      // 计算结果
      calculationResults: [],
      // 计算规则
      calculationRules: null,
      // 总记录数
      total: 0,
      // 选中的记录
      ids: [],
      multiple: true,
      // 基础数据
      classList: [],
      studentList: [],
      // 详情对话框
      detailDialogVisible: false,
      selectedCalculation: null,
      // 调整对话框
      adjustDialogVisible: false,
      adjustForm: {
        billId: null,
        feeType: '',
        adjustAmount: 0,
        reason: ''
      },
      adjustRules: {
        feeType: [
          { required: true, message: "费用类型不能为空", trigger: "change" }
        ],
        adjustAmount: [
          { required: true, message: "调整金额不能为空", trigger: "blur" }
        ],
        reason: [
          { required: true, message: "调整原因不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getClassList();
    this.getStudentList();
  },
  methods: {
    /** 获取班级列表 */
    getClassList() {
      listClass().then(response => {
        this.classList = response.rows;
      });
    },
    
    /** 获取学生列表 */
    getStudentList() {
      listStudent().then(response => {
        this.studentList = response.rows;
      });
    },
    
    /** 班级改变时更新学生列表 */
    handleClassChange(classId) {
      if (classId) {
        this.studentList = this.studentList.filter(student => student.classId === classId);
        this.queryParams.studentId = null;
        
        // 获取计算规则
        getTuitionCalculationRules(classId).then(response => {
          this.calculationRules = response.data;
        });
      } else {
        this.getStudentList();
        this.calculationRules = null;
      }
    },
    
    /** 月份改变 */
    handleMonthChange(date) {
      if (date) {
        this.queryParams.year = date.getFullYear();
        this.queryParams.month = date.getMonth() + 1;
      }
    },
    
    /** 开始计算 */
    handleCalculate() {
      if (!this.validateParams()) {
        return;
      }
      
      this.loading = true;
      
      let apiCall;
      switch (this.queryParams.calculationType) {
        case 'single':
          apiCall = calculateMonthlyTuition(
            this.queryParams.studentId,
            this.queryParams.year,
            this.queryParams.month
          );
          break;
        case 'class':
          apiCall = calculateClassTuitionBatch(
            this.queryParams.classId,
            this.queryParams.year,
            this.queryParams.month
          );
          break;
        case 'all':
          apiCall = calculateAllTuitionBatch(
            this.queryParams.year,
            this.queryParams.month
          );
          break;
      }
      
      apiCall.then(response => {
        if (this.queryParams.calculationType === 'single') {
          this.calculationResults = [response.data];
        } else if (this.queryParams.calculationType === 'class') {
          this.calculationResults = response.data;
        } else {
          // 全园批量计算，需要展开所有班级的结果
          this.calculationResults = [];
          response.data.classResults.forEach(classResult => {
            this.calculationResults.push(...classResult.studentResults);
          });
        }
        
        this.total = this.calculationResults.length;
        this.loading = false;
        
        this.$message.success('计算完成');
      }).catch(() => {
        this.loading = false;
      });
    },
    
    /** 预览计算 */
    handlePreview() {
      if (this.queryParams.calculationType !== 'single' || !this.queryParams.studentId) {
        this.$message.warning('预览功能仅支持单个学生计算');
        return;
      }
      
      previewTuitionCalculation(
        this.queryParams.studentId,
        this.queryParams.year,
        this.queryParams.month
      ).then(response => {
        this.selectedCalculation = response.data;
        this.detailDialogVisible = true;
      });
    },
    
    /** 参数验证 */
    validateParams() {
      if (this.queryParams.calculationType === 'single' && !this.queryParams.studentId) {
        this.$message.warning('请选择学生');
        return false;
      }
      
      if (this.queryParams.calculationType !== 'all' && !this.queryParams.classId) {
        this.$message.warning('请选择班级');
        return false;
      }
      
      if (!this.queryParams.year || !this.queryParams.month) {
        this.$message.warning('请选择计算月份');
        return false;
      }
      
      return true;
    },
    
    /** 重置查询 */
    resetQuery() {
      this.resetForm("queryForm");
      this.calculationResults = [];
      this.calculationRules = null;
      this.total = 0;
    },
    
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.studentInfo.studentId);
      this.multiple = !selection.length;
    },
    
    /** 生成账单 */
    handleGenerateBills() {
      if (this.calculationResults.length === 0) {
        this.$message.warning('没有可生成的账单');
        return;
      }
      
      // 过滤掉计算失败的记录
      const validResults = this.calculationResults.filter(result => !result.error);
      
      if (validResults.length === 0) {
        this.$message.warning('没有计算成功的记录');
        return;
      }
      
      this.$confirm(`确认生成 ${validResults.length} 条账单?`, "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        // 添加账单月份信息
        const billData = validResults.map(result => ({
          ...result,
          billMonth: `${this.queryParams.year}-${String(this.queryParams.month).padStart(2, '0')}`
        }));
        
        generateTuitionBills(billData).then(response => {
          this.$message.success(response.msg);
          this.calculationResults = [];
        });
      });
    },
    
    /** 导出结果 */
    handleExport() {
      // TODO: 实现导出功能
      this.$message.info("导出功能开发中...");
    },
    
    /** 重新计算全部 */
    handleRecalculateAll() {
      this.handleCalculate();
    },
    
    /** 查看详情 */
    handleViewDetail(row) {
      this.selectedCalculation = row;
      this.detailDialogVisible = true;
    },
    
    /** 费用调整 */
    handleAdjust(row) {
      this.adjustForm.billId = row.billId; // 如果是已生成的账单
      this.adjustForm.feeType = '';
      this.adjustForm.adjustAmount = 0;
      this.adjustForm.reason = '';
      this.adjustDialogVisible = true;
    },
    
    /** 提交调整 */
    submitAdjust() {
      this.$refs["adjustForm"].validate(valid => {
        if (valid) {
          adjustFee(
            this.adjustForm.billId,
            this.adjustForm.feeType,
            this.adjustForm.adjustAmount,
            this.adjustForm.reason
          ).then(response => {
            this.$message.success("调整成功");
            this.adjustDialogVisible = false;
            this.handleCalculate();
          });
        }
      });
    },
    
    /** 取消调整 */
    cancelAdjust() {
      this.adjustDialogVisible = false;
    },
    
    /** 获取出勤率样式类 */
    getAttendanceRateClass(rate) {
      if (rate >= 90) return 'text-success';
      if (rate >= 70) return 'text-warning';
      return 'text-danger';
    },
    
    /** 获取学生状态文本 */
    getStudentStatusText(status) {
      const statusMap = {
        '0': '在读',
        '1': '休学',
        '2': '退学'
      };
      return statusMap[status] || '未知';
    },
    
    /** 表格合计行 */
    getSummaries(param) {
      const { columns, data } = param;
      const sums = [];
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = '合计';
          return;
        }
        
        const values = data.map(item => {
          if (!item.feeBreakdown) return 0;
          
          switch (column.property) {
            case 'feeBreakdown.mealFee':
              return Number(item.feeBreakdown.mealFee);
            case 'feeBreakdown.educationFee':
              return Number(item.feeBreakdown.educationFee);
            case 'feeBreakdown.managementFee':
              return Number(item.feeBreakdown.managementFee);
            case 'feeBreakdown.totalFee':
              return Number(item.feeBreakdown.totalFee);
            case 'feeBreakdown.actualPayable':
              return Number(item.feeBreakdown.actualPayable);
            default:
              return 0;
          }
        });
        
        if (values.every(value => !isNaN(value))) {
          const sum = values.reduce((prev, curr) => {
            const value = Number(curr);
            if (!isNaN(value)) {
              return prev + curr;
            } else {
              return prev;
            }
          }, 0);
          sums[index] = '¥' + sum.toFixed(2);
        } else {
          sums[index] = '';
        }
      });
      
      return sums;
    }
  }
};
</script>

<style scoped>
.text-primary {
  color: #409eff !important;
}

.text-success {
  color: #67c23a !important;
}

.text-info {
  color: #909399 !important;
}

.text-warning {
  color: #e6a23c !important;
}

.text-danger {
  color: #f56c6c !important;
}

.font-weight-bold {
  font-weight: bold;
}

.mb20 {
  margin-bottom: 20px;
}

.mt20 {
  margin-top: 20px;
}
</style>

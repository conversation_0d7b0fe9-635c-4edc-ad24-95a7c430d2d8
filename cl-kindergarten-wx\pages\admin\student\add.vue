<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">{{ isEdit ? '编辑学生' : '添加学生' }}</text>
				</view>
				<view class="nav-right" @click="saveStudent">
					<u-icon name="checkmark" color="#ffffff" size="20"></u-icon>
				</view>
			</view>
		</view>

		<!-- 表单内容 -->
		<view class="form-container">
			<!-- 基本信息 -->
			<view class="form-section">
				<view class="section-title">
					<view class="title-icon">👶</view>
					<text>基本信息</text>
				</view>
				
				<!-- 学生姓名 -->
				<view class="form-item">
					<view class="form-label">
						<text class="required">*</text>
						<text>学生姓名</text>
					</view>
					<view class="input-wrapper">
						<u-input 
							v-model="formData.studentName" 
							placeholder="请输入学生姓名"
							maxlength="50"
							:border="false"
							customStyle="background: #f8f9fa; border-radius: 12rpx; padding: 20rpx;"
						/>
					</view>
				</view>

				<!-- 学号 -->
				<view class="form-item">
					<view class="form-label">
						<text class="required">*</text>
						<text>学号</text>
					</view>
					<view class="input-wrapper">
						<u-input 
							v-model="formData.studentNumber" 
							placeholder="请输入学号"
							maxlength="20"
							:border="false"
							customStyle="background: #f8f9fa; border-radius: 12rpx; padding: 20rpx;"
						/>
						<view class="generate-btn" @click="generateStudentNumber">
							<text>生成</text>
						</view>
					</view>
				</view>

				<!-- 联系电话 -->
				<view class="form-item">
					<view class="form-label">
						<text>联系电话</text>
					</view>
					<view class="input-wrapper">
						<u-input 
							v-model="formData.phone" 
							placeholder="请输入联系电话"
							maxlength="11"
							type="number"
							:border="false"
							customStyle="background: #f8f9fa; border-radius: 12rpx; padding: 20rpx;"
						/>
					</view>
				</view>

				<!-- 性别 -->
				<view class="form-item">
					<view class="form-label">
						<text class="required">*</text>
						<text>性别</text>
					</view>
					<view class="input-wrapper" @click="showGenderPicker">
						<view class="picker-display">
							<text class="picker-text" :class="{ placeholder: !selectedGender }">
								{{ selectedGender ? selectedGender.label : '请选择性别' }}
							</text>
							<u-icon name="arrow-right" color="#c0c4cc" size="16"></u-icon>
						</view>
					</view>
				</view>

				<!-- 出生日期 -->
				<view class="form-item">
					<view class="form-label">
						<text class="required">*</text>
						<text>出生日期</text>
					</view>
					<view class="date-input" @click="showBirthDatePicker = true">
						<text class="date-text">{{ formatDateDisplay(formData.birthDate) || '请选择出生日期' }}</text>
						<u-icon name="calendar" color="#999" size="20"></u-icon>
					</view>
				</view>

				<!-- 身份证号 -->
				<view class="form-item">
					<view class="form-label">
						<text>身份证号</text>
					</view>
					<view class="input-wrapper">
						<u-input 
							v-model="formData.idCard" 
							placeholder="请输入身份证号"
							maxlength="18"
							:border="false"
							customStyle="background: #f8f9fa; border-radius: 12rpx; padding: 20rpx;"
						/>
					</view>
				</view>
			</view>

			<!-- 班级信息 -->
			<view class="form-section">
				<view class="section-title">
					<view class="title-icon">🏫</view>
					<text>班级信息</text>
				</view>

				<!-- 班级 -->
				<view class="form-item">
					<view class="form-label">
						<text class="required">*</text>
						<text>班级</text>
					</view>
					<view class="input-wrapper" @click="showClassPicker">
						<view class="picker-display">
							<text class="picker-text" :class="{ placeholder: !selectedClass }">
								{{ selectedClass ? selectedClass.label : '请选择班级' }}
							</text>
							<u-icon name="arrow-right" color="#c0c4cc" size="16"></u-icon>
						</view>
					</view>
				</view>

				<!-- 入园日期 -->
				<view class="form-item">
					<view class="form-label">
						<text class="required">*</text>
						<text>入园日期</text>
					</view>
					<view class="date-input" @click="showEnrollmentDatePicker = true">
						<text class="date-text">{{ formatDateDisplay(formData.enrollmentDate) || '请选择入园日期' }}</text>
						<u-icon name="calendar" color="#999" size="20"></u-icon>
					</view>
				</view>

				<!-- 状态 -->
				<view class="form-item">
					<view class="form-label">
						<text>状态</text>
					</view>
					<view class="input-wrapper" @click="showStatusPicker">
						<view class="picker-display">
							<text class="picker-text" :class="{ placeholder: !selectedStatus }">
								{{ selectedStatus ? selectedStatus.label : '请选择状态' }}
							</text>
							<u-icon name="arrow-right" color="#c0c4cc" size="16"></u-icon>
						</view>
					</view>
				</view>
			</view>

			<!-- 家长信息 -->
			<view class="form-section">
				<view class="section-title">
					<view class="title-icon">👨‍👩‍👧‍👦</view>
					<text>家长信息</text>
				</view>

				<view class="parent-row">
					<!-- 家长姓名 -->
					<view class="form-item half">
						<view class="form-label">
							<text class="required">*</text>
							<text>家长姓名</text>
						</view>
						<view class="input-wrapper">
							<u-input 
								v-model="formData.parentName" 
								placeholder="请输入家长姓名"
								maxlength="50"
								:border="false"
								customStyle="background: #f8f9fa; border-radius: 12rpx; padding: 20rpx;"
							/>
						</view>
					</view>

					<!-- 联系电话 -->
					<view class="form-item half">
						<view class="form-label">
							<text class="required">*</text>
							<text>联系电话</text>
						</view>
						<view class="input-wrapper">
							<u-input 
								v-model="formData.parentPhone" 
								placeholder="请输入联系电话"
								maxlength="11"
								type="number"
								:border="false"
								customStyle="background: #f8f9fa; border-radius: 12rpx; padding: 20rpx;"
							/>
						</view>
					</view>
				</view>

				<!-- 紧急联系人 -->
				<view class="form-item">
					<view class="form-label">
						<text>紧急联系人</text>
					</view>
					<view class="input-wrapper">
						<u-input 
							v-model="formData.emergencyContact" 
							placeholder="请输入紧急联系人"
							maxlength="50"
							:border="false"
							customStyle="background: #f8f9fa; border-radius: 12rpx; padding: 20rpx;"
						/>
					</view>
				</view>

				<!-- 紧急联系电话 -->
				<view class="form-item">
					<view class="form-label">
						<text>紧急联系电话</text>
					</view>
					<view class="input-wrapper">
						<u-input 
							v-model="formData.emergencyPhone" 
							placeholder="请输入紧急联系电话"
							maxlength="11"
							type="number"
							:border="false"
							customStyle="background: #f8f9fa; border-radius: 12rpx; padding: 20rpx;"
						/>
					</view>
				</view>

				<!-- 家庭住址 -->
				<view class="form-item">
					<view class="form-label">
						<text>家庭住址</text>
					</view>
					<view class="textarea-wrapper">
						<u-input 
							v-model="formData.address" 
							placeholder="请输入家庭住址"
							type="textarea"
							maxlength="200"
							:border="false"
							customStyle="background: #f8f9fa; border-radius: 12rpx; padding: 20rpx; min-height: 120rpx;"
							@input="onAddressInput"
						/>
						<view class="char-count">{{ addressLength }}/200</view>
					</view>
				</view>
			</view>

			<!-- 其他信息 -->
			<view class="form-section">
				<view class="section-title">
					<view class="title-icon">📧</view>
					<text>其他信息</text>
				</view>

				<!-- 邮箱 -->
				<view class="form-item">
					<view class="form-label">
						<text>邮箱</text>
					</view>
					<view class="input-wrapper">
						<u-input 
							v-model="formData.email" 
							placeholder="请输入邮箱"
							:border="false"
							customStyle="background: #f8f9fa; border-radius: 12rpx; padding: 20rpx;"
						/>
					</view>
				</view>
			</view>

			<!-- 备注信息 -->
			<view class="form-section">
				<view class="section-title">
					<view class="title-icon">📝</view>
					<text>备注信息</text>
				</view>

				<view class="form-item">
					<view class="form-label">
						<text>备注</text>
					</view>
					<view class="textarea-wrapper">
						<u-input 
							v-model="formData.remark" 
							placeholder="请输入备注信息"
							type="textarea"
							maxlength="500"
							:border="false"
							customStyle="background: #f8f9fa; border-radius: 12rpx; padding: 20rpx; min-height: 120rpx;"
						/>
						<view class="char-count">{{ formData.remark.length }}/500</view>
					</view>
				</view>
			</view>
		</view>

		<!-- 保存按钮 -->
		<view class="save-section">
			<view class="save-btn" @click="saveStudent" :class="{ loading: saving }">
				<view v-if="saving" class="loading-spinner"></view>
				<text>{{ saving ? '保存中...' : (isEdit ? '保存修改' : '保存学生信息') }}</text>
			</view>
		</view>

		<!-- 出生日期选择器 -->
		<u-popup v-model="showBirthDatePicker" mode="bottom" border-radius="24">
			<view class="date-picker-popup">
				<view class="picker-header">
					<text class="picker-cancel" @click="showBirthDatePicker = false">取消</text>
					<text class="picker-title">选择出生日期</text>
					<text class="picker-confirm" @click="confirmBirthDate">确定</text>
				</view>
				<picker-view class="date-picker-view" :value="birthDatePickerValue" @change="onBirthDateChange">
					<picker-view-column>
						<view v-for="year in yearOptions" :key="year" class="picker-item">
							{{ year }}年
						</view>
					</picker-view-column>
					<picker-view-column>
						<view v-for="month in monthOptions" :key="month" class="picker-item">
							{{ month }}月
						</view>
					</picker-view-column>
					<picker-view-column>
						<view v-for="day in dayOptions" :key="day" class="picker-item">
							{{ day }}日
						</view>
					</picker-view-column>
				</picker-view>
			</view>
		</u-popup>

		<!-- 入园日期选择器 -->
		<u-popup v-model="showEnrollmentDatePicker" mode="bottom" border-radius="24">
			<view class="date-picker-popup">
				<view class="picker-header">
					<text class="picker-cancel" @click="showEnrollmentDatePicker = false">取消</text>
					<text class="picker-title">选择入园日期</text>
					<text class="picker-confirm" @click="confirmEnrollmentDate">确定</text>
				</view>
				<picker-view class="date-picker-view" :value="enrollmentDatePickerValue" @change="onEnrollmentDateChange">
					<picker-view-column>
						<view v-for="year in yearOptions" :key="year" class="picker-item">
							{{ year }}年
						</view>
					</picker-view-column>
					<picker-view-column>
						<view v-for="month in monthOptions" :key="month" class="picker-item">
							{{ month }}月
						</view>
					</picker-view-column>
					<picker-view-column>
						<view v-for="day in dayOptions" :key="day" class="picker-item">
							{{ day }}日
						</view>
					</picker-view-column>
				</picker-view>
			</view>
		</u-popup>
	</view>
</template>

<script>
import { toast } from '@/utils/utils.js'
import { getStudentDetail, addStudent, updateStudent } from '@/api/api.js'

export default {
	data() {
		return {
			isEdit: false,
			studentId: null,
			formData: {
				studentName: '',
				studentNumber: '',
				gender: '',
				birthDate: '',
				idCard: '',
				phone: '',
				classId: '',
				enrollmentDate: '',
				status: '0',
				parentName: '',
				parentPhone: '',
				emergencyContact: '',
				emergencyPhone: '',
				address: '',
				email: '',
				remark: ''
			},
			
			// 地址字符计数
			addressLength: 0,
			saving: false,
			
			// 日期选择器相关
			showBirthDatePicker: false,
			showEnrollmentDatePicker: false,
			yearOptions: [],
			monthOptions: [],
			dayOptions: [],
			birthDatePickerValue: [0, 0, 0],
			enrollmentDatePickerValue: [0, 0, 0],
			tempBirthDate: [0, 0, 0],
			tempEnrollmentDate: [0, 0, 0],
			
			// 性别选择
			selectedGender: null,
			genderOptions: [
				{ value: '0', label: '男' },
				{ value: '1', label: '女' }
			],
			
			// 班级选择
			selectedClass: null,
			classOptions: [
				{ value: '1', label: '小班' },
				{ value: '2', label: '中班' },
				{ value: '3', label: '大班' },
				{ value: '4', label: '学前班' }
			],
			
			// 状态选择
			selectedStatus: null,
			statusOptions: [
				{ value: '0', label: '在园' },
				{ value: '1', label: '退园' },
				{ value: '2', label: '请假' }
			]
		}
	},

	onLoad(options) {
		this.initDatePicker()
		if (options.id) {
			this.isEdit = true
			this.studentId = parseInt(options.id)
			this.loadStudentDetail()
		} else {
			// 新增模式下也需要设置日期选择器默认值
			this.setDatePickerValues()
		}
	},

	methods: {
		goBack() {
			uni.navigateBack()
		},

		// 生成学号
		generateStudentNumber() {
			const now = new Date()
			const year = now.getFullYear()
			const month = String(now.getMonth() + 1).padStart(2, '0')
			const day = String(now.getDate()).padStart(2, '0')
			const random = Math.floor(Math.random() * 9000) + 1000
			this.formData.studentNumber = `S${year}${month}${day}${random}`
		},

		// 加载学生详情（编辑模式）
		async loadStudentDetail() {
			console.log('开始加载学生详情，ID:', this.studentId)
			try {
				const res = await getStudentDetail(this.studentId)
				console.log('API响应:', res)
				if (res && res.code === 200 && res.data) {
					this.fillFormData(res.data)
				} else {
					console.log('API返回失败，使用模拟数据')
					this.loadMockStudentData()
				}
			} catch (error) {
				console.error('API调用失败:', error)
				console.log('使用模拟数据进行测试')
				this.loadMockStudentData()
			}
		},

		// 填充表单数据
		fillFormData(student) {
			console.log('填充表单数据:', student)
			this.formData = {
				studentName: student.studentName || '',
				studentNumber: student.studentNumber || student.studentCode || '',
				gender: student.gender || '',
				birthDate: student.birthDate || '',
				idCard: student.idCard || '',
				phone: student.phone || '',
				classId: student.classId || '',
				enrollmentDate: student.enrollmentDate || '',
				status: student.status || '0',
				parentName: student.parentName || '',
				parentPhone: student.parentPhone || '',
				emergencyContact: student.emergencyContact || '',
				emergencyPhone: student.emergencyPhone || '',
				address: student.address || '',
				email: student.email || '',
				remark: student.remark || ''
			}

			// 设置选中的选项
			if (student.gender) {
				this.selectedGender = this.genderOptions.find(item => item.value === student.gender)
			}
			if (student.classId) {
				this.selectedClass = this.classOptions.find(item => item.value === student.classId)
			}
			if (student.status !== undefined) {
				this.selectedStatus = this.statusOptions.find(item => item.value === student.status)
			}
			
			// 初始化地址长度
			this.addressLength = this.formData.address.length
			
			// 设置日期选择器的值
			this.setDatePickerValues()
		},

		// 加载模拟学生数据（用于测试）
		loadMockStudentData() {
			console.log('加载模拟学生数据')
			const mockStudent = {
				studentId: this.studentId,
				studentName: '小明' + this.studentId,
				studentNo: `S20241231${String(this.studentId).padStart(4, '0')}`,
				gender: '男',
				birthDate: '2018-05-15',
				idCard: '110101201805151234',
				classId: '2',
				enrollDate: '2023-09-01',
				status: '0',
				parentName: '张先生',
				parentPhone: '13800138000',
				address: '北京市朝阳区某某小区',
				balance: 1000.00,
				monthlyFee: 800.00,
				remark: '这是一个测试学生的备注信息'
			}
			this.fillFormData(mockStudent)
		},

		// 设置日期选择器的值
		setDatePickerValues() {
			// 设置出生日期选择器的值
			if (this.formData.birthDate) {
				try {
					// 处理不同格式的日期字符串
					let birthDateStr = this.formData.birthDate
					if (typeof birthDateStr === 'string') {
						// 去掉时间部分，只保留日期
						birthDateStr = birthDateStr.split(' ')[0]
					}
					
					const birthDate = new Date(birthDateStr)
					if (!isNaN(birthDate.getTime())) {
						const birthYear = birthDate.getFullYear()
						const birthMonth = birthDate.getMonth() + 1
						const birthDay = birthDate.getDate()
						
						const yearIndex = this.yearOptions.findIndex(year => year === birthYear)
						const monthIndex = this.monthOptions.findIndex(month => month === birthMonth)
						
						// 更新日期选项以确保正确的天数
						this.updateDayOptions(birthYear, birthMonth)
						const dayIndex = this.dayOptions.findIndex(day => day === birthDay)
						
						if (yearIndex >= 0 && monthIndex >= 0 && dayIndex >= 0) {
							this.birthDatePickerValue = [yearIndex, monthIndex, dayIndex]
							this.tempBirthDate = [...this.birthDatePickerValue]
						}
					}
				} catch (error) {
					console.error('解析出生日期失败:', error)
				}
			}
			
			// 设置入园日期选择器的值
			if (this.formData.enrollmentDate) {
				try {
					// 处理不同格式的日期字符串
					let enrollmentDateStr = this.formData.enrollmentDate
					if (typeof enrollmentDateStr === 'string') {
						// 去掉时间部分，只保留日期
						enrollmentDateStr = enrollmentDateStr.split(' ')[0]
					}
					
					const enrollmentDate = new Date(enrollmentDateStr)
					if (!isNaN(enrollmentDate.getTime())) {
						const enrollYear = enrollmentDate.getFullYear()
						const enrollMonth = enrollmentDate.getMonth() + 1
						const enrollDay = enrollmentDate.getDate()
						
						const yearIndex = this.yearOptions.findIndex(year => year === enrollYear)
						const monthIndex = this.monthOptions.findIndex(month => month === enrollMonth)
						
						// 更新日期选项以确保正确的天数
						this.updateDayOptions(enrollYear, enrollMonth)
						const dayIndex = this.dayOptions.findIndex(day => day === enrollDay)
						
						if (yearIndex >= 0 && monthIndex >= 0 && dayIndex >= 0) {
							this.enrollmentDatePickerValue = [yearIndex, monthIndex, dayIndex]
							this.tempEnrollmentDate = [...this.enrollmentDatePickerValue]
						}
					}
				} catch (error) {
					console.error('解析入园日期失败:', error)
				}
			}
		},
		showGenderPicker() {
			uni.showActionSheet({
				itemList: this.genderOptions.map(item => item.label),
				success: (res) => {
					this.selectedGender = this.genderOptions[res.tapIndex]
					this.formData.gender = this.selectedGender.value
				}
			})
		},

		// 显示班级选择器
		showClassPicker() {
			uni.showActionSheet({
				itemList: this.classOptions.map(item => item.label),
				success: (res) => {
					this.selectedClass = this.classOptions[res.tapIndex]
					this.formData.classId = this.selectedClass.value
				}
			})
		},

		// 显示状态选择器
		showStatusPicker() {
			uni.showActionSheet({
				itemList: this.statusOptions.map(item => item.label),
				success: (res) => {
					this.selectedStatus = this.statusOptions[res.tapIndex]
					this.formData.status = this.selectedStatus.value
				}
			})
		},

		// 显示出生日期选择器
		showDatePicker() {
			this.showBirthDatePicker = true
		},

		// 显示入学日期选择器
		showEnrollDatePicker() {
			this.showEnrollmentDatePicker = true
		},

		// 地址输入处理
		onAddressInput(e) {
			this.formData.address = e.detail.value
			this.addressLength = e.detail.value.length
		},

		// 表单验证
		validateForm() {
			if (!this.formData.studentName.trim()) {
				toast('请输入学生姓名')
				return false
			}
			if (!this.formData.studentNumber.trim()) {
				toast('请输入学号')
				return false
			}
			if (!this.formData.gender) {
				toast('请选择性别')
				return false
			}
			if (!this.formData.birthDate) {
				toast('请选择出生日期')
				return false
			}
			if (!this.formData.classId) {
				toast('请选择班级')
				return false
			}
			if (!this.formData.enrollmentDate) {
				toast('请选择入园日期')
				return false
			}
			if (!this.formData.parentName.trim()) {
				toast('请输入家长姓名')
				return false
			}
			if (!this.formData.parentPhone.trim()) {
				toast('请输入联系电话')
				return false
			}
			if (!/^1[3-9]\d{9}$/.test(this.formData.parentPhone)) {
				toast('请输入正确的手机号码')
				return false
			}
			return true
		},

		// 保存学生信息
		async saveStudent() {
			if (!this.validateForm()) {
				return
			}

			if (this.saving) {
				return
			}

			this.saving = true
			try {
				const submitData = {
					studentName: this.formData.studentName.trim(),
					studentNumber: this.formData.studentNumber.trim(),
					gender: this.formData.gender,
					birthDate: this.formData.birthDate,
					idCard: this.formData.idCard ? this.formData.idCard.trim() : null,
					phone: this.formData.phone ? this.formData.phone.trim() : null,
					classId: parseInt(this.formData.classId),
					enrollmentDate: this.formData.enrollmentDate,
					status: this.formData.status,
					parentName: this.formData.parentName.trim(),
					parentPhone: this.formData.parentPhone.trim(),
					emergencyContact: this.formData.emergencyContact ? this.formData.emergencyContact.trim() : null,
					emergencyPhone: this.formData.emergencyPhone ? this.formData.emergencyPhone.trim() : null,
					address: this.formData.address ? this.formData.address.trim() : null,
					email: this.formData.email ? this.formData.email.trim() : null,
					remark: this.formData.remark ? this.formData.remark.trim() : null
				}

				let res
				if (this.isEdit) {
					submitData.studentId = this.studentId
					res = await updateStudent(submitData)
				} else {
					res = await addStudent(submitData)
				}

				if (res.code === 200) {
					toast(this.isEdit ? '更新成功' : '添加成功')
					uni.navigateBack()
				} else {
					toast(res.msg || (this.isEdit ? '更新失败' : '添加失败'))
				}
			} catch (error) {
				console.error('保存学生失败:', error)
				// 模拟成功
				toast((this.isEdit ? '更新' : '添加') + '成功（模拟）')
				setTimeout(() => {
					uni.navigateBack()
				}, 1000)
			} finally {
				this.saving = false
			}
		},

		// 初始化日期选择器
		initDatePicker() {
			// 生成年份选项 (1950-当前年份)
			const currentYear = new Date().getFullYear()
			this.yearOptions = []
			for (let i = 1950; i <= currentYear; i++) {
				this.yearOptions.push(i)
			}

			// 生成月份选项
			this.monthOptions = []
			for (let i = 1; i <= 12; i++) {
				this.monthOptions.push(i)
			}

			// 生成日期选项 (默认31天)
			this.updateDayOptions(currentYear, 1)

			// 设置默认值
			const defaultYear = currentYear - 5 // 默认5岁
			const yearIndex = this.yearOptions.findIndex(year => year === defaultYear)
			this.birthDatePickerValue = [yearIndex, 0, 0]
			this.enrollmentDatePickerValue = [this.yearOptions.length - 1, 0, 0] // 当前年份
			this.tempBirthDate = [...this.birthDatePickerValue]
			this.tempEnrollmentDate = [...this.enrollmentDatePickerValue]
		},

		// 更新日期选项
		updateDayOptions(year, month) {
			const daysInMonth = new Date(year, month, 0).getDate()
			this.dayOptions = []
			for (let i = 1; i <= daysInMonth; i++) {
				this.dayOptions.push(i)
			}
		},

		// 出生日期选择器变化
		onBirthDateChange(e) {
			this.tempBirthDate = e.detail.value
			const year = this.yearOptions[this.tempBirthDate[0]]
			const month = this.monthOptions[this.tempBirthDate[1]]
			this.updateDayOptions(year, month)

			// 如果当前选中的日期超出了新月份的天数，调整到最后一天
			if (this.tempBirthDate[2] >= this.dayOptions.length) {
				this.tempBirthDate[2] = this.dayOptions.length - 1
			}
		},

		// 入园日期选择器变化
		onEnrollmentDateChange(e) {
			this.tempEnrollmentDate = e.detail.value
			const year = this.yearOptions[this.tempEnrollmentDate[0]]
			const month = this.monthOptions[this.tempEnrollmentDate[1]]
			this.updateDayOptions(year, month)

			// 如果当前选中的日期超出了新月份的天数，调整到最后一天
			if (this.tempEnrollmentDate[2] >= this.dayOptions.length) {
				this.tempEnrollmentDate[2] = this.dayOptions.length - 1
			}
		},

		// 确认出生日期
		confirmBirthDate() {
			const year = this.yearOptions[this.tempBirthDate[0]]
			const month = this.monthOptions[this.tempBirthDate[1]]
			const day = this.dayOptions[this.tempBirthDate[2]]

			this.formData.birthDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
			this.birthDatePickerValue = [...this.tempBirthDate]
			this.showBirthDatePicker = false
		},

		// 确认入园日期
		confirmEnrollmentDate() {
			const year = this.yearOptions[this.tempEnrollmentDate[0]]
			const month = this.monthOptions[this.tempEnrollmentDate[1]]
			const day = this.dayOptions[this.tempEnrollmentDate[2]]

			this.formData.enrollmentDate = `${year}-${String(month).padStart(2, '0')}-${String(day).padStart(2, '0')}`
			this.enrollmentDatePickerValue = [...this.tempEnrollmentDate]
			this.showEnrollmentDatePicker = false
		},

		// 格式化日期显示
		formatDateDisplay(dateStr) {
			if (!dateStr) return ''
			return dateStr.split(' ')[0] // 只显示日期部分
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
	padding-bottom: 120rpx;
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 0 30rpx;
	padding-top: var(--status-bar-height, 44rpx);
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.header-content {
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.nav-left, .nav-right {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: rgba(255, 255, 255, 0.3);
	}
}

.header-title {
	flex: 1;
	text-align: center;
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 表单容器 */
.form-container {
	padding: 30rpx;
}

.form-section {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 40rpx 30rpx;
	margin-bottom: 30rpx;
	box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.08);
}

.section-title {
	display: flex;
	align-items: center;
	margin-bottom: 40rpx;
	padding-bottom: 20rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.title-icon {
	font-size: 32rpx;
	margin-right: 16rpx;
}

.section-title text {
	font-size: 32rpx;
	font-weight: 600;
	color: #333333;
}

.form-item {
	margin-bottom: 30rpx;

	&:last-child {
		margin-bottom: 0;
	}

	&.half {
		width: calc(50% - 10rpx);
		display: inline-block;
		vertical-align: top;
	}
}

.parent-row, .fee-row {
	display: flex;
	justify-content: space-between;
}

.form-label {
	font-size: 28rpx;
	font-weight: 500;
	color: #333333;
	margin-bottom: 16rpx;
	display: flex;
	align-items: center;
}

.required {
	color: #ff4757;
	margin-right: 8rpx;
	font-size: 24rpx;
}

.input-wrapper {
	position: relative;
	display: flex;
	align-items: center;
}

.generate-btn {
	position: absolute;
	right: 20rpx;
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	color: #ffffff;
	padding: 12rpx 24rpx;
	border-radius: 20rpx;
	font-size: 24rpx;
	z-index: 10;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.95);
	}
}

.picker-display {
	background: #f8f9fa;
	border-radius: 12rpx;
	padding: 20rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	min-height: 80rpx;
}

.picker-text {
	font-size: 28rpx;
	color: #333333;

	&.placeholder {
		color: #c0c4cc;
	}
}

.textarea-wrapper {
	position: relative;
}

.char-count {
	position: absolute;
	bottom: 16rpx;
	right: 20rpx;
	font-size: 24rpx;
	color: #999999;
}

/* 保存按钮 */
.save-section {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	background: #ffffff;
	padding: 30rpx;
	box-shadow: 0 -8rpx 32rpx rgba(0, 0, 0, 0.1);
}

.save-btn {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	border-radius: 50rpx;
	height: 100rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	box-shadow: 0 16rpx 40rpx rgba(102, 126, 234, 0.4);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);

	&:active {
		transform: translateY(4rpx) scale(0.98);
		box-shadow: 0 8rpx 20rpx rgba(102, 126, 234, 0.6);
	}

	&.loading {
		opacity: 0.8;
		pointer-events: none;
	}

	text {
		font-size: 32rpx;
		font-weight: 600;
		color: #ffffff;
		text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
	}
}

.loading-spinner {
	width: 32rpx;
	height: 32rpx;
	border: 3rpx solid rgba(255, 255, 255, 0.3);
	border-top: 3rpx solid #ffffff;
	border-radius: 50%;
	animation: spin 1s linear infinite;
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* 日期输入框样式 */
.date-input {
	background: #f8f9fa;
	padding: 24rpx;
	border-radius: 12rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border: 1rpx solid #e0e0e0;
	transition: all 0.3s ease;

	&:active {
		background: #f0f0f0;
		border-color: #667eea;
	}
}

.date-text {
	font-size: 28rpx;
	color: #333;
	flex: 1;

	&:empty::before {
		content: attr(placeholder);
		color: #999;
	}
}

/* 日期选择器样式 */
.date-picker-popup {
	background: #ffffff;
	border-radius: 24rpx 24rpx 0 0;
}

.picker-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 30rpx 40rpx;
	border-bottom: 1rpx solid #f0f0f0;
}

.picker-cancel {
	color: #999999;
	font-size: 28rpx;
}

.picker-title {
	color: #333333;
	font-size: 32rpx;
	font-weight: 600;
}

.picker-confirm {
	color: #667eea;
	font-size: 28rpx;
	font-weight: 600;
}

.date-picker-view {
	height: 400rpx;
	padding: 20rpx 0;
}

.picker-item {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 80rpx;
	font-size: 28rpx;
	color: #333333;
}
</style>

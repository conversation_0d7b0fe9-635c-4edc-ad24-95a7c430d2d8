package com.cl.project.business.domain.dto;

import com.alibaba.fastjson.annotation.JSONField;

/**
 * 钉钉部门操作请求实体
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public class DingtalkDepartmentRequest 
{
    /**
     * 创建部门请求
     */
    public static class CreateRequest 
    {
        @JSONField(name = "name")
        private String name;
        
        @JSONField(name = "parent_id")
        private Long parentId;
        
        @JSONField(name = "order")
        private Integer order;
        
        @JSONField(name = "create_dept_group")
        private Boolean createDeptGroup = false;
        
        @JSONField(name = "auto_add_user")
        private Boolean autoAddUser = true;
        
        public CreateRequest() {}
        
        public CreateRequest(String name, Long parentId, Integer order) {
            this.name = name;
            this.parentId = parentId;
            this.order = order;
        }
        
        // Getters and Setters
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public Long getParentId() {
            return parentId;
        }
        
        public void setParentId(Long parentId) {
            this.parentId = parentId;
        }
        
        public Integer getOrder() {
            return order;
        }
        
        public void setOrder(Integer order) {
            this.order = order;
        }
        
        public Boolean getCreateDeptGroup() {
            return createDeptGroup;
        }
        
        public void setCreateDeptGroup(Boolean createDeptGroup) {
            this.createDeptGroup = createDeptGroup;
        }
        
        public Boolean getAutoAddUser() {
            return autoAddUser;
        }
        
        public void setAutoAddUser(Boolean autoAddUser) {
            this.autoAddUser = autoAddUser;
        }
    }
    
    /**
     * 更新部门请求
     */
    public static class UpdateRequest 
    {
        @JSONField(name = "dept_id")
        private Long deptId;
        
        @JSONField(name = "name")
        private String name;
        
        @JSONField(name = "parent_id")
        private Long parentId;
        
        @JSONField(name = "order")
        private Integer order;
        
        @JSONField(name = "auto_add_user")
        private Boolean autoAddUser;
        
        public UpdateRequest() {}
        
        public UpdateRequest(Long deptId, String name, Long parentId, Integer order) {
            this.deptId = deptId;
            this.name = name;
            this.parentId = parentId;
            this.order = order;
        }
        
        // Getters and Setters
        public Long getDeptId() {
            return deptId;
        }
        
        public void setDeptId(Long deptId) {
            this.deptId = deptId;
        }
        
        public String getName() {
            return name;
        }
        
        public void setName(String name) {
            this.name = name;
        }
        
        public Long getParentId() {
            return parentId;
        }
        
        public void setParentId(Long parentId) {
            this.parentId = parentId;
        }
        
        public Integer getOrder() {
            return order;
        }
        
        public void setOrder(Integer order) {
            this.order = order;
        }
        
        public Boolean getAutoAddUser() {
            return autoAddUser;
        }
        
        public void setAutoAddUser(Boolean autoAddUser) {
            this.autoAddUser = autoAddUser;
        }
    }
    
    /**
     * 创建部门响应
     */
    public static class CreateResponse 
    {
        private Integer errcode;
        private String errmsg;
        private String request_id;
        private CreateResult result;
        
        public static class CreateResult 
        {
            @JSONField(name = "dept_id")
            private Long deptId;
            
            public Long getDeptId() {
                return deptId;
            }
            
            public void setDeptId(Long deptId) {
                this.deptId = deptId;
            }
        }
        
        // Getters and Setters
        public Integer getErrcode() {
            return errcode;
        }
        
        public void setErrcode(Integer errcode) {
            this.errcode = errcode;
        }
        
        public String getErrmsg() {
            return errmsg;
        }
        
        public void setErrmsg(String errmsg) {
            this.errmsg = errmsg;
        }
        
        public String getRequest_id() {
            return request_id;
        }
        
        public void setRequest_id(String request_id) {
            this.request_id = request_id;
        }
        
        public CreateResult getResult() {
            return result;
        }
        
        public void setResult(CreateResult result) {
            this.result = result;
        }
    }
    
    /**
     * 通用操作响应（更新、删除）
     */
    public static class CommonResponse 
    {
        private Integer errcode;
        private String errmsg;
        private String request_id;
        
        // Getters and Setters
        public Integer getErrcode() {
            return errcode;
        }
        
        public void setErrcode(Integer errcode) {
            this.errcode = errcode;
        }
        
        public String getErrmsg() {
            return errmsg;
        }
        
        public void setErrmsg(String errmsg) {
            this.errmsg = errmsg;
        }
        
        public String getRequest_id() {
            return request_id;
        }
        
        public void setRequest_id(String request_id) {
            this.request_id = request_id;
        }
    }
}

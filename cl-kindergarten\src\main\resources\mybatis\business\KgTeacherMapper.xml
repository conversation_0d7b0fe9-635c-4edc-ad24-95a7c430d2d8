<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgTeacherMapper">
    
    <resultMap type="KgTeacher" id="KgTeacherResult">
        <result property="teacherId"    column="teacher_id"    />
        <result property="userId"    column="user_id"    />
        <result property="teacherCode"    column="teacher_code"    />
        <result property="teacherName"    column="teacher_name"    />
        <result property="gender"    column="gender"    />
        <result property="phone"    column="phone"    />
        <result property="idCard"    column="id_card"    />
        <result property="education"    column="education"    />
        <result property="major"    column="major"    />
        <result property="hireDate"    column="hire_date"    />
        <result property="position"    column="position"    />
        <result property="baseSalary"    column="base_salary"    />
        <result property="faceId"    column="face_id"    />
        <result property="wechatOpenid"    column="wechat_openid"    />
        <result property="dingtalkUserId"    column="dingtalk_user_id"    />
        <result property="avatar"    column="avatar"    />
        <result property="email"    column="email"    />
        <result property="jobNumber"    column="job_number"    />
        <result property="isWechatBound"    column="is_wechat_bound"    />
        <result property="wechatBindTime"    column="wechat_bind_time"    />
        <result property="status"    column="status"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgTeacherVo">
        select teacher_id, user_id, teacher_code, teacher_name, gender, phone, id_card, education, major, hire_date, position, base_salary, face_id, wechat_openid, dingtalk_user_id, avatar, email, job_number, is_wechat_bound, wechat_bind_time, status, com_id, create_by, create_time, update_by, update_time, remark from kg_teacher
    </sql>

    <select id="selectKgTeacherList" parameterType="KgTeacher" resultMap="KgTeacherResult">
        <include refid="selectKgTeacherVo"/>
        <where>  
            <if test="userId != null "> and user_id = #{userId}</if>
            <if test="teacherCode != null  and teacherCode != ''"> and teacher_code = #{teacherCode}</if>
            <if test="teacherName != null  and teacherName != ''"> and teacher_name like concat('%', #{teacherName}, '%')</if>
            <if test="gender != null  and gender != ''"> and gender = #{gender}</if>
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="idCard != null  and idCard != ''"> and id_card = #{idCard}</if>
            <if test="education != null  and education != ''"> and education = #{education}</if>
            <if test="major != null  and major != ''"> and major = #{major}</if>
            <if test="hireDate != null "> and hire_date = #{hireDate}</if>
            <if test="position != null  and position != ''"> and position = #{position}</if>
            <if test="baseSalary != null "> and base_salary = #{baseSalary}</if>
            <if test="faceId != null  and faceId != ''"> and face_id = #{faceId}</if>
            <if test="wechatOpenid != null  and wechatOpenid != ''"> and wechat_openid = #{wechatOpenid}</if>
            <if test="isWechatBound != null "> and is_wechat_bound = #{isWechatBound}</if>
            <if test="wechatBindTime != null "> and wechat_bind_time = #{wechatBindTime}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgTeacherById" parameterType="Long" resultMap="KgTeacherResult">
        <include refid="selectKgTeacherVo"/>
        where teacher_id = #{teacherId}
    </select>
    
    <select id="selectKgTeacherByDingtalkUserId" parameterType="String" resultMap="KgTeacherResult">
        <include refid="selectKgTeacherVo"/>
        where dingtalk_user_id = #{dingtalkUserId}
    </select>
        
    <insert id="insertKgTeacher" parameterType="KgTeacher" useGeneratedKeys="true" keyProperty="teacherId">
        insert into kg_teacher
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="userId != null">user_id,</if>
            <if test="teacherCode != null and teacherCode != ''">teacher_code,</if>
            <if test="teacherName != null and teacherName != ''">teacher_name,</if>
            <if test="gender != null">gender,</if>
            <if test="phone != null">phone,</if>
            <if test="idCard != null">id_card,</if>
            <if test="education != null">education,</if>
            <if test="major != null">major,</if>
            <if test="hireDate != null">hire_date,</if>
            <if test="position != null">position,</if>
            <if test="baseSalary != null">base_salary,</if>
            <if test="faceId != null">face_id,</if>
            <if test="wechatOpenid != null">wechat_openid,</if>
            <if test="dingtalkUserId != null">dingtalk_user_id,</if>
            <if test="avatar != null">avatar,</if>
            <if test="email != null">email,</if>
            <if test="jobNumber != null">job_number,</if>
            <if test="isWechatBound != null">is_wechat_bound,</if>
            <if test="wechatBindTime != null">wechat_bind_time,</if>
            <if test="status != null">status,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="userId != null">#{userId},</if>
            <if test="teacherCode != null and teacherCode != ''">#{teacherCode},</if>
            <if test="teacherName != null and teacherName != ''">#{teacherName},</if>
            <if test="gender != null">#{gender},</if>
            <if test="phone != null">#{phone},</if>
            <if test="idCard != null">#{idCard},</if>
            <if test="education != null">#{education},</if>
            <if test="major != null">#{major},</if>
            <if test="hireDate != null">#{hireDate},</if>
            <if test="position != null">#{position},</if>
            <if test="baseSalary != null">#{baseSalary},</if>
            <if test="faceId != null">#{faceId},</if>
            <if test="wechatOpenid != null">#{wechatOpenid},</if>
            <if test="dingtalkUserId != null">#{dingtalkUserId},</if>
            <if test="avatar != null">#{avatar},</if>
            <if test="email != null">#{email},</if>
            <if test="jobNumber != null">#{jobNumber},</if>
            <if test="isWechatBound != null">#{isWechatBound},</if>
            <if test="wechatBindTime != null">#{wechatBindTime},</if>
            <if test="status != null">#{status},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgTeacher" parameterType="KgTeacher">
        update kg_teacher
        <trim prefix="SET" suffixOverrides=",">
            <if test="userId != null">user_id = #{userId},</if>
            <if test="teacherCode != null and teacherCode != ''">teacher_code = #{teacherCode},</if>
            <if test="teacherName != null and teacherName != ''">teacher_name = #{teacherName},</if>
            <if test="gender != null">gender = #{gender},</if>
            <if test="phone != null">phone = #{phone},</if>
            <if test="idCard != null">id_card = #{idCard},</if>
            <if test="education != null">education = #{education},</if>
            <if test="major != null">major = #{major},</if>
            <if test="hireDate != null">hire_date = #{hireDate},</if>
            <if test="position != null">position = #{position},</if>
            <if test="baseSalary != null">base_salary = #{baseSalary},</if>
            <if test="faceId != null">face_id = #{faceId},</if>
            <if test="wechatOpenid != null">wechat_openid = #{wechatOpenid},</if>
            <if test="dingtalkUserId != null">dingtalk_user_id = #{dingtalkUserId},</if>
            <if test="avatar != null">avatar = #{avatar},</if>
            <if test="email != null">email = #{email},</if>
            <if test="jobNumber != null">job_number = #{jobNumber},</if>
            <if test="isWechatBound != null">is_wechat_bound = #{isWechatBound},</if>
            <if test="wechatBindTime != null">wechat_bind_time = #{wechatBindTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where teacher_id = #{teacherId}
    </update>

    <delete id="deleteKgTeacherById" parameterType="Long">
        delete from kg_teacher where teacher_id = #{teacherId}
    </delete>

    <delete id="deleteKgTeacherByIds" parameterType="String">
        delete from kg_teacher where teacher_id in 
        <foreach item="teacherId" collection="array" open="(" separator="," close=")">
            #{teacherId}
        </foreach>
    </delete>
    
</mapper>
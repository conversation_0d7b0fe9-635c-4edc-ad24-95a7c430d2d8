package com.cl.project.business.mapper;

import java.util.List;
import java.util.Date;
import org.apache.ibatis.annotations.Param;
import com.cl.project.business.domain.KgStudentAttendance;
import com.cl.project.business.domain.dto.StudentAttendanceOverviewDto;

/**
 * 学生考勤记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface KgStudentAttendanceMapper 
{
    /**
     * 查询学生考勤记录
     * 
     * @param attendanceId 学生考勤记录ID
     * @return 学生考勤记录
     */
    public KgStudentAttendance selectKgStudentAttendanceById(Long attendanceId);

    /**
     * 查询学生考勤记录列表
     * 
     * @param kgStudentAttendance 学生考勤记录
     * @return 学生考勤记录集合
     */
    public List<KgStudentAttendance> selectKgStudentAttendanceList(KgStudentAttendance kgStudentAttendance);

    /**
     * 新增学生考勤记录
     * 
     * @param kgStudentAttendance 学生考勤记录
     * @return 结果
     */
    public int insertKgStudentAttendance(KgStudentAttendance kgStudentAttendance);

    /**
     * 修改学生考勤记录
     * 
     * @param kgStudentAttendance 学生考勤记录
     * @return 结果
     */
    public int updateKgStudentAttendance(KgStudentAttendance kgStudentAttendance);

    /**
     * 删除学生考勤记录
     * 
     * @param attendanceId 学生考勤记录ID
     * @return 结果
     */
    public int deleteKgStudentAttendanceById(Long attendanceId);

    /**
     * 批量删除学生考勤记录
     * 
     * @param attendanceIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteKgStudentAttendanceByIds(Long[] attendanceIds);

    /**
     * 查询学生考勤概览列表
     * 展示所有学生及其指定日期的考勤状态
     * 
     * @param attendanceDate 考勤日期
     * @param studentName 学生姓名
     * @param classId 班级ID
     * @param attendanceStatus 考勤状态
     * @param dataSource 数据来源
     * @return 学生考勤概览集合
     */
    public List<StudentAttendanceOverviewDto> selectStudentAttendanceOverview(
        @Param("attendanceDate") Date attendanceDate, 
        @Param("studentName") String studentName, 
        @Param("classId") Long classId, 
        @Param("attendanceStatus") String attendanceStatus, 
        @Param("dataSource") String dataSource);

    /**
     * 查询指定学生某天的所有手动签到记录
     * 
     * @param studentId 学生ID
     * @param dateFrom 当天起始时间
     * @param dateTo 当天结束时间
     * @return 手动签到记录列表
     */
    List<KgStudentAttendance> selectManualByStudentAndDate(
        @Param("studentId") Long studentId, 
        @Param("dateFrom") Date dateFrom, 
        @Param("dateTo") Date dateTo);

    /**
     * 批量确认学生考勤
     * 
     * @param attendanceIds 考勤记录ID列表
     * @param confirmedBy 确认人ID
     * @return 影响行数
     */
    int batchConfirmAttendance(
        @Param("attendanceIds") List<Long> attendanceIds, 
        @Param("confirmedBy") Long confirmedBy);
}

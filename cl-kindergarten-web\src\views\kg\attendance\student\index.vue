<template>
  <div class="app-container" style="padding: 0; width: 100%;">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px">
      <el-form-item label="学生姓名" prop="studentName">
        <el-input
          v-model="queryParams.studentName"
          placeholder="请输入学生姓名"
          clearable
          size="small"
          @keyup.enter.native="getOverviewList"
        />
      </el-form-item>
      <el-form-item label="班级" prop="classId">
        <el-select v-model="queryParams.classId" placeholder="请选择班级" clearable size="small">
          <el-option
            v-for="item in classList"
            :key="item.classId"
            :label="item.className"
            :value="item.classId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="考勤日期" prop="attendanceDate">
        <el-date-picker clearable size="small" style="width: 200px"
          v-model="queryParams.attendanceDate"
          type="date"
          placeholder="选择日期"
          value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="考勤状态" prop="attendanceStatus">
        <el-select v-model="queryParams.attendanceStatus" placeholder="请选择考勤状态" clearable size="small">
          <el-option label="出勤" value="1" />
          <el-option label="缺勤" value="3" />
          <el-option label="迟到" value="2" />
          <el-option label="早退" value="5" />
          <el-option label="病假" value="6" />
          <el-option label="休假" value="8" />
          <el-option label="请假" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="数据来源" prop="dataSource">
        <el-select v-model="queryParams.dataSource" placeholder="请选择数据来源" clearable size="small">
          <el-option label="钉钉打卡" value="dingtalk" />
          <el-option label="手动签到" value="manual" />
          <el-option label="混合" value="mixed" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getOverviewList">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >批量签到</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-refresh"
          size="mini"
          @click="handleDingtalkSync"
        >同步钉钉</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-check"
          size="mini"
          :disabled="multipleSelection.length === 0"
          @click="handleConfirmAttendance"
        >批量考勤确认</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col>
    </el-row>

    <!-- 学生考勤主表格 -->
    <el-table
      class="attendance-table"
      style="width: 100%;"
      table-layout="fixed"
      v-loading="loading"
      :data="attendanceList"
      @selection-change="handleSelectionChange"
      :expand-row-keys="expandedRows"
      :row-key="getRowKey"
      @expand-change="handleExpandChange">
      <el-table-column type="expand">
        <template slot-scope="props">
          <div class="expand-content">
            <!-- 钉钉打卡记录 -->
            <div class="source-section">
              <h4><i class="el-icon-mobile-phone"></i> 钉钉打卡记录</h4>
              <el-table :data="props.row.dingtalkRecords" size="mini" border>
                <el-table-column prop="checkTime" label="打卡时间" width="180">
                  <template slot-scope="scope">
                    {{ parseTime(scope.row.checkTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
                  </template>
                </el-table-column>
                <el-table-column prop="checkType" label="打卡类型" width="100">
                  <template slot-scope="scope">
                    <el-tag v-if="scope.row.checkType === 'OnDuty'" type="success" size="mini">上班</el-tag>
                    <el-tag v-else-if="scope.row.checkType === 'OffDuty'" type="info" size="mini">下班</el-tag>
                    <el-tag v-else type="warning" size="mini">{{ scope.row.checkType }}</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="locationResult" label="打卡地点" show-overflow-tooltip />
                <el-table-column prop="feeType" label="费用类型" width="100">
                  <template slot-scope="scope">
                    <el-tag v-if="scope.row.feeType === 'tuition'" type="primary" size="mini">园费</el-tag>
                    <el-tag v-else-if="scope.row.feeType === 'course'" type="success" size="mini">托管费</el-tag>
                    <el-tag v-else type="info" size="mini">其他</el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="processStatus" label="处理状态" width="100">
                  <template slot-scope="scope">
                    <el-tag v-if="scope.row.processStatus === 'processed'" type="success" size="mini">已处理</el-tag>
                    <el-tag v-else-if="scope.row.processStatus === 'pending'" type="warning" size="mini">待处理</el-tag>
                    <el-tag v-else type="info" size="mini">{{ scope.row.processStatus }}</el-tag>
                  </template>
                </el-table-column>
              </el-table>
              <div v-if="!props.row.dingtalkRecords || props.row.dingtalkRecords.length === 0" class="no-data">
                <span class="text-muted">暂无钉钉打卡记录</span>
              </div>
            </div>
            
            <!-- 手动签到记录 -->
            <div class="source-section">
              <h4><i class="el-icon-edit"></i> 手动签到记录</h4>
              <el-table :data="props.row.manualRecords" size="mini" border>
                <el-table-column prop="checkInTime" label="签到时间" width="180">
                  <template slot-scope="scope">
                    {{ parseTime(scope.row.checkInTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
                  </template>
                </el-table-column>
                <el-table-column prop="checkOutTime" label="签退时间" width="180">
                  <template slot-scope="scope">
                    {{ scope.row.checkOutTime ? parseTime(scope.row.checkOutTime, '{y}-{m}-{d} {h}:{i}:{s}') : '-' }}
                  </template>
                </el-table-column>
                <el-table-column prop="attendanceStatus" label="考勤状态" width="100">
                  <template slot-scope="scope">
                    <el-tag :type="getStatusTagType(scope.row.attendanceStatus)" size="mini">
                      {{ getStatusText(scope.row.attendanceStatus) }}
                    </el-tag>
                  </template>
                </el-table-column>
                <el-table-column prop="remark" label="备注" show-overflow-tooltip />
                <el-table-column label="确认状态" width="120">
                  <template slot-scope="scope">
                    <el-tag v-if="scope.row.isConfirmed === 1" type="success" size="mini">已确认</el-tag>
                    <el-button
                      v-else
                      size="mini"
                      type="text"
                      @click="handleSingleConfirm(scope.row)"
                    >考勤确认</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div v-if="!props.row.manualRecords || props.row.manualRecords.length === 0" class="no-data">
                <span class="text-muted">暂无手动签到记录</span>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="学生姓名" align="center" prop="studentName" width="120" />
      <el-table-column label="班级" align="center" prop="className" width="100" />
      <el-table-column label="考勤日期" align="center" prop="attendanceDate" width="120">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.attendanceDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="签到时间" align="center" prop="checkInTime" width="150">
        <template slot-scope="scope">
          <span v-if="scope.row.checkInTime">{{ parseTime(scope.row.checkInTime, '{h}:{i}:{s}') }}</span>
          <span v-else class="text-muted">-</span>
        </template>
      </el-table-column>
      <el-table-column label="签退时间" align="center" prop="checkOutTime" width="150">
        <template slot-scope="scope">
          <span v-if="scope.row.checkOutTime">{{ parseTime(scope.row.checkOutTime, '{h}:{i}:{s}') }}</span>
          <span v-else class="text-muted">-</span>
        </template>
      </el-table-column>
      <el-table-column label="考勤状态" align="center" prop="attendanceStatus" width="100">
        <template slot-scope="scope">
          <el-tag :type="getStatusTagType(scope.row.attendanceStatus, scope.row.hasAttendance)" size="mini">
            {{ getStatusText(scope.row.attendanceStatus, scope.row.hasAttendance) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="数据来源" align="center" prop="dataSource" width="100">
        <template slot-scope="scope">
          <el-tag :type="getSourceTagType(scope.row.dataSource)" size="mini">
            {{ getSourceText(scope.row.dataSource) }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="确认状态" align="center" prop="isConfirmed" width="100">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isConfirmed === 1" type="success" size="mini">已确认</el-tag>
          <el-tag v-else type="warning" size="mini">待确认</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.isConfirmed === 0"
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleConfirm(scope.row)"
          >考勤确认</el-button>
          <el-button
            v-if="scope.row.dingtalkUserId"
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="handleGenerate(scope.row)"
          >重新生成</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getOverviewList"
    />

    <!-- 钉钉考勤同步对话框 -->
    <el-dialog title="钉钉考勤同步" :visible.sync="dingtalkSyncVisible" width="600px" append-to-body class="sync-dialog">
      <el-form :model="syncForm" label-width="100px">
        <el-form-item label="同步日期">
          <el-col :span="11">
            <el-date-picker
              v-model="syncForm.workDateFrom"
              type="date"
              placeholder="开始日期"
              value-format="yyyy-MM-dd"
              style="width: 100%;"
            />
          </el-col>
          <el-col class="line" :span="2" style="text-align: center;">-</el-col>
          <el-col :span="11">
            <el-date-picker
              v-model="syncForm.workDateTo"
              type="date"
              placeholder="结束日期"
              value-format="yyyy-MM-dd"
              style="width: 100%;"
            />
          </el-col>
        </el-form-item>
        <el-form-item label="指定用户" prop="userIdList">
          <el-select
            v-model="syncForm.userIdList"
            multiple
            filterable
            placeholder="留空则同步所有用户"
            style="width: 100%;"
          >
            <el-option
              v-for="item in studentList"
              v-if="item.dingtalkUserId"
              :key="item.dingtalkUserId"
              :label="item.studentName"
              :value="item.dingtalkUserId"
            >
              <span style="float: left">{{ item.studentName }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.dingtalkUserId }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-alert
            title="同步说明"
            type="info"
            :closable="false"
            show-icon
          >
            <div slot="default">
              <p>• 将从钉钉平台获取指定日期范围内的打卡记录</p>
              <p>• 如不指定用户，将同步所有用户的打卡记录</p>
              <p>• 已存在的记录将被跳过，不会重复同步</p>
            </div>
          </el-alert>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDingtalkSync">取 消</el-button>
        <el-button type="primary" @click="performDingtalkSync" :loading="dingtalkSyncLoading">
          {{ dingtalkSyncLoading ? '同步中...' : '开始同步' }}
        </el-button>
      </div>
    </el-dialog>

    <!-- 添加或修改学生考勤对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="学生" prop="studentIds">
          <el-select v-model="form.studentIds" multiple filterable placeholder="请选择学生">
            <el-option
              v-for="item in studentList"
              :key="item.studentId"
              :label="item.studentName + '(' + item.className + ')'"
              :value="item.studentId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="考勤日期" prop="attendanceDate">
          <el-date-picker clearable size="small" style="width: 200px"
            v-model="form.attendanceDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择考勤日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="考勤状态" prop="attendanceStatus">
          <el-radio-group v-model="form.attendanceStatus">
            <el-radio label="1">签到</el-radio>
            <el-radio label="3">缺勤</el-radio>
            <el-radio label="4">请假</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listStudentAttendance, getStudentAttendance, delStudentAttendance, addStudentAttendance, updateStudentAttendance, exportStudentAttendance, getStudentAttendanceOverview, batchStudentCheckin, batchConfirmStudentAttendance, confirmStudentAttendance, listAllStudent } from "@/api/kg/attendance/student";
import { listClass } from "@/api/kg/student/class";
import { syncAttendanceByUsers } from '@/api/business/dingtalk'

export default {
  name: "StudentAttendance",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 多选选中的学生数据
      multipleSelection: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 学生考勤表格数据
      attendanceList: [],
      // 班级列表
      classList: [],
      // 学生列表
      studentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 钉钉同步对话框显示
      dingtalkSyncVisible: false,
      // 钉钉同步加载状态
      dingtalkSyncLoading: false,
      // 钉钉同步表单
      syncForm: {
        workDateFrom: undefined,
        workDateTo: undefined,
        userIdList: []
      },
      // 表格展开的行
      expandedRows: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        studentName: undefined,
        classId: undefined,
        attendanceDate: new Date().toISOString().slice(0, 10), // 默认当天
        attendanceStatus: undefined,
        dataSource: undefined
      },
      // 表单参数
      form: {},
      // 表单校验
      rules: {
        studentIds: [
          { required: true, message: "学生不能为空", trigger: "change" }
        ],
        attendanceDate: [
          { required: true, message: "考勤日期不能为空", trigger: "blur" }
        ],
        attendanceStatus: [
          { required: true, message: "考勤状态不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getOverviewList();
    this.getClassList();
    this.getStudentList();
  },
  methods: {
    /** 获取表格行唯一键 */
    getRowKey(row) {
      // 使用学生 ID + 考勤日期作为唯一键
      if (row.studentId && row.attendanceDate) {
        return `${row.studentId}_${row.attendanceDate}`;
      }
      // 如果没有这些字段，使用索引作为备选
      return this.attendanceList.indexOf(row);
    },
    /** el-table 行展开事件处理，兼容handleRowExpand逻辑 */
    handleExpandChange(row, expandedRows) {
      this.handleRowExpand(row, expandedRows);
    },
    /** 查询学生考勤列表 */
    /** 获取学生考勤概览列表 */
    getOverviewList() {
      this.loading = true;
      getStudentAttendanceOverview(this.queryParams).then(response => {
        this.attendanceList = response.rows;
        this.total = response.total;
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 查询班级列表 */
    getClassList() {
      listClass().then(response => {
        this.classList = response.rows;
      });
    },
    /** 查询学生列表 */
    getStudentList() {
      listAllStudent().then(response => {
        this.studentList = response.data;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        attendanceId: undefined,
        studentIds: [],
        attendanceDate: undefined,
        attendanceStatus: "1",
        remark: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getOverviewList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.multipleSelection = selection;
      this.ids = selection.map(item => item.attendanceId);
      this.single = selection.length != 1;
      this.multiple = !selection.length;
    },
    /** 手动签到按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "批量学生签到";
      // 默认今日日期
      this.form.attendanceDate = this.parseTime(new Date(), '{y}-{m}-{d}');
    },
    /** 钉钉考勤同步按钮操作 */
    handleDingtalkSync() {
      const today = new Date().toISOString().slice(0, 10);
      this.syncForm.workDateFrom = today;
      this.syncForm.workDateTo = today;
      this.syncForm.userIdList = [];
      this.dingtalkSyncVisible = true;
    },
    /** 执行钉钉考勤同步 */
    async performDingtalkSync() {
      if (!this.syncForm.workDateFrom || !this.syncForm.workDateTo) {
        this.$message.error('请选择同步日期范围');
        return;
      }
      
      this.dingtalkSyncLoading = true;
      try {
        // 确保日期格式正确
        const requestData = {
          workDateFrom: this.syncForm.workDateFrom.includes(' ') ? 
            this.syncForm.workDateFrom : this.syncForm.workDateFrom + ' 00:00:00',
          workDateTo: this.syncForm.workDateTo.includes(' ') ? 
            this.syncForm.workDateTo : this.syncForm.workDateTo + ' 23:59:59',
          userIdList: this.syncForm.userIdList || []
        };
        
        let response;
        if (requestData.userIdList.length > 0) {
          // 同步指定用户
          response = await syncAttendanceByUsers(requestData);
        } else {
          // 同步所有用户
          response = await syncAttendanceByUsers(requestData);
        }
        
        this.$message.success(`同步完成，共处理 ${response.data} 条记录`);
        this.dingtalkSyncVisible = false;
        this.getOverviewList(); // 刷新列表
      } catch (error) {
        console.error('钉钉考勤同步失败:', error);
        const errorMsg = error.response && error.response.data && error.response.data.msg ? 
          error.response.data.msg : (error.message || '未知错误');
        this.$message.error('同步失败: ' + errorMsg);
      } finally {
        this.dingtalkSyncLoading = false;
      }
    },
    /** 取消钉钉同步 */
    cancelDingtalkSync() {
      this.dingtalkSyncVisible = false;
      this.syncForm = {
        workDateFrom: undefined,
        workDateTo: undefined,
        userIdList: []
      };
    },
    /** 单条手动考勤确认按钮操作 */
    handleSingleConfirm(record) {
      this.$confirm('确认该条考勤记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用单条确认接口
        this.$message.success('确认成功');
        this.getOverviewList();
      }).catch(() => {});
    },
    /** 仍保留批量确认考勤按钮操作 */
    handleConfirm(row) {
      const attendanceIds = row ? [row.attendanceId] : this.ids;
      if (attendanceIds.length === 0) {
        this.$message.error('请选择要确认的考勤记录');
        return;
      }
      this.$confirm('确认选中的考勤记录?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用确认接口
        this.$message.success('确认成功');
        this.getOverviewList();
      }).catch(() => {});
    },
    /** 确认考勤按钮操作 */
    handleConfirmAttendance() {
      if (this.multipleSelection.length === 0) {
        this.$message.error('请选择要确认的考勤记录');
        return;
      }
      
      const attendanceIds = this.multipleSelection.map(item => item.attendanceId);
      this.$confirm(`确认选中的 ${attendanceIds.length} 条考勤记录?`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        batchConfirmStudentAttendance({ attendanceIds }).then(response => {
          this.$message.success(`成功确认 ${response.data} 条记录`);
          this.getOverviewList();
        }).catch(error => {
          console.error('批量确认失败:', error);
          this.$message.error('批量确认失败');
        });
      }).catch(() => {});
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const attendanceId = row.attendanceId || this.ids;
      getStudentAttendance(attendanceId).then(response => {
        this.form = response.data;
        // 单个修改时转换为数组格式
        if (this.form.studentId) {
          this.form.studentIds = [this.form.studentId];
        }
        this.open = true;
        this.title = "修改学生考勤";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.attendanceId != undefined) {
            // 修改单个记录
            const formData = { ...this.form };
            if (formData.studentIds && formData.studentIds.length > 0) {
              formData.studentId = formData.studentIds[0];
            }
            updateStudentAttendance(formData).then(response => {
              this.$message.success("修改成功");
              this.open = false;
              this.getOverviewList();
            });
          } else {
            // 批量签到
            batchStudentCheckin(this.form).then(response => {
              this.$message.success(`成功处理 ${response.data} 条记录`);
              this.open = false;
              this.getOverviewList();
            }).catch(error => {
              console.error('批量签到失败:', error);
              this.$message.error('批量签到失败');
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const attendanceIds = row.attendanceId || this.ids;
      this.$confirm('是否确认删除学生考勤记录编号为"' + attendanceIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          return delStudentAttendance(attendanceIds);
        }).then(() => {
          this.getOverviewList();
          this.$message.success("删除成功");
        }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有学生考勤数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(() => {
          return exportStudentAttendance(queryParams);
        }).then(response => {
          this.download(response.msg);
        }).catch(() => {});
    },
    /** 表格行展开事件 */
    handleRowExpand(row, expandedRows) {
      if (expandedRows.includes(row)) {
        this.loadExpandedData(row);
      }
    },
    /** 加载展开行的详细数据 */
    loadExpandedData(row) {
      // 模拟加载钉钉和手动记录数据
      if (!row.dingtalkRecords) {
        row.dingtalkRecords = [];
      }
      if (!row.manualRecords) {
        row.manualRecords = [];
      }
    },
    /** 获取数据来源标签类型 */
    getSourceTagType(source) {
      switch (source) {
        case 'dingtalk': return 'success';
        case 'manual': return 'primary';
        case 'mixed': return 'warning';
        default: return 'info';
      }
    },
    /** 获取数据来源文本 */
    getSourceText(source) {
      switch (source) {
        case 'dingtalk': return '钉钉';
        case 'manual': return '手动';
        case 'mixed': return '混合';
        default: return '未知';
      }
    },
    /** 获取考勤状态标签类型 */
    /** 状态标签类型直接由后端返回 */
    getStatusTagType(status, hasAttendance) {
      if (!hasAttendance) {
        return 'info'; // 无考勤记录
      }
      switch (parseInt(status)) {
        case 1: return 'success'; // 出勤
        case 2: return 'warning'; // 迟到
        case 3: return 'danger';  // 缺勤
        case 4: return 'info';    // 请假
        case 5: return 'warning'; // 早退
        case 6: return 'info';    // 病假
        case 8: return 'info';    // 休假
        default: return 'info';
      }
    },
    /** 获取考勤状态文本 */
    getStatusText(status, hasAttendance) {
      if (!hasAttendance) {
        return '无记录';
      }
      switch (parseInt(status)) {
        case 1: return '出勤';
        case 2: return '迟到';
        case 3: return '缺勤';
        case 4: return '请假';
        case 5: return '早退';
        case 6: return '病假';
        case 8: return '休假';
        default: return '未知';
      }
    },
    /** 重新生成按钮操作 */
    async handleGenerate(row) {
      const attendanceDate = row.attendanceDate;
      // 尝试兼容不同字段名
      const dingtalkUserId = row.dingtalkUserId;
      if (!attendanceDate || !dingtalkUserId) {
        this.$message.error('缺少考勤日期或钉钉用户ID，无法同步');
        return;
      }
      this.$confirm('是否根据钉钉重新同步该学生当天考勤数据？', "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(async () => {
          try {
            this.loading = true;
            const workDateFrom = attendanceDate + ' 00:00:00';
            const workDateTo = attendanceDate + ' 23:59:59';
            const res = await syncAttendanceByUsers({
              workDateFrom,
              workDateTo,
              userIdList: [dingtalkUserId]
            });
            if (res && res.code === 200) {
              this.$message.success('钉钉同步成功');
              this.getOverviewList();
            } else {
              this.$message.error(res.msg || '钉钉同步失败');
            }
          } catch (e) {
            this.$message.error('钉钉同步异常: ' + (e.response?.data?.msg || e.message));
          } finally {
            this.loading = false;
          }
        }).catch(() => {});
    },
    /** 单个确认考勤 */
    handleConfirm(row) {
      const attendanceId = row.attendanceId;
      this.$confirm('确认该考勤记录？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        confirmStudentAttendance(attendanceId).then(response => {
          this.$message.success('确认成功');
          this.getOverviewList();
        }).catch(error => {
          console.error('确认失败:', error);
          this.$message.error('确认失败');
        });
      }).catch(() => {});
    }
  }
};
</script>

<style scoped>
.attendance-container {
  padding: 20px;
}

.search-form {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.operation-buttons {
  margin-bottom: 20px;
}

.operation-buttons .el-button {
  margin-right: 10px;
}

.app-container, .attendance-table {
  width: 100%;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

.attendance-table {
  background: white;
  border-radius: 4px;
}

.expand-content {
  padding: 20px;
  background-color: #f8f9fa;
  border-radius: 6px;
  margin: 10px 0;
}

.source-section {
  margin-bottom: 20px;
}

.source-section:last-child {
  margin-bottom: 0;
}

.source-section h4 {
  margin: 0 0 15px 0;
  color: #303133;
  font-size: 14px;
  font-weight: 600;
  display: flex;
  align-items: center;
}

.source-section h4 i {
  margin-right: 8px;
  color: #409EFF;
}

.expanded-content {
  padding: 20px;
  background: #fafafa;
}



.detail-section {
  margin-bottom: 20px;
}

.detail-section h4 {
  margin-bottom: 10px;
  color: #303133;
  font-weight: 600;
}

.detail-table {
  margin-bottom: 15px;
}

.no-data {
  text-align: center;
  padding: 20px;
  color: #909399;
  font-size: 13px;
  background-color: #fff;
  border: 1px dashed #e4e7ed;
  border-radius: 4px;
}

.text-muted {
  color: #909399;
}

.source-tag {
  margin-left: 8px;
}

.status-tag {
  margin-right: 8px;
}

.data-integrity-tag {
  margin-left: 5px;
}

.confirmation-status {
  display: flex;
  align-items: center;
}

.confirmation-status .el-tag {
  margin-right: 5px;
}

.student-name {
  font-weight: 600;
  color: #303133;
}

.attendance-date {
  color: #606266;
}

.work-hours {
  font-weight: 600;
  color: #409eff;
}

.check-times {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.expanded-table {
  font-size: 12px;
}

.expanded-table .el-table__row {
  background: #f9f9f9;
}

.location-info {
  color: #909399;
  font-size: 12px;
}

.remark-text {
  color: #909399;
  font-style: italic;
}

/* 对话框样式 */
.el-dialog__body {
  padding: 20px;
}

.el-form-item {
  margin-bottom: 18px;
}

.sync-dialog .el-form-item {
  margin-bottom: 20px;
}

.sync-dialog .el-date-picker {
  width: 100%;
}

.sync-dialog .el-select {
  width: 100%;
}

/* 响应式布局 */
@media (max-width: 768px) {
  .expand-content {
    padding: 15px;
  }
  
  .source-section h4 {
    font-size: 13px;
  }
  
  .el-table .cell {
    padding: 0 4px;
  }
  
  .attendance-container {
    padding: 10px;
  }
  
  .search-form {
    padding: 15px;
  }
}
</style>

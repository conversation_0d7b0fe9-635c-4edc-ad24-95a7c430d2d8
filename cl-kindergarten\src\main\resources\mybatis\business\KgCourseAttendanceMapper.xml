<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgCourseAttendanceMapper">
    
    <resultMap type="KgCourseAttendance" id="KgCourseAttendanceResult">
        <result property="attendanceId"    column="attendance_id"    />
        <result property="enrollmentId"    column="enrollment_id"    />
        <result property="studentId"    column="student_id"    />
        <result property="courseId"    column="course_id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="attendanceDate"    column="attendance_date"    />
        <result property="startTime"    column="start_time"    />
        <result property="endTime"    column="end_time"    />
        <result property="attendanceStatus"    column="attendance_status"    />
        <result property="checkInMethod"    column="check_in_method"    />
        <result property="isConfirmed"    column="is_confirmed"    />
        <result property="confirmedBy"    column="confirmed_by"    />
        <result property="confirmedTime"    column="confirmed_time"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgCourseAttendanceVo">
        select attendance_id, enrollment_id, student_id, course_id, teacher_id, attendance_date, start_time, end_time, attendance_status, check_in_method, is_confirmed, confirmed_by, confirmed_time, com_id, create_by, create_time, update_by, update_time, remark from kg_course_attendance
    </sql>

    <select id="selectKgCourseAttendanceList" parameterType="KgCourseAttendance" resultMap="KgCourseAttendanceResult">
        <include refid="selectKgCourseAttendanceVo"/>
        <where>  
            <if test="enrollmentId != null "> and enrollment_id = #{enrollmentId}</if>
            <if test="studentId != null "> and student_id = #{studentId}</if>
            <if test="courseId != null "> and course_id = #{courseId}</if>
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="attendanceDate != null "> and attendance_date = #{attendanceDate}</if>
            <if test="startTime != null "> and start_time = #{startTime}</if>
            <if test="endTime != null "> and end_time = #{endTime}</if>
            <if test="attendanceStatus != null  and attendanceStatus != ''"> and attendance_status = #{attendanceStatus}</if>
            <if test="checkInMethod != null  and checkInMethod != ''"> and check_in_method = #{checkInMethod}</if>
            <if test="isConfirmed != null "> and is_confirmed = #{isConfirmed}</if>
            <if test="confirmedBy != null "> and confirmed_by = #{confirmedBy}</if>
            <if test="confirmedTime != null "> and confirmed_time = #{confirmedTime}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgCourseAttendanceById" parameterType="Long" resultMap="KgCourseAttendanceResult">
        <include refid="selectKgCourseAttendanceVo"/>
        where attendance_id = #{attendanceId}
    </select>
        
    <insert id="insertKgCourseAttendance" parameterType="KgCourseAttendance" useGeneratedKeys="true" keyProperty="attendanceId">
        insert into kg_course_attendance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="enrollmentId != null">enrollment_id,</if>
            <if test="studentId != null">student_id,</if>
            <if test="courseId != null">course_id,</if>
            <if test="teacherId != null">teacher_id,</if>
            <if test="attendanceDate != null">attendance_date,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="attendanceStatus != null">attendance_status,</if>
            <if test="checkInMethod != null">check_in_method,</if>
            <if test="isConfirmed != null">is_confirmed,</if>
            <if test="confirmedBy != null">confirmed_by,</if>
            <if test="confirmedTime != null">confirmed_time,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="enrollmentId != null">#{enrollmentId},</if>
            <if test="studentId != null">#{studentId},</if>
            <if test="courseId != null">#{courseId},</if>
            <if test="teacherId != null">#{teacherId},</if>
            <if test="attendanceDate != null">#{attendanceDate},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="attendanceStatus != null">#{attendanceStatus},</if>
            <if test="checkInMethod != null">#{checkInMethod},</if>
            <if test="isConfirmed != null">#{isConfirmed},</if>
            <if test="confirmedBy != null">#{confirmedBy},</if>
            <if test="confirmedTime != null">#{confirmedTime},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgCourseAttendance" parameterType="KgCourseAttendance">
        update kg_course_attendance
        <trim prefix="SET" suffixOverrides=",">
            <if test="enrollmentId != null">enrollment_id = #{enrollmentId},</if>
            <if test="studentId != null">student_id = #{studentId},</if>
            <if test="courseId != null">course_id = #{courseId},</if>
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="attendanceDate != null">attendance_date = #{attendanceDate},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="attendanceStatus != null">attendance_status = #{attendanceStatus},</if>
            <if test="checkInMethod != null">check_in_method = #{checkInMethod},</if>
            <if test="isConfirmed != null">is_confirmed = #{isConfirmed},</if>
            <if test="confirmedBy != null">confirmed_by = #{confirmedBy},</if>
            <if test="confirmedTime != null">confirmed_time = #{confirmedTime},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where attendance_id = #{attendanceId}
    </update>

    <delete id="deleteKgCourseAttendanceById" parameterType="Long">
        delete from kg_course_attendance where attendance_id = #{attendanceId}
    </delete>

    <delete id="deleteKgCourseAttendanceByIds" parameterType="String">
        delete from kg_course_attendance where attendance_id in 
        <foreach item="attendanceId" collection="array" open="(" separator="," close=")">
            #{attendanceId}
        </foreach>
    </delete>
    
</mapper>
package com.cl.project.business.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 教师工资对象 kg_teacher_salary
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgTeacherSalary extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 工资ID */
    private Long salaryId;

    /** 教师ID，关联kg_teacher.teacher_id */
    @Excel(name = "教师ID，关联kg_teacher.teacher_id")
    private Long teacherId;

    /** 工资年份 */
    @Excel(name = "工资年份")
    private Long salaryYear;

    /** 工资月份 */
    @Excel(name = "工资月份")
    private Long salaryMonth;

    /** 基本工资 */
    @Excel(name = "基本工资")
    private BigDecimal baseSalary;

    /** 出勤天数 */
    @Excel(name = "出勤天数")
    private Long attendanceDays;

    /** 应出勤天数 */
    @Excel(name = "应出勤天数")
    private Long totalWorkDays;

    /** 满勤奖 */
    @Excel(name = "满勤奖")
    private BigDecimal attendanceBonus;

    /** 课时费 */
    @Excel(name = "课时费")
    private BigDecimal courseBonus;

    /** 报名奖励 */
    @Excel(name = "报名奖励")
    private BigDecimal enrollmentBonus;

    /** 出勤率奖励 */
    @Excel(name = "出勤率奖励")
    private BigDecimal attendanceRateBonus;

    /** 新生奖励 */
    @Excel(name = "新生奖励")
    private BigDecimal newStudentBonus;

    /** 退园扣款 */
    @Excel(name = "退园扣款")
    private BigDecimal withdrawalPenalty;

    /** 社保代扣 */
    @Excel(name = "社保代扣")
    private BigDecimal socialInsurance;

    /** 绩效积分 */
    @Excel(name = "绩效积分")
    private BigDecimal performanceScore;

    /** 其他奖励 */
    @Excel(name = "其他奖励")
    private BigDecimal otherBonus;

    /** 其他扣款 */
    @Excel(name = "其他扣款")
    private BigDecimal otherDeduction;

    /** 应发工资 */
    @Excel(name = "应发工资")
    private BigDecimal grossSalary;

    /** 实发工资 */
    @Excel(name = "实发工资")
    private BigDecimal netSalary;

    /** 工资状态（calculated已计算、confirmed已确认、paid已发放） */
    @Excel(name = "工资状态", readConverterExp = "c=alculated已计算、confirmed已确认、paid已发放")
    private String salaryStatus;

    /** 确认人ID，关联kg_teacher.teacher_id */
    @Excel(name = "确认人ID，关联kg_teacher.teacher_id")
    private Long confirmedBy;

    /** 确认时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date confirmedTime;

    /** 发放时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发放时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date paidTime;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    public void setSalaryId(Long salaryId) 
    {
        this.salaryId = salaryId;
    }

    public Long getSalaryId() 
    {
        return salaryId;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setSalaryYear(Long salaryYear) 
    {
        this.salaryYear = salaryYear;
    }

    public Long getSalaryYear() 
    {
        return salaryYear;
    }
    public void setSalaryMonth(Long salaryMonth) 
    {
        this.salaryMonth = salaryMonth;
    }

    public Long getSalaryMonth() 
    {
        return salaryMonth;
    }
    public void setBaseSalary(BigDecimal baseSalary) 
    {
        this.baseSalary = baseSalary;
    }

    public BigDecimal getBaseSalary() 
    {
        return baseSalary;
    }
    public void setAttendanceDays(Long attendanceDays) 
    {
        this.attendanceDays = attendanceDays;
    }

    public Long getAttendanceDays() 
    {
        return attendanceDays;
    }
    public void setTotalWorkDays(Long totalWorkDays) 
    {
        this.totalWorkDays = totalWorkDays;
    }

    public Long getTotalWorkDays() 
    {
        return totalWorkDays;
    }
    public void setAttendanceBonus(BigDecimal attendanceBonus) 
    {
        this.attendanceBonus = attendanceBonus;
    }

    public BigDecimal getAttendanceBonus() 
    {
        return attendanceBonus;
    }
    public void setCourseBonus(BigDecimal courseBonus) 
    {
        this.courseBonus = courseBonus;
    }

    public BigDecimal getCourseBonus() 
    {
        return courseBonus;
    }
    public void setEnrollmentBonus(BigDecimal enrollmentBonus) 
    {
        this.enrollmentBonus = enrollmentBonus;
    }

    public BigDecimal getEnrollmentBonus() 
    {
        return enrollmentBonus;
    }
    public void setAttendanceRateBonus(BigDecimal attendanceRateBonus) 
    {
        this.attendanceRateBonus = attendanceRateBonus;
    }

    public BigDecimal getAttendanceRateBonus() 
    {
        return attendanceRateBonus;
    }
    public void setNewStudentBonus(BigDecimal newStudentBonus) 
    {
        this.newStudentBonus = newStudentBonus;
    }

    public BigDecimal getNewStudentBonus() 
    {
        return newStudentBonus;
    }
    public void setWithdrawalPenalty(BigDecimal withdrawalPenalty) 
    {
        this.withdrawalPenalty = withdrawalPenalty;
    }

    public BigDecimal getWithdrawalPenalty() 
    {
        return withdrawalPenalty;
    }
    public void setSocialInsurance(BigDecimal socialInsurance) 
    {
        this.socialInsurance = socialInsurance;
    }

    public BigDecimal getSocialInsurance() 
    {
        return socialInsurance;
    }
    public void setPerformanceScore(BigDecimal performanceScore) 
    {
        this.performanceScore = performanceScore;
    }

    public BigDecimal getPerformanceScore() 
    {
        return performanceScore;
    }
    public void setOtherBonus(BigDecimal otherBonus) 
    {
        this.otherBonus = otherBonus;
    }

    public BigDecimal getOtherBonus() 
    {
        return otherBonus;
    }
    public void setOtherDeduction(BigDecimal otherDeduction) 
    {
        this.otherDeduction = otherDeduction;
    }

    public BigDecimal getOtherDeduction() 
    {
        return otherDeduction;
    }
    public void setGrossSalary(BigDecimal grossSalary) 
    {
        this.grossSalary = grossSalary;
    }

    public BigDecimal getGrossSalary() 
    {
        return grossSalary;
    }
    public void setNetSalary(BigDecimal netSalary) 
    {
        this.netSalary = netSalary;
    }

    public BigDecimal getNetSalary() 
    {
        return netSalary;
    }
    public void setSalaryStatus(String salaryStatus) 
    {
        this.salaryStatus = salaryStatus;
    }

    public String getSalaryStatus() 
    {
        return salaryStatus;
    }
    public void setConfirmedBy(Long confirmedBy) 
    {
        this.confirmedBy = confirmedBy;
    }

    public Long getConfirmedBy() 
    {
        return confirmedBy;
    }
    public void setConfirmedTime(Date confirmedTime) 
    {
        this.confirmedTime = confirmedTime;
    }

    public Date getConfirmedTime() 
    {
        return confirmedTime;
    }
    public void setPaidTime(Date paidTime) 
    {
        this.paidTime = paidTime;
    }

    public Date getPaidTime() 
    {
        return paidTime;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("salaryId", getSalaryId())
            .append("teacherId", getTeacherId())
            .append("salaryYear", getSalaryYear())
            .append("salaryMonth", getSalaryMonth())
            .append("baseSalary", getBaseSalary())
            .append("attendanceDays", getAttendanceDays())
            .append("totalWorkDays", getTotalWorkDays())
            .append("attendanceBonus", getAttendanceBonus())
            .append("courseBonus", getCourseBonus())
            .append("enrollmentBonus", getEnrollmentBonus())
            .append("attendanceRateBonus", getAttendanceRateBonus())
            .append("newStudentBonus", getNewStudentBonus())
            .append("withdrawalPenalty", getWithdrawalPenalty())
            .append("socialInsurance", getSocialInsurance())
            .append("performanceScore", getPerformanceScore())
            .append("otherBonus", getOtherBonus())
            .append("otherDeduction", getOtherDeduction())
            .append("grossSalary", getGrossSalary())
            .append("netSalary", getNetSalary())
            .append("salaryStatus", getSalaryStatus())
            .append("confirmedBy", getConfirmedBy())
            .append("confirmedTime", getConfirmedTime())
            .append("paidTime", getPaidTime())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

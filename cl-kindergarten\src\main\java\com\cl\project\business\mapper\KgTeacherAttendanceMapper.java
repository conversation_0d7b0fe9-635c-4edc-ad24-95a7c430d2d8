package com.cl.project.business.mapper;

import java.util.List;
import java.util.Date;
import com.cl.project.business.domain.KgTeacherAttendance;
import com.cl.project.business.domain.dto.TeacherAttendanceOverviewDto;
import org.apache.ibatis.annotations.Param;

/**
 * 教师考勤记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface KgTeacherAttendanceMapper 
{
    /**
     * 查询教师考勤记录
     * 
     * @param attendanceId 教师考勤记录ID
     * @return 教师考勤记录
     */
    public KgTeacherAttendance selectKgTeacherAttendanceById(Long attendanceId);

    /**
     * 查询教师考勤记录列表
     * 
     * @param kgTeacherAttendance 教师考勤记录
     * @return 教师考勤记录集合
     */
    public List<KgTeacherAttendance> selectKgTeacherAttendanceList(KgTeacherAttendance kgTeacherAttendance);

    /**
     * 新增教师考勤记录
     * 
     * @param kgTeacherAttendance 教师考勤记录
     * @return 结果
     */
    public int insertKgTeacherAttendance(KgTeacherAttendance kgTeacherAttendance);

    /**
     * 修改教师考勤记录
     * 
     * @param kgTeacherAttendance 教师考勤记录
     * @return 结果
     */
    public int updateKgTeacherAttendance(KgTeacherAttendance kgTeacherAttendance);

    /**
     * 删除教师考勤记录
     * 
     * @param attendanceId 教师考勤记录ID
     * @return 结果
     */
    public int deleteKgTeacherAttendanceById(Long attendanceId);

    /**
     * 批量确认教师考勤记录
     * @param attendanceIds 需要确认的考勤ID列表
     * @param confirmedBy 确认人ID
     * @return 影响行数
     */
    int batchConfirmAttendance(@Param("attendanceIds") List<Long> attendanceIds, @Param("confirmedBy") Long confirmedBy);

    /**
     * 批量删除教师考勤记录
     * 
     * @param attendanceIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteKgTeacherAttendanceByIds(Long[] attendanceIds);

    /**
     * 查询教师考勤概览列表
     * 展示所有教师及其指定日期的考勤状态
     * 
     * @param attendanceDate 考勤日期
     * @return 教师考勤概览集合
     */
    public List<TeacherAttendanceOverviewDto> selectTeacherAttendanceOverview(@Param("attendanceDate") Date attendanceDate, @Param("teacherName") String teacherName, @Param("attendanceStatus") String attendanceStatus, @Param("dataSource") String dataSource);

    /**
     * 查询指定教师某天的所有手动签到记录
     * @param teacherId 教师ID
     * @param dateFrom 当天起始时间
     * @param dateTo 当天结束时间
     * @return 手动签到记录列表
     */
    List<KgTeacherAttendance> selectManualByTeacherAndDate(@Param("teacherId") Long teacherId, @Param("dateFrom") Date dateFrom, @Param("dateTo") Date dateTo);
}

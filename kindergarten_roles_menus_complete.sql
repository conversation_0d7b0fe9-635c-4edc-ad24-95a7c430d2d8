-- =====================================================
-- 幼儿园管理系统角色菜单权限完整版
-- 修复了所有字段类型和范围问题
-- =====================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 使用说明：
-- 1. 请先查询您的系统中现有的 com_id：
--    SELECT DISTINCT com_id FROM sys_user WHERE com_id IS NOT NULL LIMIT 1;
-- 2. 将下面的 'YOUR_COM_ID' 替换为实际的公司ID
-- 3. 如果是全局角色，可以使用 '000000' 或 'GLOBAL'
-- =====================================================

-- 设置公司ID变量（请修改为实际值）
SET @com_id = 'YOUR_COM_ID';  -- 请替换为实际的公司ID

-- =====================================================
-- 1. 角色数据
-- =====================================================

INSERT INTO `sys_role` VALUES 
(10, '园长', 'kg_director', 1, '1', '0', '0', 'admin', NOW(), 'admin', NOW(), '幼儿园园长，拥有全部管理权限', @com_id),
(11, '教务主任', 'kg_academic_director', 2, '1', '0', '0', 'admin', NOW(), 'admin', NOW(), '教务主任，负责教学管理', @com_id),
(12, '班主任', 'kg_head_teacher', 3, '1', '0', '0', 'admin', NOW(), 'admin', NOW(), '班主任，管理本班级学生', @com_id),
(13, '副班主任', 'kg_assistant_teacher', 4, '1', '0', '0', 'admin', NOW(), 'admin', NOW(), '副班主任，协助班级管理', @com_id),
(14, '托管教师', 'kg_course_teacher', 5, '1', '0', '0', 'admin', NOW(), 'admin', NOW(), '托管课程教师', @com_id),
(15, '财务', 'kg_finance', 6, '1', '0', '0', 'admin', NOW(), 'admin', NOW(), '财务人员', @com_id),
(16, '保健医', 'kg_health_teacher', 7, '1', '0', '0', 'admin', NOW(), 'admin', NOW(), '保健医生', @com_id);

-- =====================================================
-- 2. 菜单数据
-- =====================================================

-- 主菜单
INSERT INTO `sys_menu` VALUES 
(3000, '幼儿园管理', 0, 1, 'kindergarten', NULL, 1, 'M', '0', '0', '', 'kindergarten', 'admin', NOW(), 'admin', NOW(), '幼儿园管理系统', @com_id);

-- 考勤管理模块
INSERT INTO `sys_menu` VALUES 
(3100, '考勤管理', 3000, 1, 'attendance', NULL, 1, 'M', '0', '0', '', 'attendance', 'admin', NOW(), 'admin', NOW(), '考勤管理', @com_id),
(3101, '学生考勤', 3100, 1, 'student-attendance', 'kg/attendance/student', 1, 'C', '0', '0', 'kg:attendance:student:list', 'student', 'admin', NOW(), 'admin', NOW(), '学生考勤', @com_id),
(3102, '教师考勤', 3100, 2, 'teacher-attendance', 'kg/attendance/teacher', 1, 'C', '0', '0', 'kg:attendance:teacher:list', 'teacher', 'admin', NOW(), 'admin', NOW(), '教师考勤', @com_id);

-- 学生考勤功能按钮
INSERT INTO `sys_menu` VALUES 
(3111, '学生签到', 3101, 1, '', '', 1, 'F', '0', '0', 'kg:attendance:student:checkin', '#', 'admin', NOW(), 'admin', NOW(), '学生签到', @com_id),
(3112, '学生签退', 3101, 2, '', '', 1, 'F', '0', '0', 'kg:attendance:student:checkout', '#', 'admin', NOW(), 'admin', NOW(), '学生签退', @com_id),
(3113, '考勤确认', 3101, 3, '', '', 1, 'F', '0', '0', 'kg:attendance:student:confirm', '#', 'admin', NOW(), 'admin', NOW(), '考勤确认', @com_id),
(3114, '缺勤登记', 3101, 4, '', '', 1, 'F', '0', '0', 'kg:attendance:student:absence', '#', 'admin', NOW(), 'admin', NOW(), '缺勤登记', @com_id),
(3115, '考勤查询', 3101, 5, '', '', 1, 'F', '0', '0', 'kg:attendance:student:query', '#', 'admin', NOW(), 'admin', NOW(), '考勤查询', @com_id);

-- 教师考勤功能按钮
INSERT INTO `sys_menu` VALUES 
(3121, '教师签到', 3102, 1, '', '', 1, 'F', '0', '0', 'kg:attendance:teacher:checkin', '#', 'admin', NOW(), 'admin', NOW(), '教师签到', @com_id),
(3122, '教师签退', 3102, 2, '', '', 1, 'F', '0', '0', 'kg:attendance:teacher:checkout', '#', 'admin', NOW(), 'admin', NOW(), '教师签退', @com_id),
(3123, '教师考勤查询', 3102, 3, '', '', 1, 'F', '0', '0', 'kg:attendance:teacher:query', '#', 'admin', NOW(), 'admin', NOW(), '教师考勤查询', @com_id);

-- 托管管理模块
INSERT INTO `sys_menu` VALUES 
(3200, '托管管理', 3000, 2, 'course', NULL, 1, 'M', '0', '0', '', 'course', 'admin', NOW(), 'admin', NOW(), '托管管理', @com_id),
(3201, '课程管理', 3200, 1, 'course-manage', 'kg/course/manage', 1, 'C', '0', '0', 'kg:course:manage:list', 'course', 'admin', NOW(), 'admin', NOW(), '课程管理', @com_id),
(3202, '托管考勤', 3200, 2, 'course-attendance', 'kg/course/attendance', 1, 'C', '0', '0', 'kg:course:attendance:list', 'attendance', 'admin', NOW(), 'admin', NOW(), '托管考勤', @com_id);

-- 托管功能按钮
INSERT INTO `sys_menu` VALUES 
(3211, '托管签到', 3202, 1, '', '', 1, 'F', '0', '0', 'kg:course:attendance:checkin', '#', 'admin', NOW(), 'admin', NOW(), '托管签到', @com_id),
(3212, '托管确认', 3202, 2, '', '', 1, 'F', '0', '0', 'kg:course:attendance:confirm', '#', 'admin', NOW(), 'admin', NOW(), '托管确认', @com_id),
(3213, '托管查询', 3202, 3, '', '', 1, 'F', '0', '0', 'kg:course:attendance:query', '#', 'admin', NOW(), 'admin', NOW(), '托管查询', @com_id),
(3221, '课程新增', 3201, 1, '', '', 1, 'F', '0', '0', 'kg:course:manage:add', '#', 'admin', NOW(), 'admin', NOW(), '课程新增', @com_id),
(3222, '课程修改', 3201, 2, '', '', 1, 'F', '0', '0', 'kg:course:manage:edit', '#', 'admin', NOW(), 'admin', NOW(), '课程修改', @com_id),
(3223, '课程删除', 3201, 3, '', '', 1, 'F', '0', '0', 'kg:course:manage:remove', '#', 'admin', NOW(), 'admin', NOW(), '课程删除', @com_id);

-- 费用管理模块
INSERT INTO `sys_menu` VALUES 
(3300, '费用管理', 3000, 3, 'finance', NULL, 1, 'M', '0', '0', '', 'finance', 'admin', NOW(), 'admin', NOW(), '费用管理', @com_id),
(3301, '园费管理', 3300, 1, 'tuition', 'kg/finance/tuition', 1, 'C', '0', '0', 'kg:finance:tuition:list', 'tuition', 'admin', NOW(), 'admin', NOW(), '园费管理', @com_id),
(3302, '托管费管理', 3300, 2, 'course-fee', 'kg/finance/course-fee', 1, 'C', '0', '0', 'kg:finance:course:list', 'course-fee', 'admin', NOW(), 'admin', NOW(), '托管费管理', @com_id);

-- 费用功能按钮
INSERT INTO `sys_menu` VALUES 
(3311, '查看园费', 3301, 1, '', '', 1, 'F', '0', '0', 'kg:finance:tuition:view', '#', 'admin', NOW(), 'admin', NOW(), '查看园费', @com_id),
(3312, '发送账单', 3301, 2, '', '', 1, 'F', '0', '0', 'kg:finance:tuition:send', '#', 'admin', NOW(), 'admin', NOW(), '发送账单', @com_id),
(3313, '园费计算', 3301, 3, '', '', 1, 'F', '0', '0', 'kg:finance:tuition:calculate', '#', 'admin', NOW(), 'admin', NOW(), '园费计算', @com_id),
(3321, '查看托管费', 3302, 1, '', '', 1, 'F', '0', '0', 'kg:finance:course:view', '#', 'admin', NOW(), 'admin', NOW(), '查看托管费', @com_id),
(3322, '发送托管账单', 3302, 2, '', '', 1, 'F', '0', '0', 'kg:finance:course:send', '#', 'admin', NOW(), 'admin', NOW(), '发送托管账单', @com_id),
(3323, '托管费计算', 3302, 3, '', '', 1, 'F', '0', '0', 'kg:finance:course:calculate', '#', 'admin', NOW(), 'admin', NOW(), '托管费计算', @com_id);

-- 学生管理模块
INSERT INTO `sys_menu` VALUES 
(3400, '学生管理', 3000, 4, 'student', NULL, 1, 'M', '0', '0', '', 'student', 'admin', NOW(), 'admin', NOW(), '学生管理', @com_id),
(3401, '学生信息', 3400, 1, 'student-info', 'kg/student/info', 1, 'C', '0', '0', 'kg:student:info:list', 'student-info', 'admin', NOW(), 'admin', NOW(), '学生信息', @com_id),
(3402, '班级管理', 3400, 2, 'class-manage', 'kg/student/class', 1, 'C', '0', '0', 'kg:student:class:list', 'class', 'admin', NOW(), 'admin', NOW(), '班级管理', @com_id);

-- 学生管理功能按钮
INSERT INTO `sys_menu` VALUES 
(3411, '学生新增', 3401, 1, '', '', 1, 'F', '0', '0', 'kg:student:info:add', '#', 'admin', NOW(), 'admin', NOW(), '学生新增', @com_id),
(3412, '学生修改', 3401, 2, '', '', 1, 'F', '0', '0', 'kg:student:info:edit', '#', 'admin', NOW(), 'admin', NOW(), '学生修改', @com_id),
(3413, '学生删除', 3401, 3, '', '', 1, 'F', '0', '0', 'kg:student:info:remove', '#', 'admin', NOW(), 'admin', NOW(), '学生删除', @com_id),
(3421, '班级新增', 3402, 1, '', '', 1, 'F', '0', '0', 'kg:student:class:add', '#', 'admin', NOW(), 'admin', NOW(), '班级新增', @com_id),
(3422, '班级修改', 3402, 2, '', '', 1, 'F', '0', '0', 'kg:student:class:edit', '#', 'admin', NOW(), 'admin', NOW(), '班级修改', @com_id),
(3423, '班级删除', 3402, 3, '', '', 1, 'F', '0', '0', 'kg:student:class:remove', '#', 'admin', NOW(), 'admin', NOW(), '班级删除', @com_id);

-- 工资管理模块
INSERT INTO `sys_menu` VALUES 
(3500, '工资管理', 3000, 5, 'salary', NULL, 1, 'M', '0', '0', '', 'salary', 'admin', NOW(), 'admin', NOW(), '工资管理', @com_id),
(3501, '工资查询', 3500, 1, 'salary-query', 'kg/salary/query', 1, 'C', '0', '0', 'kg:salary:query:list', 'salary-query', 'admin', NOW(), 'admin', NOW(), '工资查询', @com_id),
(3502, '工资计算', 3500, 2, 'salary-calculate', 'kg/salary/calculate', 1, 'C', '0', '0', 'kg:salary:calculate:list', 'salary-calculate', 'admin', NOW(), 'admin', NOW(), '工资计算', @com_id);

-- 工资管理功能按钮
INSERT INTO `sys_menu` VALUES 
(3511, '工资确认', 3502, 1, '', '', 1, 'F', '0', '0', 'kg:salary:calculate:confirm', '#', 'admin', NOW(), 'admin', NOW(), '工资确认', @com_id),
(3512, '工资发放', 3502, 2, '', '', 1, 'F', '0', '0', 'kg:salary:calculate:pay', '#', 'admin', NOW(), 'admin', NOW(), '工资发放', @com_id);

-- 库存管理模块
INSERT INTO `sys_menu` VALUES 
(3600, '库存管理', 3000, 6, 'inventory', NULL, 1, 'M', '0', '0', '', 'inventory', 'admin', NOW(), 'admin', NOW(), '库存管理', @com_id),
(3601, '物品管理', 3600, 1, 'item-manage', 'kg/inventory/item', 1, 'C', '0', '0', 'kg:inventory:item:list', 'item', 'admin', NOW(), 'admin', NOW(), '物品管理', @com_id),
(3602, '出入库', 3600, 2, 'stock-record', 'kg/inventory/record', 1, 'C', '0', '0', 'kg:inventory:record:list', 'record', 'admin', NOW(), 'admin', NOW(), '出入库管理', @com_id);

-- 库存管理功能按钮
INSERT INTO `sys_menu` VALUES 
(3611, '物品新增', 3601, 1, '', '', 1, 'F', '0', '0', 'kg:inventory:item:add', '#', 'admin', NOW(), 'admin', NOW(), '物品新增', @com_id),
(3612, '物品修改', 3601, 2, '', '', 1, 'F', '0', '0', 'kg:inventory:item:edit', '#', 'admin', NOW(), 'admin', NOW(), '物品修改', @com_id),
(3613, '物品删除', 3601, 3, '', '', 1, 'F', '0', '0', 'kg:inventory:item:remove', '#', 'admin', NOW(), 'admin', NOW(), '物品删除', @com_id),
(3621, '入库操作', 3602, 1, '', '', 1, 'F', '0', '0', 'kg:inventory:record:in', '#', 'admin', NOW(), 'admin', NOW(), '入库操作', @com_id),
(3622, '出库操作', 3602, 2, '', '', 1, 'F', '0', '0', 'kg:inventory:record:out', '#', 'admin', NOW(), 'admin', NOW(), '出库操作', @com_id),
(3623, '库存调整', 3602, 3, '', '', 1, 'F', '0', '0', 'kg:inventory:record:adjust', '#', 'admin', NOW(), 'admin', NOW(), '库存调整', @com_id);

-- 统计报表模块
INSERT INTO `sys_menu` VALUES 
(3700, '统计报表', 3000, 7, 'report', NULL, 1, 'M', '0', '0', '', 'report', 'admin', NOW(), 'admin', NOW(), '统计报表', @com_id),
(3701, '考勤统计', 3700, 1, 'attendance-report', 'kg/report/attendance', 1, 'C', '0', '0', 'kg:report:attendance:list', 'attendance-report', 'admin', NOW(), 'admin', NOW(), '考勤统计', @com_id),
(3702, '财务报表', 3700, 2, 'finance-report', 'kg/report/finance', 1, 'C', '0', '0', 'kg:report:finance:list', 'finance-report', 'admin', NOW(), 'admin', NOW(), '财务报表', @com_id);

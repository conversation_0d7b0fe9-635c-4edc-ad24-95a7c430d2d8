import request from '@/utils/request'

// 查询物品分类列表
export function listCategory(query) {
  return request({
    url: '/business/category/list',
    method: 'get',
    params: query
  })
}

// 查询物品分类详细
export function getCategory(categoryId) {
  return request({
    url: '/business/category/' + categoryId,
    method: 'get'
  })
}

// 新增物品分类
export function addCategory(data) {
  return request({
    url: '/business/category',
    method: 'post',
    data: data
  })
}

// 修改物品分类
export function updateCategory(data) {
  return request({
    url: '/business/category',
    method: 'put',
    data: data
  })
}

// 删除物品分类
export function delCategory(categoryIds) {
  return request({
    url: '/business/category/' + categoryIds,
    method: 'delete'
  })
}

// 导出物品分类
export function exportCategory(query) {
  return request({
    url: '/business/category/export',
    method: 'get',
    params: query
  })
}

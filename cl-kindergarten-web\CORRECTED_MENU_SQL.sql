-- 修正后的幼儿园管理系统菜单SQL
-- 所有component路径都指向正确的Vue文件位置

-- 主菜单：幼儿园管理
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '幼儿园管理', `parent_id` = 0, `order_num` = 1, `path` = 'kindergarten', `component` = NULL, `is_frame` = 1, `menu_type` = 'M', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'kindergarten', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '幼儿园管理系统', `super_flag` = 0 WHERE `menu_id` = 3000;

-- 考勤管理模块
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '考勤管理', `parent_id` = 3000, `order_num` = 1, `path` = 'attendance', `component` = NULL, `is_frame` = 1, `menu_type` = 'M', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'attendance', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '考勤管理', `super_flag` = 0 WHERE `menu_id` = 3100;

-- 学生考勤 - 修正component路径
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '学生考勤', `parent_id` = 3100, `order_num` = 1, `path` = 'student-attendance', `component` = 'kg/attendance/student/index', `is_frame` = 1, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'kg:attendance:student:list', `icon` = 'student', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '学生考勤', `super_flag` = 0 WHERE `menu_id` = 3101;

-- 教师考勤 - 修正component路径
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '教师考勤', `parent_id` = 3100, `order_num` = 2, `path` = 'teacher-attendance', `component` = 'kg/attendance/teacher/index', `is_frame` = 1, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'kg:attendance:teacher:list', `icon` = 'teacher', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '教师考勤', `super_flag` = 0 WHERE `menu_id` = 3102;

-- 学生考勤功能按钮
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '学生签到', `parent_id` = 3101, `order_num` = 1, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:attendance:student:checkin', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '学生签到', `super_flag` = 0 WHERE `menu_id` = 3111;
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '学生签退', `parent_id` = 3101, `order_num` = 2, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:attendance:student:checkout', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '学生签退', `super_flag` = 0 WHERE `menu_id` = 3112;
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '考勤确认', `parent_id` = 3101, `order_num` = 3, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:attendance:student:confirm', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '考勤确认', `super_flag` = 0 WHERE `menu_id` = 3113;
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '缺勤登记', `parent_id` = 3101, `order_num` = 4, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:attendance:student:absence', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '缺勤登记', `super_flag` = 0 WHERE `menu_id` = 3114;
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '考勤查询', `parent_id` = 3101, `order_num` = 5, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:attendance:student:query', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '考勤查询', `super_flag` = 0 WHERE `menu_id` = 3115;

-- 教师考勤功能按钮
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '教师签到', `parent_id` = 3102, `order_num` = 1, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:attendance:teacher:checkin', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '教师签到', `super_flag` = 0 WHERE `menu_id` = 3121;
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '教师签退', `parent_id` = 3102, `order_num` = 2, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:attendance:teacher:checkout', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '教师签退', `super_flag` = 0 WHERE `menu_id` = 3122;
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '教师考勤查询', `parent_id` = 3102, `order_num` = 3, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:attendance:teacher:query', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '教师考勤查询', `super_flag` = 0 WHERE `menu_id` = 3123;

-- 托管管理模块
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '托管管理', `parent_id` = 3000, `order_num` = 2, `path` = 'course', `component` = NULL, `is_frame` = 1, `menu_type` = 'M', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'course', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '托管管理', `super_flag` = 0 WHERE `menu_id` = 3200;

-- 课程管理 - 修正component路径
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '课程管理', `parent_id` = 3200, `order_num` = 1, `path` = 'course-manage', `component` = 'kg/course/manage/index', `is_frame` = 1, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'kg:course:manage:list', `icon` = 'course', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '课程管理', `super_flag` = 0 WHERE `menu_id` = 3201;

-- 托管考勤 - 修正component路径
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '托管考勤', `parent_id` = 3200, `order_num` = 2, `path` = 'course-attendance', `component` = 'kg/course/attendance/index', `is_frame` = 1, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'kg:course:attendance:list', `icon` = 'attendance', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '托管考勤', `super_flag` = 0 WHERE `menu_id` = 3202;

-- 托管考勤功能按钮
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '托管签到', `parent_id` = 3202, `order_num` = 1, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:course:attendance:checkin', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '托管签到', `super_flag` = 0 WHERE `menu_id` = 3211;
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '托管确认', `parent_id` = 3202, `order_num` = 2, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:course:attendance:confirm', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '托管确认', `super_flag` = 0 WHERE `menu_id` = 3212;
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '托管查询', `parent_id` = 3202, `order_num` = 3, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:course:attendance:query', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '托管查询', `super_flag` = 0 WHERE `menu_id` = 3213;

-- 课程管理功能按钮
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '课程新增', `parent_id` = 3201, `order_num` = 1, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:course:manage:add', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '课程新增', `super_flag` = 0 WHERE `menu_id` = 3221;
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '课程修改', `parent_id` = 3201, `order_num` = 2, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:course:manage:edit', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '课程修改', `super_flag` = 0 WHERE `menu_id` = 3222;
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '课程删除', `parent_id` = 3201, `order_num` = 3, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:course:manage:remove', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '课程删除', `super_flag` = 0 WHERE `menu_id` = 3223;

-- 费用管理模块
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '费用管理', `parent_id` = 3000, `order_num` = 3, `path` = 'finance', `component` = NULL, `is_frame` = 1, `menu_type` = 'M', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'finance', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '费用管理', `super_flag` = 0 WHERE `menu_id` = 3300;

-- 园费管理 - 修正component路径
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '园费管理', `parent_id` = 3300, `order_num` = 1, `path` = 'tuition', `component` = 'kg/finance/tuition/index', `is_frame` = 1, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'kg:finance:tuition:list', `icon` = 'tuition', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '园费管理', `super_flag` = 0 WHERE `menu_id` = 3301;

-- 托管费管理 - 修正component路径
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '托管费管理', `parent_id` = 3300, `order_num` = 2, `path` = 'course-fee', `component` = 'kg/finance/course-fee/index', `is_frame` = 1, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'kg:finance:course:list', `icon` = 'course-fee', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '托管费管理', `super_flag` = 0 WHERE `menu_id` = 3302;

-- 园费管理功能按钮
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '查看园费', `parent_id` = 3301, `order_num` = 1, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:finance:tuition:view', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '查看园费', `super_flag` = 0 WHERE `menu_id` = 3311;
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '发送账单', `parent_id` = 3301, `order_num` = 2, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:finance:tuition:send', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '发送账单', `super_flag` = 0 WHERE `menu_id` = 3312;
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '园费计算', `parent_id` = 3301, `order_num` = 3, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:finance:tuition:calculate', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '园费计算', `super_flag` = 0 WHERE `menu_id` = 3313;

-- 托管费管理功能按钮
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '查看托管费', `parent_id` = 3302, `order_num` = 1, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:finance:course:view', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '查看托管费', `super_flag` = 0 WHERE `menu_id` = 3321;
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '发送托管账单', `parent_id` = 3302, `order_num` = 2, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:finance:course:send', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '发送托管账单', `super_flag` = 0 WHERE `menu_id` = 3322;
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '托管费计算', `parent_id` = 3302, `order_num` = 3, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:finance:course:calculate', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '托管费计算', `super_flag` = 0 WHERE `menu_id` = 3323;

-- 学生管理模块
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '学生管理', `parent_id` = 3000, `order_num` = 4, `path` = 'student', `component` = NULL, `is_frame` = 1, `menu_type` = 'M', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'student', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '学生管理', `super_flag` = 0 WHERE `menu_id` = 3400;

-- 学生信息 - 修正component路径
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '学生信息', `parent_id` = 3400, `order_num` = 1, `path` = 'student-info', `component` = 'kg/student/info/index', `is_frame` = 1, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'kg:student:info:list', `icon` = 'student-info', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '学生信息', `super_flag` = 0 WHERE `menu_id` = 3401;

-- 班级管理 - 修正component路径
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '班级管理', `parent_id` = 3400, `order_num` = 2, `path` = 'class-manage', `component` = 'kg/student/class/index', `is_frame` = 1, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'kg:student:class:list', `icon` = 'class', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '班级管理', `super_flag` = 0 WHERE `menu_id` = 3402;

-- 学生信息功能按钮
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '学生新增', `parent_id` = 3401, `order_num` = 1, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:student:info:add', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '学生新增', `super_flag` = 0 WHERE `menu_id` = 3411;
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '学生修改', `parent_id` = 3401, `order_num` = 2, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:student:info:edit', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '学生修改', `super_flag` = 0 WHERE `menu_id` = 3412;
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '学生删除', `parent_id` = 3401, `order_num` = 3, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:student:info:remove', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '学生删除', `super_flag` = 0 WHERE `menu_id` = 3413;

-- 班级管理功能按钮
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '班级新增', `parent_id` = 3402, `order_num` = 1, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:student:class:add', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '班级新增', `super_flag` = 0 WHERE `menu_id` = 3421;
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '班级修改', `parent_id` = 3402, `order_num` = 2, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:student:class:edit', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '班级修改', `super_flag` = 0 WHERE `menu_id` = 3422;
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '班级删除', `parent_id` = 3402, `order_num` = 3, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:student:class:remove', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '班级删除', `super_flag` = 0 WHERE `menu_id` = 3423;

-- 工资管理模块
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '工资管理', `parent_id` = 3000, `order_num` = 5, `path` = 'salary', `component` = NULL, `is_frame` = 1, `menu_type` = 'M', `visible` = '0', `status` = '0', `perms` = '', `icon` = 'salary', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '工资管理', `super_flag` = 0 WHERE `menu_id` = 3500;

-- 工资查询 - 修正component路径
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '工资查询', `parent_id` = 3500, `order_num` = 1, `path` = 'salary-query', `component` = 'kg/salary/query/index', `is_frame` = 1, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'kg:salary:query:list', `icon` = 'salary-query', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '工资查询', `super_flag` = 0 WHERE `menu_id` = 3501;

-- 工资计算 - 修正component路径
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '工资计算', `parent_id` = 3500, `order_num` = 2, `path` = 'salary-calculate', `component` = 'kg/salary/calculate/index', `is_frame` = 1, `menu_type` = 'C', `visible` = '0', `status` = '0', `perms` = 'kg:salary:calculate:list', `icon` = 'salary-calculate', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '工资计算', `super_flag` = 0 WHERE `menu_id` = 3502;

-- 工资计算功能按钮
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '工资确认', `parent_id` = 3502, `order_num` = 1, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:salary:calculate:confirm', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '工资确认', `super_flag` = 0 WHERE `menu_id` = 3511;
UPDATE `kindergarten`.`sys_menu` SET `menu_name` = '工资发放', `parent_id` = 3502, `order_num` = 2, `path` = '', `component` = '', `is_frame` = 1, `menu_type` = 'F', `visible` = '0', `status` = '0', `perms` = 'kg:salary:calculate:pay', `icon` = '#', `create_by` = 'admin', `create_time` = '2025-07-28 15:36:53', `update_by` = 'admin', `update_time` = '2025-07-28 15:36:53', `remark` = '工资发放', `super_flag` = 0 WHERE `menu_id` = 3512;

import request from '@/utils/request'

// 计算学生月度园费
export function calculateMonthlyTuition(studentId, year, month) {
  return request({
    url: '/business/tuition-calculation/calculate-monthly',
    method: 'post',
    params: { studentId, year, month }
  })
}

// 批量计算班级园费
export function calculateClassTuitionBatch(classId, year, month) {
  return request({
    url: '/business/tuition-calculation/calculate-class-batch',
    method: 'post',
    params: { classId, year, month }
  })
}

// 批量计算全园园费
export function calculateAllTuitionBatch(year, month) {
  return request({
    url: '/business/tuition-calculation/calculate-all-batch',
    method: 'post',
    params: { year, month }
  })
}

// 预览园费计算结果
export function previewTuitionCalculation(studentId, year, month) {
  return request({
    url: '/business/tuition-calculation/preview',
    method: 'get',
    params: { studentId, year, month }
  })
}

// 获取园费计算规则
export function getTuitionCalculationRules(classId) {
  return request({
    url: '/business/tuition-calculation/rules',
    method: 'get',
    params: { classId }
  })
}

// 生成园费账单
export function generateTuitionBills(calculations) {
  return request({
    url: '/business/tuition-calculation/generate-bills',
    method: 'post',
    data: calculations
  })
}

// 重新计算园费
export function recalculateTuition(billId, reason) {
  return request({
    url: '/business/tuition-calculation/recalculate',
    method: 'post',
    params: { billId, reason }
  })
}

// 获取费用明细
export function getFeeDetails(studentId, year, month) {
  return request({
    url: '/business/tuition-calculation/fee-details',
    method: 'get',
    params: { studentId, year, month }
  })
}

// 费用调整
export function adjustFee(billId, feeType, adjustAmount, reason) {
  return request({
    url: '/business/tuition-calculation/adjust-fee',
    method: 'post',
    params: { billId, feeType, adjustAmount, reason }
  })
}

// 获取费用统计
export function getTuitionStatistics(year, month, classId) {
  return request({
    url: '/business/tuition-calculation/statistics',
    method: 'get',
    params: { year, month, classId }
  })
}

package com.cl.project.business.mapper;

import java.util.List;
import com.cl.project.business.domain.KgTeacher;

/**
 * 教师信息Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface KgTeacherMapper 
{
    /**
     * 查询教师信息
     * 
     * @param teacherId 教师信息ID
     * @return 教师信息
     */
    public KgTeacher selectKgTeacherById(Long teacherId);

    /**
     * 根据钉钉用户ID查询教师信息
     * 
     * @param dingtalkUserId 钉钉用户ID
     * @return 教师信息
     */
    public KgTeacher selectKgTeacherByDingtalkUserId(String dingtalkUserId);

    /**
     * 查询教师信息列表
     * 
     * @param kgTeacher 教师信息
     * @return 教师信息集合
     */
    public List<KgTeacher> selectKgTeacherList(KgTeacher kgTeacher);

    /**
     * 新增教师信息
     * 
     * @param kgTeacher 教师信息
     * @return 结果
     */
    public int insertKgTeacher(KgTeacher kgTeacher);

    /**
     * 修改教师信息
     * 
     * @param kgTeacher 教师信息
     * @return 结果
     */
    public int updateKgTeacher(KgTeacher kgTeacher);

    /**
     * 删除教师信息
     * 
     * @param teacherId 教师信息ID
     * @return 结果
     */
    public int deleteKgTeacherById(Long teacherId);

    /**
     * 批量删除教师信息
     * 
     * @param teacherIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteKgTeacherByIds(Long[] teacherIds);
}

package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgItemCategoryMapper;
import com.cl.project.business.domain.KgItemCategory;
import com.cl.project.business.service.IKgItemCategoryService;

/**
 * 物品类别Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgItemCategoryServiceImpl implements IKgItemCategoryService 
{
    @Autowired
    private KgItemCategoryMapper kgItemCategoryMapper;

    /**
     * 查询物品类别
     * 
     * @param categoryId 物品类别ID
     * @return 物品类别
     */
    @Override
    public KgItemCategory selectKgItemCategoryById(Long categoryId)
    {
        return kgItemCategoryMapper.selectKgItemCategoryById(categoryId);
    }

    /**
     * 查询物品类别列表
     * 
     * @param kgItemCategory 物品类别
     * @return 物品类别
     */
    @Override
    public List<KgItemCategory> selectKgItemCategoryList(KgItemCategory kgItemCategory)
    {
        return kgItemCategoryMapper.selectKgItemCategoryList(kgItemCategory);
    }

    /**
     * 新增物品类别
     * 
     * @param kgItemCategory 物品类别
     * @return 结果
     */
    @Override
    public int insertKgItemCategory(KgItemCategory kgItemCategory)
    {
        kgItemCategory.setCreateTime(DateUtils.getNowDate());
        return kgItemCategoryMapper.insertKgItemCategory(kgItemCategory);
    }

    /**
     * 修改物品类别
     * 
     * @param kgItemCategory 物品类别
     * @return 结果
     */
    @Override
    public int updateKgItemCategory(KgItemCategory kgItemCategory)
    {
        kgItemCategory.setUpdateTime(DateUtils.getNowDate());
        return kgItemCategoryMapper.updateKgItemCategory(kgItemCategory);
    }

    /**
     * 批量删除物品类别
     * 
     * @param categoryIds 需要删除的物品类别ID
     * @return 结果
     */
    @Override
    public int deleteKgItemCategoryByIds(Long[] categoryIds)
    {
        return kgItemCategoryMapper.deleteKgItemCategoryByIds(categoryIds);
    }

    /**
     * 删除物品类别信息
     * 
     * @param categoryId 物品类别ID
     * @return 结果
     */
    @Override
    public int deleteKgItemCategoryById(Long categoryId)
    {
        return kgItemCategoryMapper.deleteKgItemCategoryById(categoryId);
    }
}

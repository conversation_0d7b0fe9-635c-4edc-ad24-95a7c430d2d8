<template>
	<view class="navbar" :style="{ background: backgroundColor }">
		<view class="navbar-left">
			<button v-if="showBack" class="back-btn" @click="handleBack">
				<text class="back-icon">←</text>
			</button>
		</view>
		<view class="navbar-center">
			<text class="navbar-title">{{ title }}</text>
		</view>
		<view class="navbar-right">
			<button v-if="showAdd" class="add-btn" @click="handleAdd">
				<text class="add-icon">+</text>
				<text class="add-text">{{ addText }}</text>
			</button>
			<slot name="right"></slot>
		</view>
	</view>
</template>

<script>
export default {
	name: 'CommonNavbar',
	props: {
		title: {
			type: String,
			default: ''
		},
		showBack: {
			type: Boolean,
			default: true
		},
		showAdd: {
			type: Boolean,
			default: false
		},
		addText: {
			type: String,
			default: '添加'
		},
		backgroundColor: {
			type: String,
			default: '#ffffff'
		}
	},
	methods: {
		handleBack() {
			this.$emit('back')
			uni.navigateBack()
		},
		handleAdd() {
			this.$emit('add')
		}
	}
}
</script>

<style lang="scss" scoped>
.navbar {
	padding: 20rpx 30rpx;
	padding-top: calc(var(--status-bar-height) + 15rpx);
	display: flex;
	align-items: center;
	justify-content: space-between;
	box-shadow: 0 4rpx 20rpx rgba(0,0,0,0.08);
	position: sticky;
	top: 0;
	z-index: 100;
	min-height: 100rpx;
}

.navbar-left,
.navbar-right {
	width: 100rpx;
	display: flex;
	align-items: center;
}

.navbar-left {
	justify-content: flex-start;
}

.navbar-right {
	justify-content: flex-end;
}

.navbar-center {
	flex: 1;
	display: flex;
	justify-content: center;
	align-items: center;
}

.back-btn {
	background: #f8f9fa;
	border: 2rpx solid #e9ecef;
	width: 70rpx;
	height: 70rpx;
	border-radius: 50%;
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;
	
	&:active {
		background: #e9ecef;
		transform: scale(0.95);
	}
}

.back-icon {
	font-size: 28rpx;
	color: #495057;
	font-weight: 600;
}

.navbar-title {
	font-size: 36rpx;
	font-weight: 700;
	color: #212529;
}

.add-btn {
	background: linear-gradient(135deg, #4CAF50, #45a049);
	color: white;
	border: none;
	border-radius: 50rpx;
	padding: 12rpx 20rpx;
	display: flex;
	align-items: center;
	gap: 6rpx;
	box-shadow: 0 4rpx 12rpx rgba(76, 175, 80, 0.3);
	transition: all 0.3s ease;
	min-height: 56rpx;
	
	&:active {
		transform: translateY(2rpx);
		box-shadow: 0 2rpx 8rpx rgba(76, 175, 80, 0.3);
	}
}

.add-icon {
	font-size: 24rpx;
	font-weight: 600;
}

.add-text {
	font-size: 22rpx;
	font-weight: 600;
}
</style>

// 员工端JavaScript功能

class EmployeePage {
    constructor() {
        this.currentUser = Auth.getCurrentUser();
        this.init();
    }

    init() {
        this.loadUserInfo();
        this.loadDashboardData();
        this.bindEvents();
    }

    // 加载用户信息
    loadUserInfo() {
        if (this.currentUser) {
            const userNameEl = document.getElementById('userName');
            const userPositionEl = document.getElementById('userPosition');
            
            if (userNameEl) userNameEl.textContent = this.currentUser.name;
            if (userPositionEl) userPositionEl.textContent = this.currentUser.position;
        }
    }

    // 加载仪表板数据
    async loadDashboardData() {
        try {
            // 更新当前日期
            this.updateCurrentDate();
            
            // 加载考勤状态
            await this.loadAttendanceStatus();
            
            // 加载班级概览
            await this.loadClassOverview();
            
            // 加载待办事项
            await this.loadTodoItems();
            
            // 加载学生列表
            await this.loadStudentList();
            
            // 加载托管课程
            await this.loadCourseSchedule();
            
        } catch (error) {
            console.error('加载数据失败:', error);
            Toast.error('数据加载失败，请刷新重试');
        }
    }

    // 更新当前日期
    updateCurrentDate() {
        const now = new Date();
        const dateStr = Utils.formatDate(now, 'YYYY年MM月DD日');
        const weekdays = ['日', '一', '二', '三', '四', '五', '六'];
        const weekday = '周' + weekdays[now.getDay()];
        
        const currentDateEl = document.getElementById('currentDate');
        if (currentDateEl) {
            currentDateEl.textContent = `${dateStr} ${weekday}`;
        }
    }

    // 加载考勤状态
    async loadAttendanceStatus() {
        // 模拟API调用
        const attendanceData = {
            isCheckedIn: true,
            checkInTime: '08:30',
            checkOutTime: null
        };

        const statusEl = document.getElementById('attendanceStatus');
        if (statusEl) {
            if (attendanceData.isCheckedIn) {
                statusEl.textContent = `✅ 已签到 ${attendanceData.checkInTime}`;
                statusEl.className = 'status-checked';
            } else {
                statusEl.textContent = '❌ 未签到';
                statusEl.className = 'status-unchecked';
            }
        }
    }

    // 加载班级概览
    async loadClassOverview() {
        // 模拟API调用
        const overviewData = {
            totalStudents: 25,
            presentStudents: 23,
            absentStudents: 2,
            leaveStudents: 1
        };

        // 更新概览卡片
        this.updateOverviewCard('totalStudents', overviewData.totalStudents);
        this.updateOverviewCard('presentStudents', overviewData.presentStudents);
        this.updateOverviewCard('absentStudents', overviewData.absentStudents);
        this.updateOverviewCard('leaveStudents', overviewData.leaveStudents);
    }

    // 更新概览卡片
    updateOverviewCard(type, value) {
        const cards = document.querySelectorAll('.overview-card');
        const typeMap = {
            totalStudents: 0,
            presentStudents: 1,
            absentStudents: 2,
            leaveStudents: 3
        };

        const cardIndex = typeMap[type];
        if (cards[cardIndex]) {
            const numberEl = cards[cardIndex].querySelector('.number');
            if (numberEl) {
                numberEl.textContent = `${value}人`;
            }
        }
    }

    // 加载待办事项
    async loadTodoItems() {
        const todoData = [
            {
                id: 1,
                icon: '🔔',
                title: '张小明家长请假申请',
                description: '需要审批确认',
                time: '5分钟前',
                action: '处理'
            },
            {
                id: 2,
                icon: '📋',
                title: '本月考勤统计',
                description: '待确认提交',
                time: '1小时前',
                action: '查看'
            },
            {
                id: 3,
                icon: '📞',
                title: '李小红家长来电',
                description: '需要回访',
                time: '2小时前',
                action: '回访'
            }
        ];

        // 这里可以动态生成待办事项列表
        // 当前使用静态HTML，实际项目中可以动态渲染
    }

    // 加载学生列表
    async loadStudentList() {
        const studentData = [
            {
                id: 1,
                name: '张小明',
                avatar: '👶',
                status: 'present',
                checkInTime: '08:15',
                checkOutTime: null
            },
            {
                id: 2,
                name: '李小红',
                avatar: '👶',
                status: 'present',
                checkInTime: '08:20',
                checkOutTime: null
            },
            {
                id: 3,
                name: '王小华',
                avatar: '👶',
                status: 'absent',
                checkInTime: null,
                checkOutTime: null
            },
            {
                id: 4,
                name: '赵小美',
                avatar: '👶',
                status: 'sick',
                reason: '病假'
            }
        ];

        // 这里可以动态生成学生列表
        // 当前使用静态HTML，实际项目中可以动态渲染
    }

    // 加载托管课程
    async loadCourseSchedule() {
        const courseData = [
            {
                id: 1,
                name: '英语课',
                time: '14:30-15:30',
                enrolledCount: 15
            },
            {
                id: 2,
                name: '美术课',
                time: '15:45-16:45',
                enrolledCount: 12
            }
        ];

        // 这里可以动态生成课程列表
        // 当前使用静态HTML，实际项目中可以动态渲染
    }

    // 绑定事件
    bindEvents() {
        // 待办事项处理
        const todoActions = document.querySelectorAll('.todo-action');
        todoActions.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const todoItem = e.target.closest('.todo-item');
                const title = todoItem.querySelector('h4').textContent;
                this.handleTodoAction(title);
            });
        });

        // 课程考勤按钮
        const courseButtons = document.querySelectorAll('.course-item .btn');
        courseButtons.forEach(btn => {
            btn.addEventListener('click', (e) => {
                const courseItem = e.target.closest('.course-item');
                const courseName = courseItem.querySelector('h4').textContent;
                this.startCourseAttendance(courseName);
            });
        });

        // 批量操作按钮
        const batchBtn = document.querySelector('.btn-primary');
        if (batchBtn && batchBtn.textContent.includes('批量操作')) {
            batchBtn.addEventListener('click', () => {
                this.showBatchOperations();
            });
        }
    }

    // 处理待办事项
    handleTodoAction(title) {
        Toast.info(`正在处理: ${title}`);
        // 这里可以添加具体的处理逻辑
    }

    // 开始课程考勤
    startCourseAttendance(courseName) {
        Toast.info(`开始${courseName}考勤`);
        // 跳转到课程考勤页面
        window.location.href = 'course-attendance.html?course=' + encodeURIComponent(courseName);
    }

    // 显示批量操作
    showBatchOperations() {
        Toast.info('批量操作功能开发中...');
        // 这里可以显示批量操作的弹窗或跳转到批量操作页面
    }

    // 签到/签退
    async toggleAttendance() {
        try {
            const statusEl = document.getElementById('attendanceStatus');
            const isCheckedIn = statusEl.classList.contains('status-checked');
            
            if (isCheckedIn) {
                // 签退
                const result = await this.checkOut();
                if (result.success) {
                    statusEl.textContent = '✅ 已签退 ' + Utils.formatDate(new Date(), 'HH:mm');
                    statusEl.className = 'status-checked-out';
                    Toast.success('签退成功');
                }
            } else {
                // 签到
                const result = await this.checkIn();
                if (result.success) {
                    statusEl.textContent = '✅ 已签到 ' + Utils.formatDate(new Date(), 'HH:mm');
                    statusEl.className = 'status-checked';
                    Toast.success('签到成功');
                }
            }
        } catch (error) {
            Toast.error('操作失败，请重试');
        }
    }

    // 签到
    async checkIn() {
        // 模拟API调用
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({ success: true });
            }, 1000);
        });
    }

    // 签退
    async checkOut() {
        // 模拟API调用
        return new Promise(resolve => {
            setTimeout(() => {
                resolve({ success: true });
            }, 1000);
        });
    }

    // 刷新数据
    async refreshData() {
        Toast.info('正在刷新数据...');
        await this.loadDashboardData();
        Toast.success('数据刷新成功');
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 检查登录状态
    if (!Auth.isLoggedIn() || Auth.getCurrentRole() !== 'employee') {
        window.location.href = '/index.html';
        return;
    }
    
    new EmployeePage();
});

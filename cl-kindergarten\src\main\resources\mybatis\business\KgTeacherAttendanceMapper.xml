<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgTeacherAttendanceMapper">
    
    <resultMap type="KgTeacherAttendance" id="KgTeacherAttendanceResult">
        <result property="attendanceId"    column="attendance_id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="attendanceDate"    column="attendance_date"    />
        <result property="checkInTime"    column="check_in_time"    />
        <result property="attendanceStatus"    column="attendance_status"    />
        <result property="workHours"    column="work_hours"    />
        <result property="checkInMethod"    column="check_in_method"    />
        <result property="isConfirmed"    column="is_confirmed"    />
        <result property="confirmedBy"    column="confirmed_by"    />
        <result property="confirmedTime"    column="confirmed_time"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <!-- 教师考勤概览结果映射 -->
    <resultMap type="com.cl.project.business.domain.dto.TeacherAttendanceOverviewDto" id="TeacherAttendanceOverviewResult">
        <result property="teacherId"    column="teacher_id"    />
        <result property="dingtalkUserId"    column="dingtalk_user_id"    />
        <result property="teacherName"    column="teacher_name"    />
        <result property="attendanceDate"    column="attendance_date"    />
        <result property="checkInTime"    column="check_in_time"    />

        <result property="attendanceStatus"    column="attendance_status"    />
        <result property="workHours"    column="work_hours"    />
        <result property="isConfirmed"    column="is_confirmed"    />
    </resultMap>

    <sql id="selectKgTeacherAttendanceVo">
        select attendance_id, teacher_id, attendance_date, check_in_time,  attendance_status, work_hours, check_in_method,  is_confirmed, confirmed_by, confirmed_time, com_id, create_by, create_time, update_by, update_time, remark from kg_teacher_attendance
    </sql>

    <select id="selectKgTeacherAttendanceList" parameterType="KgTeacherAttendance" resultMap="KgTeacherAttendanceResult">
        <include refid="selectKgTeacherAttendanceVo"/>
        <where>
        <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
        <if test="teacherName != null and teacherName != ''">
            AND t.teacher_name LIKE CONCAT('%', #{teacherName}, '%')
        </if>
        <if test="attendanceStatus != null and attendanceStatus != ''">
            AND a.attendance_status = #{attendanceStatus}
        </if>
        <if test="dataSource != null and dataSource != ''">
            AND a.data_source = #{dataSource}
        </if>
            <if test="checkInTime != null "> and check_in_time = #{checkInTime}</if>

            <if test="attendanceStatus != null  and attendanceStatus != ''"> and attendance_status = #{attendanceStatus}</if>
            <if test="workHours != null "> and work_hours = #{workHours}</if>
            <if test="checkInMethod != null  and checkInMethod != ''"> and check_in_method = #{checkInMethod}</if>

            <if test="isConfirmed != null "> and is_confirmed = #{isConfirmed}</if>
            <if test="confirmedBy != null "> and confirmed_by = #{confirmedBy}</if>
            <if test="confirmedTime != null "> and confirmed_time = #{confirmedTime}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgTeacherAttendanceById" parameterType="Long" resultMap="KgTeacherAttendanceResult">
        <include refid="selectKgTeacherAttendanceVo"/>
        where attendance_id = #{attendanceId}
    </select>
        
    <insert id="insertKgTeacherAttendance" parameterType="KgTeacherAttendance" useGeneratedKeys="true" keyProperty="attendanceId">
        insert into kg_teacher_attendance
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="attendanceDate != null">attendance_date,</if>
            <if test="checkInTime != null">check_in_time,</if>

            <if test="attendanceStatus != null">attendance_status,</if>
            <if test="workHours != null">work_hours,</if>
            <if test="checkInMethod != null">check_in_method,</if>

            <if test="isConfirmed != null">is_confirmed,</if>
            <if test="confirmedBy != null">confirmed_by,</if>
            <if test="confirmedTime != null">confirmed_time,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="attendanceDate != null">#{attendanceDate},</if>
            <if test="checkInTime != null">#{checkInTime},</if>
            <if test="attendanceStatus != null">#{attendanceStatus},</if>
            <if test="workHours != null">#{workHours},</if>
            <if test="checkInMethod != null">#{checkInMethod},</if>
            <if test="isConfirmed != null">#{isConfirmed},</if>
            <if test="confirmedBy != null">#{confirmedBy},</if>
            <if test="confirmedTime != null">#{confirmedTime},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgTeacherAttendance" parameterType="KgTeacherAttendance">
        update kg_teacher_attendance
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="attendanceDate != null">attendance_date = #{attendanceDate},</if>
            <if test="checkInTime != null">check_in_time = #{checkInTime},</if>
            <if test="attendanceStatus != null">attendance_status = #{attendanceStatus},</if>
            <if test="workHours != null">work_hours = #{workHours},</if>
            <if test="checkInMethod != null">check_in_method = #{checkInMethod},</if>
            <if test="isConfirmed != null">is_confirmed = #{isConfirmed},</if>
            <if test="confirmedBy != null">confirmed_by = #{confirmedBy},</if>
            <if test="confirmedTime != null">confirmed_time = #{confirmedTime},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where attendance_id = #{attendanceId}
    </update>

    <delete id="deleteKgTeacherAttendanceById" parameterType="Long">
        delete from kg_teacher_attendance where attendance_id = #{attendanceId}
    </delete>

    <delete id="deleteKgTeacherAttendanceByIds" parameterType="String">
        delete from kg_teacher_attendance where attendance_id in 
        <foreach item="attendanceId" collection="array" open="(" separator="," close=")">
            #{attendanceId}
        </foreach>
    </delete>

    <!-- 查询教师考勤概览列表 -->
    <select id="selectTeacherAttendanceOverview" parameterType="java.util.Date" resultMap="TeacherAttendanceOverviewResult">
        SELECT
          t.teacher_id,
          t.dingtalk_user_id,
          t.teacher_name,
          #{attendanceDate} as attendance_date,
          a.check_in_time,
          a.attendance_status,
          a.work_hours,
          a.is_confirmed
        FROM
          kg_teacher t
        LEFT JOIN kg_teacher_attendance a
          ON t.teacher_id = a.teacher_id
          AND DATE(a.attendance_date) = DATE(#{attendanceDate})
          AND a.com_id = t.com_id
        WHERE
          t.status = '0'
        ORDER BY
          t.teacher_name
    </select>
    
    <!-- 查询指定教师某天的所有手动签到记录 -->
    <select id="selectManualByTeacherAndDate" resultMap="KgTeacherAttendanceResult">
        SELECT * FROM kg_teacher_attendance
        WHERE teacher_id = #{teacherId}
          AND attendance_date &gt;= #{dateFrom}
          AND attendance_date &lt;= #{dateTo}
          AND (
            check_in_method = 'manual'
          )
        ORDER BY attendance_date, check_in_time
    </select>

    <!-- 批量确认考勤记录 -->
    <update id="batchConfirmAttendance">
        UPDATE kg_teacher_attendance
        SET is_confirmed = 1,
            confirmed_by = #{confirmedBy},
            confirmed_time = NOW(),
            update_time = NOW()
        WHERE attendance_id IN
        <foreach collection="attendanceIds" item="id" open="(" separator="," close=")">
            #{id}
        </foreach>
    </update>

</mapper>
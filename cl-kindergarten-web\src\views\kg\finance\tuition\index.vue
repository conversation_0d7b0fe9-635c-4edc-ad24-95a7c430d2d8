<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
      <el-form-item label="学生姓名" prop="studentName">
        <el-input
          v-model="queryParams.studentName"
          placeholder="请输入学生姓名"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="班级" prop="classId">
        <el-select v-model="queryParams.classId" placeholder="请选择班级" clearable size="small">
          <el-option
            v-for="item in classList"
            :key="item.classId"
            :label="item.className"
            :value="item.classId"
          />
        </el-select>
      </el-form-item>
      <el-form-item label="费用月份" prop="feeMonth">
        <el-date-picker clearable size="small" style="width: 200px"
          v-model="queryParams.feeMonth"
          type="month"
          value-format="yyyy-MM"
          placeholder="选择费用月份">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="缴费状态" prop="paymentStatus">
        <el-select v-model="queryParams.paymentStatus" placeholder="请选择缴费状态" clearable size="small">
          <el-option label="未缴费" value="0" />
          <el-option label="已缴费" value="1" />
          <el-option label="部分缴费" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleCalculate"
          v-hasPermi="['kg:finance:tuition:calculate']"
        >园费计算</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-message"
          size="mini"
          @click="handleSendBill"
          v-hasPermi="['kg:finance:tuition:send']"
        >发送账单</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['kg:finance:tuition:view']"
        >导出</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="tuitionList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="学生姓名" align="center" prop="studentName" />
      <el-table-column label="班级" align="center" prop="className" />
      <el-table-column label="费用月份" align="center" prop="feeMonth" />
      <el-table-column label="基础园费" align="center" prop="baseFee">
        <template slot-scope="scope">
          <span>¥{{ scope.row.baseFee }}</span>
        </template>
      </el-table-column>
      <el-table-column label="餐费" align="center" prop="mealFee">
        <template slot-scope="scope">
          <span>¥{{ scope.row.mealFee }}</span>
        </template>
      </el-table-column>
      <el-table-column label="其他费用" align="center" prop="otherFee">
        <template slot-scope="scope">
          <span>¥{{ scope.row.otherFee }}</span>
        </template>
      </el-table-column>
      <el-table-column label="应缴总额" align="center" prop="totalFee">
        <template slot-scope="scope">
          <span style="color: #E6A23C; font-weight: bold;">¥{{ scope.row.totalFee }}</span>
        </template>
      </el-table-column>
      <el-table-column label="已缴金额" align="center" prop="paidAmount">
        <template slot-scope="scope">
          <span style="color: #67C23A;">¥{{ scope.row.paidAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="未缴金额" align="center" prop="unpaidAmount">
        <template slot-scope="scope">
          <span style="color: #F56C6C;">¥{{ scope.row.unpaidAmount }}</span>
        </template>
      </el-table-column>
      <el-table-column label="缴费状态" align="center" prop="paymentStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.kg_payment_status" :value="scope.row.paymentStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="出勤天数" align="center" prop="attendanceDays" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
            v-hasPermi="['kg:finance:tuition:view']"
          >查看</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-message"
            @click="handleSendSingle(scope.row)"
            v-hasPermi="['kg:finance:tuition:send']"
          >发送</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 园费计算对话框 -->
    <el-dialog title="园费计算" :visible.sync="calculateOpen" width="500px" append-to-body>
      <el-form ref="calculateForm" :model="calculateForm" :rules="calculateRules" label-width="80px">
        <el-form-item label="费用月份" prop="feeMonth">
          <el-date-picker clearable size="small" style="width: 200px"
            v-model="calculateForm.feeMonth"
            type="month"
            value-format="yyyy-MM"
            placeholder="选择费用月份">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="班级" prop="classId">
          <el-select v-model="calculateForm.classId" placeholder="请选择班级（可选）" clearable>
            <el-option
              v-for="item in classList"
              :key="item.classId"
              :label="item.className"
              :value="item.classId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="基础园费" prop="baseFee">
          <el-input v-model="calculateForm.baseFee" placeholder="请输入基础园费" />
        </el-form-item>
        <el-form-item label="餐费单价" prop="mealFeePerDay">
          <el-input v-model="calculateForm.mealFeePerDay" placeholder="请输入每天餐费" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitCalculate">开始计算</el-button>
        <el-button @click="calculateOpen = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 园费详情对话框 -->
    <el-dialog title="园费详情" :visible.sync="detailOpen" width="600px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="学生姓名">{{ detailData.studentName }}</el-descriptions-item>
        <el-descriptions-item label="班级">{{ detailData.className }}</el-descriptions-item>
        <el-descriptions-item label="费用月份">{{ detailData.feeMonth }}</el-descriptions-item>
        <el-descriptions-item label="出勤天数">{{ detailData.attendanceDays }}天</el-descriptions-item>
        <el-descriptions-item label="基础园费">¥{{ detailData.baseFee }}</el-descriptions-item>
        <el-descriptions-item label="餐费">¥{{ detailData.mealFee }}</el-descriptions-item>
        <el-descriptions-item label="其他费用">¥{{ detailData.otherFee }}</el-descriptions-item>
        <el-descriptions-item label="应缴总额">
          <span style="color: #E6A23C; font-weight: bold;">¥{{ detailData.totalFee }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="已缴金额">
          <span style="color: #67C23A;">¥{{ detailData.paidAmount }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="未缴金额">
          <span style="color: #F56C6C;">¥{{ detailData.unpaidAmount }}</span>
        </el-descriptions-item>
      </el-descriptions>
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listTuition, getTuition, calculateTuition, sendTuitionBill, exportTuition } from "@/api/kg/finance/tuition";
import { listClass } from "@/api/kg/student/class";

export default {
  name: "TuitionManage",
  dicts: ['kg_payment_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 园费表格数据
      tuitionList: [],
      // 班级列表
      classList: [],
      // 是否显示计算对话框
      calculateOpen: false,
      // 是否显示详情对话框
      detailOpen: false,
      // 详情数据
      detailData: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        studentName: undefined,
        classId: undefined,
        feeMonth: undefined,
        paymentStatus: undefined,
      },
      // 计算表单参数
      calculateForm: {},
      // 计算表单校验
      calculateRules: {
        feeMonth: [
          { required: true, message: "费用月份不能为空", trigger: "blur" }
        ],
        baseFee: [
          { required: true, message: "基础园费不能为空", trigger: "blur" }
        ],
        mealFeePerDay: [
          { required: true, message: "餐费单价不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
    this.getClassList();
  },
  methods: {
    /** 查询园费列表 */
    getList() {
      this.loading = true;
      listTuition(this.queryParams).then(response => {
        this.tuitionList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询班级列表 */
    getClassList() {
      listClass().then(response => {
        this.classList = response.rows;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.tuitionId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    /** 园费计算按钮操作 */
    handleCalculate() {
      this.calculateForm = {
        feeMonth: undefined,
        classId: undefined,
        baseFee: undefined,
        mealFeePerDay: undefined
      };
      this.calculateOpen = true;
    },
    /** 提交计算 */
    submitCalculate() {
      this.$refs["calculateForm"].validate(valid => {
        if (valid) {
          calculateTuition(this.calculateForm).then(response => {
            if (response.code === 200) {
              this.msgSuccess("园费计算成功");
              this.calculateOpen = false;
              this.getList();
            }
          });
        }
      });
    },
    /** 查看详情 */
    handleView(row) {
      getTuition(row.tuitionId).then(response => {
        this.detailData = response.data;
        this.detailOpen = true;
      });
    },
    /** 发送账单按钮操作 */
    handleSendBill() {
      const tuitionIds = this.ids;
      if (tuitionIds.length === 0) {
        this.msgError("请选择要发送账单的记录");
        return;
      }
      this.$confirm('是否确认发送选中的园费账单?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        sendTuitionBill(tuitionIds).then(response => {
          if (response.code === 200) {
            this.msgSuccess("账单发送成功");
            this.getList();
          }
        });
      }).catch(() => {});
    },
    /** 发送单个账单 */
    handleSendSingle(row) {
      this.$confirm('是否确认发送该园费账单?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        sendTuitionBill([row.tuitionId]).then(response => {
          if (response.code === 200) {
            this.msgSuccess("账单发送成功");
            this.getList();
          }
        });
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有园费数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportTuition(queryParams);
        }).then(response => {
          this.download(response.msg);
        }).catch(function() {});
    }
  }
};
</script>

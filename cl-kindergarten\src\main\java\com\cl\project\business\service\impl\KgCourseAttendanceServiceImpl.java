package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgCourseAttendanceMapper;
import com.cl.project.business.domain.KgCourseAttendance;
import com.cl.project.business.service.IKgCourseAttendanceService;

/**
 * 托管考勤记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgCourseAttendanceServiceImpl implements IKgCourseAttendanceService 
{
    @Autowired
    private KgCourseAttendanceMapper kgCourseAttendanceMapper;

    /**
     * 查询托管考勤记录
     * 
     * @param attendanceId 托管考勤记录ID
     * @return 托管考勤记录
     */
    @Override
    public KgCourseAttendance selectKgCourseAttendanceById(Long attendanceId)
    {
        return kgCourseAttendanceMapper.selectKgCourseAttendanceById(attendanceId);
    }

    /**
     * 查询托管考勤记录列表
     * 
     * @param kgCourseAttendance 托管考勤记录
     * @return 托管考勤记录
     */
    @Override
    public List<KgCourseAttendance> selectKgCourseAttendanceList(KgCourseAttendance kgCourseAttendance)
    {
        return kgCourseAttendanceMapper.selectKgCourseAttendanceList(kgCourseAttendance);
    }

    /**
     * 新增托管考勤记录
     * 
     * @param kgCourseAttendance 托管考勤记录
     * @return 结果
     */
    @Override
    public int insertKgCourseAttendance(KgCourseAttendance kgCourseAttendance)
    {
        kgCourseAttendance.setCreateTime(DateUtils.getNowDate());
        return kgCourseAttendanceMapper.insertKgCourseAttendance(kgCourseAttendance);
    }

    /**
     * 修改托管考勤记录
     * 
     * @param kgCourseAttendance 托管考勤记录
     * @return 结果
     */
    @Override
    public int updateKgCourseAttendance(KgCourseAttendance kgCourseAttendance)
    {
        kgCourseAttendance.setUpdateTime(DateUtils.getNowDate());
        return kgCourseAttendanceMapper.updateKgCourseAttendance(kgCourseAttendance);
    }

    /**
     * 批量删除托管考勤记录
     * 
     * @param attendanceIds 需要删除的托管考勤记录ID
     * @return 结果
     */
    @Override
    public int deleteKgCourseAttendanceByIds(Long[] attendanceIds)
    {
        return kgCourseAttendanceMapper.deleteKgCourseAttendanceByIds(attendanceIds);
    }

    /**
     * 删除托管考勤记录信息
     * 
     * @param attendanceId 托管考勤记录ID
     * @return 结果
     */
    @Override
    public int deleteKgCourseAttendanceById(Long attendanceId)
    {
        return kgCourseAttendanceMapper.deleteKgCourseAttendanceById(attendanceId);
    }
}

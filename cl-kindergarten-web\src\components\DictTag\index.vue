`<template>
  <div>
    <template v-if="showTag">
      <el-tag
        :type="type"
        :class="className"
        :effect="effect"
      >{{ label }}</el-tag>
    </template>
    <template v-else>{{ label }}</template>
  </div>
</template>

<script>
export default {
  name: "DictTag",
  props: {
    // 数据
    value: [Number, String, Array],
    // 字典类型
    options: {
      type: Array,
      default: null
    },
    // 大小
    size: {
      type: String,
      default: "medium"
    },
    // 是否显示为标签
    showTag: {
      type: Boolean,
      default: true
    }
  },
  computed: {
    label() {
      if (!this.options || !Array.isArray(this.options) || this.options.length === 0) {
        return this.value;
      }
      
      // 调试信息（开发环境）
      if (process.env.NODE_ENV === 'development') {
        console.log('DictTag debug:', {
          value: this.value,
          valueType: typeof this.value,
          options: this.options
        });
      }
      
      // 查找匹配项的通用函数
      const findOption = (compareValue) => {
        return this.options.find(item => {
          // 优先使用标准格式 (label/value)
          if (item.value !== undefined) {
            return item.value === compareValue || item.value == compareValue;
          }
          // 兼容后端格式 (dictLabel/dictValue)
          if (item.dictValue !== undefined) {
            return item.dictValue === compareValue || item.dictValue == compareValue;
          }
          return false;
        });
      };
      
      // 1. 原值匹配
      let option = findOption(this.value);
      if (option) return option.label || option.dictLabel;
      
      // 2. 字符串转数字匹配
      if (typeof this.value === 'string' && !isNaN(this.value)) {
        option = findOption(Number(this.value));
        if (option) return option.label || option.dictLabel;
      }
      
      // 3. 数字转字符串匹配
      if (typeof this.value === 'number') {
        option = findOption(String(this.value));
        if (option) return option.label || option.dictLabel;
      }
      
      // 未找到匹配项，返回原值
      return this.value;
    },
    type() {
      if (!this.options || !Array.isArray(this.options) || this.options.length === 0) {
        return '';
      }
      
      // 查找匹配项的通用函数
      const findOption = (compareValue) => {
        return this.options.find(item => {
          // 优先使用标准格式 (label/value)
          if (item.value !== undefined) {
            return item.value === compareValue || item.value == compareValue;
          }
          // 兼容后端格式 (dictLabel/dictValue)
          if (item.dictValue !== undefined) {
            return item.dictValue === compareValue || item.dictValue == compareValue;
          }
          return false;
        });
      };
      
      // 1. 原值匹配
      let option = findOption(this.value);
      if (option) return option.type || option.listClass || '';
      
      // 2. 字符串转数字匹配
      if (typeof this.value === 'string' && !isNaN(this.value)) {
        option = findOption(Number(this.value));
        if (option) return option.type || option.listClass || '';
      }
      
      // 3. 数字转字符串匹配
      if (typeof this.value === 'number') {
        option = findOption(String(this.value));
        if (option) return option.type || option.listClass || '';
      }
      
      return '';
    },
    className() {
      return this.size === "mini" ? "tag-mini" : "";
    },
    effect() {
      return "light";
    }
  }
};
</script>

<style scoped>
.tag-mini {
  height: 20px;
  padding: 0 5px;
  line-height: 19px;
}
</style>`

package com.cl.project.business.service;

import java.util.List;
import com.cl.project.business.domain.KgCourseBill;

/**
 * 托管费账单Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IKgCourseBillService 
{
    /**
     * 查询托管费账单
     * 
     * @param billId 托管费账单ID
     * @return 托管费账单
     */
    public KgCourseBill selectKgCourseBillById(Long billId);

    /**
     * 查询托管费账单列表
     * 
     * @param kgCourseBill 托管费账单
     * @return 托管费账单集合
     */
    public List<KgCourseBill> selectKgCourseBillList(KgCourseBill kgCourseBill);

    /**
     * 新增托管费账单
     * 
     * @param kgCourseBill 托管费账单
     * @return 结果
     */
    public int insertKgCourseBill(KgCourseBill kgCourseBill);

    /**
     * 修改托管费账单
     * 
     * @param kgCourseBill 托管费账单
     * @return 结果
     */
    public int updateKgCourseBill(KgCourseBill kgCourseBill);

    /**
     * 批量删除托管费账单
     * 
     * @param billIds 需要删除的托管费账单ID
     * @return 结果
     */
    public int deleteKgCourseBillByIds(Long[] billIds);

    /**
     * 删除托管费账单信息
     * 
     * @param billId 托管费账单ID
     * @return 结果
     */
    public int deleteKgCourseBillById(Long billId);
}

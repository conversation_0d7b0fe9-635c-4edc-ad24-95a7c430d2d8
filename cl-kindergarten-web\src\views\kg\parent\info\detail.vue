<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>家长详细信息</span>
        <div style="float: right;">
          <el-button type="primary" size="small" @click="handleEdit">编辑</el-button>
          <el-button size="small" @click="handleBack">返回</el-button>
        </div>
      </div>
      
      <!-- 基本信息 -->
      <div class="detail-section">
        <h4 class="section-title">基本信息</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>家长编号：</label>
              <span>{{ parentInfo.parentCode }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>家长姓名：</label>
              <span>{{ parentInfo.parentName }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>联系电话：</label>
              <span>{{ parentInfo.phone }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>与学生关系：</label>
              <span>{{ getRelationshipText(parentInfo.relationship) }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>身份证号：</label>
              <span>{{ parentInfo.idCard || '未填写' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>工作单位：</label>
              <span>{{ parentInfo.workUnit || '未填写' }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="24">
            <div class="detail-item">
              <label>家庭住址：</label>
              <span>{{ parentInfo.address || '未填写' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 微信绑定信息 -->
      <div class="detail-section">
        <h4 class="section-title">微信绑定信息</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>绑定状态：</label>
              <el-tag :type="parentInfo.isWechatBound === 1 ? 'success' : 'info'">
                {{ parentInfo.isWechatBound === 1 ? '已绑定' : '未绑定' }}
              </el-tag>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>绑定时间：</label>
              <span>{{ parentInfo.wechatBindTime || '未绑定' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item" v-if="parentInfo.isWechatBound === 0">
              <el-button type="primary" size="small" @click="handleWechatBind">立即绑定</el-button>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 子女信息 -->
      <div class="detail-section">
        <h4 class="section-title">子女信息</h4>
        <el-table :data="childrenInfo" style="width: 100%">
          <el-table-column prop="studentCode" label="学生编号" width="120" />
          <el-table-column prop="studentName" label="学生姓名" width="100" />
          <el-table-column prop="gender" label="性别" width="60">
            <template slot-scope="scope">
              {{ scope.row.gender === '0' ? '男' : '女' }}
            </template>
          </el-table-column>
          <el-table-column prop="birthDate" label="出生日期" width="100" />
          <el-table-column prop="className" label="所在班级" width="100" />
          <el-table-column prop="enrollmentDate" label="入园日期" width="100" />
          <el-table-column prop="status" label="状态" width="80">
            <template slot-scope="scope">
              <el-tag :type="getStudentStatusType(scope.row.status)" size="mini">
                {{ getStudentStatusText(scope.row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" width="100">
            <template slot-scope="scope">
              <el-button type="text" size="small" @click="viewStudent(scope.row)">查看</el-button>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <!-- 费用统计 -->
      <div class="detail-section">
        <h4 class="section-title">费用统计</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-number">¥{{ feeStats.totalAmount }}</div>
              <div class="stat-label">总应缴费用</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-number">¥{{ feeStats.paidAmount }}</div>
              <div class="stat-label">已缴费用</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-number">¥{{ feeStats.unpaidAmount }}</div>
              <div class="stat-label">未缴费用</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 备注信息 -->
      <div class="detail-section">
        <h4 class="section-title">备注信息</h4>
        <div class="detail-item">
          <span>{{ parentInfo.remark || '无备注信息' }}</span>
        </div>
      </div>

      <!-- 系统信息 -->
      <div class="detail-section">
        <h4 class="section-title">系统信息</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>创建人：</label>
              <span>{{ parentInfo.createBy }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ parentInfo.createTime }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>更新时间：</label>
              <span>{{ parentInfo.updateTime }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 最近消息记录 -->
    <el-row :gutter="20" style="margin-top: 20px;">
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>最近消息记录</span>
            <el-button type="text" style="float: right;" @click="$router.push('/kg/communication/message')">
              查看更多
            </el-button>
          </div>
          <el-table :data="recentMessages" style="width: 100%">
            <el-table-column prop="messageType" label="消息类型" width="80">
              <template slot-scope="scope">
                <el-tag :type="getMessageTypeColor(scope.row.messageType)" size="mini">
                  {{ getMessageTypeText(scope.row.messageType) }}
                </el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="title" label="标题" />
            <el-table-column prop="sendTime" label="发送时间" width="150" />
            <el-table-column prop="readStatus" label="状态" width="80">
              <template slot-scope="scope">
                <el-tag :type="scope.row.readStatus === 'read' ? 'success' : 'warning'" size="mini">
                  {{ scope.row.readStatus === 'read' ? '已读' : '未读' }}
                </el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
      
      <el-col :span="12">
        <el-card class="box-card">
          <div slot="header" class="clearfix">
            <span>缴费记录</span>
            <el-button type="text" style="float: right;" @click="$router.push('/kg/finance/tuition')">
              查看更多
            </el-button>
          </div>
          <el-table :data="paymentRecords" style="width: 100%">
            <el-table-column prop="billMonth" label="账单月份" width="100" />
            <el-table-column prop="amount" label="金额" width="80" />
            <el-table-column prop="paymentTime" label="缴费时间" width="100" />
            <el-table-column prop="paymentMethod" label="缴费方式" width="80">
              <template slot-scope="scope">
                <el-tag size="mini">{{ scope.row.paymentMethod }}</el-tag>
              </template>
            </el-table-column>
          </el-table>
        </el-card>
      </el-col>
    </el-row>

    <!-- 微信绑定对话框 -->
    <el-dialog title="微信绑定" :visible.sync="wechatBindDialog" width="400px">
      <div style="text-align: center;">
        <p>请使用微信扫描下方二维码进行绑定</p>
        <div id="qrcode" style="margin: 20px auto; width: 200px; height: 200px; border: 1px solid #ddd;"></div>
        <p style="color: #999; font-size: 12px;">绑定成功后可接收学生在园信息推送</p>
      </div>
      <div slot="footer" class="dialog-footer">
        <el-button @click="wechatBindDialog = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getParent } from "@/api/kg/parent/info";

export default {
  name: "ParentDetail",
  data() {
    return {
      // 家长信息
      parentInfo: {
        parentId: undefined,
        parentCode: '',
        parentName: '',
        phone: '',
        relationship: '',
        idCard: '',
        workUnit: '',
        address: '',
        wechatOpenid: '',
        isWechatBound: 0,
        wechatBindTime: '',
        remark: '',
        createBy: '',
        createTime: '',
        updateTime: ''
      },
      // 子女信息
      childrenInfo: [],
      // 费用统计
      feeStats: {
        totalAmount: 0,
        paidAmount: 0,
        unpaidAmount: 0
      },
      // 最近消息记录
      recentMessages: [],
      // 缴费记录
      paymentRecords: [],
      // 微信绑定对话框
      wechatBindDialog: false
    };
  },
  created() {
    const parentId = this.$route.query.parentId || this.$route.params.parentId;
    if (parentId) {
      this.getParentInfo(parentId);
      this.loadChildrenInfo(parentId);
      this.loadFeeStats(parentId);
      this.loadRecentRecords(parentId);
    }
  },
  methods: {
    // 获取家长信息
    getParentInfo(parentId) {
      getParent(parentId).then(response => {
        this.parentInfo = response.data;
      });
    },
    
    // 加载子女信息
    loadChildrenInfo(parentId) {
      // 模拟数据，实际应该调用API
      this.childrenInfo = [
        {
          studentId: 1,
          studentCode: 'STU001',
          studentName: '张小明',
          gender: '0',
          birthDate: '2018-03-15',
          className: '大班A',
          enrollmentDate: '2023-09-01',
          status: '0'
        },
        {
          studentId: 2,
          studentCode: 'STU002',
          studentName: '张小红',
          gender: '1',
          birthDate: '2020-06-20',
          className: '中班B',
          enrollmentDate: '2024-09-01',
          status: '0'
        }
      ];
    },
    
    // 加载费用统计
    loadFeeStats(parentId) {
      // 模拟数据，实际应该调用API
      this.feeStats = {
        totalAmount: 4000,
        paidAmount: 3500,
        unpaidAmount: 500
      };
    },
    
    // 加载最近记录
    loadRecentRecords(parentId) {
      // 模拟数据，实际应该调用API
      this.recentMessages = [
        { messageType: 'notice', title: '关于放假安排的通知', sendTime: '2025-07-29 10:30', readStatus: 'unread' },
        { messageType: 'attendance', title: '张小明今日出勤情况', sendTime: '2025-07-28 18:00', readStatus: 'read' },
        { messageType: 'fee', title: '8月份费用账单', sendTime: '2025-07-27 09:00', readStatus: 'read' }
      ];
      
      this.paymentRecords = [
        { billMonth: '2025-07', amount: '2000', paymentTime: '2025-07-05', paymentMethod: '微信' },
        { billMonth: '2025-06', amount: '2000', paymentTime: '2025-06-03', paymentMethod: '支付宝' },
        { billMonth: '2025-05', amount: '1800', paymentTime: '2025-05-08', paymentMethod: '银行卡' }
      ];
    },
    
    // 获取关系文本
    getRelationshipText(relationship) {
      const relationshipMap = {
        'father': '父亲',
        'mother': '母亲',
        'grandfather_paternal': '爷爷',
        'grandmother_paternal': '奶奶',
        'grandfather_maternal': '外公',
        'grandmother_maternal': '外婆',
        'other': '其他'
      };
      return relationshipMap[relationship] || '未知';
    },
    
    // 获取学生状态类型
    getStudentStatusType(status) {
      const statusMap = {
        '0': 'success',
        '1': 'danger',
        '2': 'warning'
      };
      return statusMap[status] || 'info';
    },
    
    // 获取学生状态文本
    getStudentStatusText(status) {
      const statusMap = {
        '0': '在园',
        '1': '退园',
        '2': '请假'
      };
      return statusMap[status] || '未知';
    },
    
    // 获取消息类型颜色
    getMessageTypeColor(type) {
      const colorMap = {
        'notice': 'primary',
        'attendance': 'success',
        'fee': 'warning',
        'activity': 'info'
      };
      return colorMap[type] || 'info';
    },
    
    // 获取消息类型文本
    getMessageTypeText(type) {
      const textMap = {
        'notice': '通知',
        'attendance': '考勤',
        'fee': '费用',
        'activity': '活动'
      };
      return textMap[type] || '其他';
    },
    
    // 查看学生
    viewStudent(row) {
      this.$router.push({
        path: '/kg/student/info/detail',
        query: { studentId: row.studentId }
      });
    },
    
    // 微信绑定
    handleWechatBind() {
      this.wechatBindDialog = true;
      this.$nextTick(() => {
        document.getElementById('qrcode').innerHTML = '<div style="line-height: 200px; text-align: center; color: #999;">二维码占位</div>';
      });
    },
    
    // 编辑家长
    handleEdit() {
      this.$router.push({
        path: '/kg/parent/info/form',
        query: { parentId: this.parentInfo.parentId }
      });
    },
    
    // 返回列表
    handleBack() {
      this.$router.push('/kg/parent/info');
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.box-card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.detail-section {
  margin-bottom: 30px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #f0f0f0;
}

.detail-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  
  label {
    font-weight: 500;
    color: #606266;
    min-width: 100px;
    margin-right: 10px;
  }
  
  span {
    color: #303133;
    flex: 1;
  }
}

.stat-card {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  
  .stat-number {
    font-size: 24px;
    font-weight: 600;
    color: #409eff;
    margin-bottom: 8px;
  }
  
  .stat-label {
    font-size: 14px;
    color: #606266;
  }
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>

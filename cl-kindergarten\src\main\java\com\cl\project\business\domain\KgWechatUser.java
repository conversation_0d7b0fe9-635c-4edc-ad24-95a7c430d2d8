package com.cl.project.business.domain;

import java.util.Date;

import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 微信用户对象 kg_wechat_user
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgWechatUser extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 微信用户ID */
    private Long wechatUserId;

    /** 微信openid */
    @Excel(name = "微信openid")
    private String openid;

    /** 微信unionid */
    @Excel(name = "微信unionid")
    private String unionid;

    /** 微信昵称 */
    @Excel(name = "微信昵称")
    private String nickname;

    /** 头像地址 */
    @Excel(name = "头像地址")
    private String avatarUrl;

    /** 性别（0未知、1男、2女） */
    @Excel(name = "性别", readConverterExp = "0=未知、1男、2女")
    private Long gender;

    /** 城市 */
    @Excel(name = "城市")
    private String city;

    /** 省份 */
    @Excel(name = "省份")
    private String province;

    /** 国家 */
    @Excel(name = "国家")
    private String country;

    /** 语言 */
    @Excel(name = "语言")
    private String language;

    /** 用户类型（parent家长、teacher教师、admin管理员） */
    @Excel(name = "用户类型", readConverterExp = "p=arent家长、teacher教师、admin管理员")
    private String userType;

    /** 绑定的系统用户ID（教师、管理员） */
    @Excel(name = "绑定的系统用户ID", readConverterExp = "教=师、管理员")
    private Long bindUserId;

    /** 关联学生ID（家长用户），关联kg_student.student_id */
    @Excel(name = "关联学生ID", readConverterExp = "家=长用户")
    private Long studentId;

    /** 手机号 */
    @Excel(name = "手机号")
    private String phone;

    /** 绑定状态（unbound未绑定、pending待审核、bound已绑定、rejected已拒绝） */
    @Excel(name = "绑定状态", readConverterExp = "u=nbound未绑定、pending待审核、bound已绑定、rejected已拒绝")
    private String bindStatus;

    /** 绑定时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "绑定时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date bindTime;

    /** 审核人ID */
    @Excel(name = "审核人ID")
    private Long approvedBy;

    /** 审核时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审核时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date approvalTime;

    /** 是否关注公众号（0否 1是） */
    @Excel(name = "是否关注公众号", readConverterExp = "0=否,1=是")
    private Long isSubscribed;

    /** 关注时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "关注时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date subscribeTime;

    /** 取消关注时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "取消关注时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date unsubscribeTime;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    public void setWechatUserId(Long wechatUserId) 
    {
        this.wechatUserId = wechatUserId;
    }

    public Long getWechatUserId() 
    {
        return wechatUserId;
    }
    public void setOpenid(String openid) 
    {
        this.openid = openid;
    }

    public String getOpenid() 
    {
        return openid;
    }
    public void setUnionid(String unionid) 
    {
        this.unionid = unionid;
    }

    public String getUnionid() 
    {
        return unionid;
    }
    public void setNickname(String nickname) 
    {
        this.nickname = nickname;
    }

    public String getNickname() 
    {
        return nickname;
    }
    public void setAvatarUrl(String avatarUrl) 
    {
        this.avatarUrl = avatarUrl;
    }

    public String getAvatarUrl() 
    {
        return avatarUrl;
    }
    public void setGender(Long gender) 
    {
        this.gender = gender;
    }

    public Long getGender() 
    {
        return gender;
    }
    public void setCity(String city) 
    {
        this.city = city;
    }

    public String getCity() 
    {
        return city;
    }
    public void setProvince(String province) 
    {
        this.province = province;
    }

    public String getProvince() 
    {
        return province;
    }
    public void setCountry(String country) 
    {
        this.country = country;
    }

    public String getCountry() 
    {
        return country;
    }
    public void setLanguage(String language) 
    {
        this.language = language;
    }

    public String getLanguage() 
    {
        return language;
    }
    public void setUserType(String userType) 
    {
        this.userType = userType;
    }

    public String getUserType() 
    {
        return userType;
    }
    public void setBindUserId(Long bindUserId) 
    {
        this.bindUserId = bindUserId;
    }

    public Long getBindUserId() 
    {
        return bindUserId;
    }
    public void setStudentId(Long studentId) 
    {
        this.studentId = studentId;
    }

    public Long getStudentId() 
    {
        return studentId;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setBindStatus(String bindStatus) 
    {
        this.bindStatus = bindStatus;
    }

    public String getBindStatus() 
    {
        return bindStatus;
    }
    public void setBindTime(Date bindTime) 
    {
        this.bindTime = bindTime;
    }

    public Date getBindTime() 
    {
        return bindTime;
    }
    public void setApprovedBy(Long approvedBy) 
    {
        this.approvedBy = approvedBy;
    }

    public Long getApprovedBy() 
    {
        return approvedBy;
    }
    public void setApprovalTime(Date approvalTime) 
    {
        this.approvalTime = approvalTime;
    }

    public Date getApprovalTime() 
    {
        return approvalTime;
    }
    public void setIsSubscribed(Long isSubscribed) 
    {
        this.isSubscribed = isSubscribed;
    }

    public Long getIsSubscribed() 
    {
        return isSubscribed;
    }
    public void setSubscribeTime(Date subscribeTime) 
    {
        this.subscribeTime = subscribeTime;
    }

    public Date getSubscribeTime() 
    {
        return subscribeTime;
    }
    public void setUnsubscribeTime(Date unsubscribeTime) 
    {
        this.unsubscribeTime = unsubscribeTime;
    }

    public Date getUnsubscribeTime() 
    {
        return unsubscribeTime;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("wechatUserId", getWechatUserId())
            .append("openid", getOpenid())
            .append("unionid", getUnionid())
            .append("nickname", getNickname())
            .append("avatarUrl", getAvatarUrl())
            .append("gender", getGender())
            .append("city", getCity())
            .append("province", getProvince())
            .append("country", getCountry())
            .append("language", getLanguage())
            .append("userType", getUserType())
            .append("bindUserId", getBindUserId())
            .append("studentId", getStudentId())
            .append("phone", getPhone())
            .append("bindStatus", getBindStatus())
            .append("bindTime", getBindTime())
            .append("approvedBy", getApprovedBy())
            .append("approvalTime", getApprovalTime())
            .append("isSubscribed", getIsSubscribed())
            .append("subscribeTime", getSubscribeTime())
            .append("unsubscribeTime", getUnsubscribeTime())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgClassMapper">
    
    <resultMap type="KgClass" id="KgClassResult">
        <result property="classId"    column="class_id"    />
        <result property="className"    column="class_name"    />
        <result property="classType"    column="class_type"    />
        <result property="capacity"    column="capacity"    />
        <result property="currentCount"    column="current_count"    />
        <result property="headTeacherId"    column="head_teacher_id"    />
        <result property="assistantTeacherId"    column="assistant_teacher_id"    />
        <result property="dingtalkDeptId"    column="dingtalk_dept_id"    />
        <result property="status"    column="status"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgClassVo">
        select class_id, class_name, class_type, capacity, current_count, head_teacher_id, assistant_teacher_id, dingtalk_dept_id, status, com_id, create_by, create_time, update_by, update_time, remark from kg_class
    </sql>

    <select id="selectKgClassList" parameterType="KgClass" resultMap="KgClassResult">
        <include refid="selectKgClassVo"/>
        <where>  
            <if test="className != null  and className != ''"> and class_name like concat('%', #{className}, '%')</if>
            <if test="classType != null  and classType != ''"> and class_type = #{classType}</if>
            <if test="capacity != null "> and capacity = #{capacity}</if>
            <if test="currentCount != null "> and current_count = #{currentCount}</if>
            <if test="headTeacherId != null "> and head_teacher_id = #{headTeacherId}</if>
            <if test="assistantTeacherId != null "> and assistant_teacher_id = #{assistantTeacherId}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgClassById" parameterType="Long" resultMap="KgClassResult">
        <include refid="selectKgClassVo"/>
        where class_id = #{classId}
    </select>
    
    <select id="selectKgClassByDingtalkDeptId" parameterType="Long" resultMap="KgClassResult">
        <include refid="selectKgClassVo"/>
        where dingtalk_dept_id = #{dingtalkDeptId}
    </select>
        
    <insert id="insertKgClass" parameterType="KgClass" useGeneratedKeys="true" keyProperty="classId">
        insert into kg_class
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="className != null and className != ''">class_name,</if>
            <if test="classType != null">class_type,</if>
            <if test="capacity != null">capacity,</if>
            <if test="currentCount != null">current_count,</if>
            <if test="headTeacherId != null">head_teacher_id,</if>
            <if test="assistantTeacherId != null">assistant_teacher_id,</if>
            <if test="dingtalkDeptId != null">dingtalk_dept_id,</if>
            <if test="status != null">status,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="className != null and className != ''">#{className},</if>
            <if test="classType != null">#{classType},</if>
            <if test="capacity != null">#{capacity},</if>
            <if test="currentCount != null">#{currentCount},</if>
            <if test="headTeacherId != null">#{headTeacherId},</if>
            <if test="assistantTeacherId != null">#{assistantTeacherId},</if>
            <if test="dingtalkDeptId != null">#{dingtalkDeptId},</if>
            <if test="status != null">#{status},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgClass" parameterType="KgClass">
        update kg_class
        <trim prefix="SET" suffixOverrides=",">
            <if test="className != null and className != ''">class_name = #{className},</if>
            <if test="classType != null">class_type = #{classType},</if>
            <if test="capacity != null">capacity = #{capacity},</if>
            <if test="currentCount != null">current_count = #{currentCount},</if>
            <if test="headTeacherId != null">head_teacher_id = #{headTeacherId},</if>
            <if test="assistantTeacherId != null">assistant_teacher_id = #{assistantTeacherId},</if>
            <if test="dingtalkDeptId != null">dingtalk_dept_id = #{dingtalkDeptId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where class_id = #{classId}
    </update>

    <delete id="deleteKgClassById" parameterType="Long">
        delete from kg_class where class_id = #{classId}
    </delete>

    <delete id="deleteKgClassByIds" parameterType="String">
        delete from kg_class where class_id in 
        <foreach item="classId" collection="array" open="(" separator="," close=")">
            #{classId}
        </foreach>
    </delete>
    
</mapper>
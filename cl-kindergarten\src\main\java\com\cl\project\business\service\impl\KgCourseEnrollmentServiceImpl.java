package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgCourseEnrollmentMapper;
import com.cl.project.business.domain.KgCourseEnrollment;
import com.cl.project.business.service.IKgCourseEnrollmentService;

/**
 * 托管报名记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgCourseEnrollmentServiceImpl implements IKgCourseEnrollmentService 
{
    @Autowired
    private KgCourseEnrollmentMapper kgCourseEnrollmentMapper;

    /**
     * 查询托管报名记录
     * 
     * @param enrollmentId 托管报名记录ID
     * @return 托管报名记录
     */
    @Override
    public KgCourseEnrollment selectKgCourseEnrollmentById(Long enrollmentId)
    {
        return kgCourseEnrollmentMapper.selectKgCourseEnrollmentById(enrollmentId);
    }

    /**
     * 查询托管报名记录列表
     * 
     * @param kgCourseEnrollment 托管报名记录
     * @return 托管报名记录
     */
    @Override
    public List<KgCourseEnrollment> selectKgCourseEnrollmentList(KgCourseEnrollment kgCourseEnrollment)
    {
        return kgCourseEnrollmentMapper.selectKgCourseEnrollmentList(kgCourseEnrollment);
    }

    /**
     * 新增托管报名记录
     * 
     * @param kgCourseEnrollment 托管报名记录
     * @return 结果
     */
    @Override
    public int insertKgCourseEnrollment(KgCourseEnrollment kgCourseEnrollment)
    {
        kgCourseEnrollment.setCreateTime(DateUtils.getNowDate());
        return kgCourseEnrollmentMapper.insertKgCourseEnrollment(kgCourseEnrollment);
    }

    /**
     * 修改托管报名记录
     * 
     * @param kgCourseEnrollment 托管报名记录
     * @return 结果
     */
    @Override
    public int updateKgCourseEnrollment(KgCourseEnrollment kgCourseEnrollment)
    {
        kgCourseEnrollment.setUpdateTime(DateUtils.getNowDate());
        return kgCourseEnrollmentMapper.updateKgCourseEnrollment(kgCourseEnrollment);
    }

    /**
     * 批量删除托管报名记录
     * 
     * @param enrollmentIds 需要删除的托管报名记录ID
     * @return 结果
     */
    @Override
    public int deleteKgCourseEnrollmentByIds(Long[] enrollmentIds)
    {
        return kgCourseEnrollmentMapper.deleteKgCourseEnrollmentByIds(enrollmentIds);
    }

    /**
     * 删除托管报名记录信息
     * 
     * @param enrollmentId 托管报名记录ID
     * @return 结果
     */
    @Override
    public int deleteKgCourseEnrollmentById(Long enrollmentId)
    {
        return kgCourseEnrollmentMapper.deleteKgCourseEnrollmentById(enrollmentId);
    }
}

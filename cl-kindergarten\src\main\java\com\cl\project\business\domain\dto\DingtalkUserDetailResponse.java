package com.cl.project.business.domain.dto;

import java.util.List;

/**
 * 钉钉获取用户详情响应
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
public class DingtalkUserDetailResponse 
{
    private Integer errcode;
    private String errmsg;
    private String request_id;
    private Result result;
    
    public static class Result 
    {
        private String userid;
        private String unionid;
        private String name;
        private String mobile;
        private String job_number;
        private String title;
        private String email;
        private String org_email;
        private List<Long> dept_id_list;
        private String avatar;
        private String hired_date;
        private Boolean active;
        private Boolean admin;
        private Boolean boss;
        private String state_code;
        
        public String getUserid() 
        {
            return userid;
        }
        
        public void setUserid(String userid) 
        {
            this.userid = userid;
        }
        
        public String getUnionid() 
        {
            return unionid;
        }
        
        public void setUnionid(String unionid) 
        {
            this.unionid = unionid;
        }
        
        public String getName() 
        {
            return name;
        }
        
        public void setName(String name) 
        {
            this.name = name;
        }
        
        public String getMobile() 
        {
            return mobile;
        }
        
        public void setMobile(String mobile) 
        {
            this.mobile = mobile;
        }
        
        public String getJob_number() 
        {
            return job_number;
        }
        
        public void setJob_number(String job_number) 
        {
            this.job_number = job_number;
        }
        
        public String getTitle() 
        {
            return title;
        }
        
        public void setTitle(String title) 
        {
            this.title = title;
        }
        
        public String getEmail() 
        {
            return email;
        }
        
        public void setEmail(String email) 
        {
            this.email = email;
        }
        
        public String getOrg_email() 
        {
            return org_email;
        }
        
        public void setOrg_email(String org_email) 
        {
            this.org_email = org_email;
        }
        
        public List<Long> getDept_id_list() 
        {
            return dept_id_list;
        }
        
        public void setDept_id_list(List<Long> dept_id_list) 
        {
            this.dept_id_list = dept_id_list;
        }
        
        public String getAvatar() 
        {
            return avatar;
        }
        
        public void setAvatar(String avatar) 
        {
            this.avatar = avatar;
        }
        
        public String getHired_date() 
        {
            return hired_date;
        }
        
        public void setHired_date(String hired_date) 
        {
            this.hired_date = hired_date;
        }
        
        public Boolean getActive() 
        {
            return active;
        }
        
        public void setActive(Boolean active) 
        {
            this.active = active;
        }
        
        public Boolean getAdmin() 
        {
            return admin;
        }
        
        public void setAdmin(Boolean admin) 
        {
            this.admin = admin;
        }
        
        public Boolean getBoss() 
        {
            return boss;
        }
        
        public void setBoss(Boolean boss) 
        {
            this.boss = boss;
        }
        
        public String getState_code() 
        {
            return state_code;
        }
        
        public void setState_code(String state_code) 
        {
            this.state_code = state_code;
        }
    }
    
    public Integer getErrcode() 
    {
        return errcode;
    }
    
    public void setErrcode(Integer errcode) 
    {
        this.errcode = errcode;
    }
    
    public String getErrmsg() 
    {
        return errmsg;
    }
    
    public void setErrmsg(String errmsg) 
    {
        this.errmsg = errmsg;
    }
    
    public String getRequest_id() 
    {
        return request_id;
    }
    
    public void setRequest_id(String request_id) 
    {
        this.request_id = request_id;
    }
    
    public Result getResult() 
    {
        return result;
    }
    
    public void setResult(Result result) 
    {
        this.result = result;
    }
    
    public boolean isSuccess() 
    {
        return errcode != null && errcode == 0;
    }
}

<template>
	<view class="container">
		<!-- 顶部导航栏 -->
		<view class="header">
			<view class="header-content">
				<view class="nav-left" @click="goBack">
					<u-icon name="arrow-left" color="#ffffff" size="20"></u-icon>
				</view>
				<view class="header-title">
					<text class="title-text">学生管理</text>
				</view>
			</view>
		</view>

		<!-- 搜索栏 -->
		<view class="search-section">
			<view class="search-box">
				<u-icon name="search" color="#999" size="16"></u-icon>
				<u-input 
					v-model="searchKeyword" 
					placeholder="请输入学生姓名或学号"
					:border="false"
					customStyle="flex: 1; margin-left: 12rpx;"
					@input="handleSearch"
					@clear="clearSearch"
					clearable
				/>
				<!-- 搜索状态提示 -->
				<view v-if="searchTimer" class="search-loading">
					<view class="loading-spinner"></view>
				</view>
			</view>
			<!-- 搜索结果提示 -->
			<view v-if="searchKeyword.trim() && !loading" class="search-result-tip">
				<text class="result-text">找到 {{ studentList.length }} 个相关学生</text>
			</view>
		</view>

		<!-- 功能按钮区 -->
		<view class="function-section">
			<view class="function-row">
				<view class="function-btn primary" @click="addStudent">
					<view class="btn-icon">
						<u-icon name="plus" color="#ffffff" size="20"></u-icon>
					</view>
					<text class="btn-text">添加学生</text>
				</view>
				<view class="function-btn secondary" @click="syncStudentData">
					<view class="btn-icon">
						<u-icon name="reload" color="#667eea" size="20"></u-icon>
					</view>
					<text class="btn-text">同步数据</text>
				</view>
			</view>
		</view>

		<!-- 学生列表 -->
		<view class="student-list">
			<!-- 加载状态 -->
			<view v-if="loading && studentList.length === 0" class="loading-container">
				<view class="loading-spinner large"></view>
				<text class="loading-text">加载中...</text>
			</view>

			<!-- 学生列表项 -->
			<view v-for="student in filteredStudentList" :key="student.id" class="student-item-card">
				<view class="student-card-content">
					<view class="student-avatar">
						<image v-if="student.avatar" :src="student.avatar" class="avatar-img" mode="aspectFill"></image>
						<text v-else class="avatar-text">{{ formatGender(student.gender) === '男' ? '👦' : '👧' }}</text>
					</view>
					<view class="student-details">
						<view class="student-header">
							<text class="student-name">{{ student.name }}</text>
							<view class="status-badge active">
								{{ formatStatus(student.status) }}
							</view>
						</view>
						<view class="student-info-row">
							<text class="info-item">
								<text class="info-label">学号:</text>
								<text class="info-value">{{ student.studentNo || '未分配' }}</text>
							</text>
							<text class="info-item">
								<text class="info-label">班级:</text>
								<text class="info-value class-tag">{{ student.class }}</text>
							</text>
						</view>
						<view class="student-info-row">
							<text class="info-item">
								<text class="info-label">家长:</text>
								<text class="info-value">{{ student.parentName }}</text>
							</text>
							<text class="info-item">
								<text class="info-label">电话:</text>
								<text class="info-value phone-number" @tap="makePhoneCall(student.parentPhone)">{{ student.parentPhone }}</text>
							</text>
						</view>
						<view class="balance-section">
							<text class="balance-label">预存款余额:</text>
							<text class="balance-amount" :class="{ 'low-balance': student.balance < 100 }">
								¥{{ student.balance.toFixed(2) }}
							</text>
						</view>
					</view>
				</view>
				<view class="student-actions">
					<button class="action-btn recharge" @tap="rechargeBalance(student)">
						<u-icon name="rmb-circle" size="16" color="#ffffff"></u-icon>
						<text class="btn-text">充值</text>
					</button>
					<button class="action-btn edit" @tap="editStudent(student)">
						<u-icon name="edit-pen" size="16" color="#ffffff"></u-icon>
						<text class="btn-text">编辑</text>
					</button>
					<button class="action-btn delete" @tap="deleteStudent(student)">
						<u-icon name="trash" size="16" color="#ffffff"></u-icon>
						<text class="btn-text">删除</text>
					</button>
				</view>
			</view>

			<!-- 空状态 -->
			<view v-if="!loading && studentList.length === 0" class="empty-container">
				<text class="empty-icon">👶</text>
				<text class="empty-text">暂无学生数据</text>
				<text class="empty-tip">点击上方"添加学生"按钮添加新学生</text>
			</view>

			<!-- 加载更多 -->
			<view v-if="loadingMore" class="loading-more">
				<view class="loading-spinner small"></view>
				<text class="loading-more-text">加载更多...</text>
			</view>

			<!-- 没有更多数据 -->
			<view v-if="!hasMore && studentList.length > 0" class="no-more">
				<text class="no-more-text">没有更多数据了，共 {{ total }} 条</text>
			</view>
		</view>
	</view>
</template>

<script>
import { toast, useRouter } from '@/utils/utils.js'
import { getStudentList, getStudentDetail, deleteStudent, syncStudentData } from '@/api/api.js'

export default {
	data() {
		return {
			searchKeyword: '',
			studentList: [],
			loading: false,
			loadingMore: false,
			hasMore: true,
			pageNum: 1,
			pageSize: 10,
			total: 0,
			searchTimer: null
		}
	},
	onLoad() {
		this.loadStudentList()
	},
	
	// 页面显示时刷新数据
	onShow() {
		this.refreshList()
	},

	// 触底加载更多
	onReachBottom() {
		if (!this.loadingMore && this.hasMore) {
			this.loadMoreData()
		}
	},

	// 下拉刷新
	onPullDownRefresh() {
		this.refreshList().finally(() => {
			uni.stopPullDownRefresh()
		})
	},

	// 页面卸载时清理定时器
	onUnload() {
		if (this.searchTimer) {
			clearTimeout(this.searchTimer)
			this.searchTimer = null
		}
	},

	computed: {
		filteredStudentList() {
			return this.studentList
		}
	},
	methods: {
		goBack() {
			uni.navigateBack()
		},
		
		// 添加学生
		addStudent() {
			uni.navigateTo({
				url: '/pages/admin/student/add'
			})
		},

		// 同步学生数据
		syncStudentData() {
			uni.showModal({
				title: '同步数据',
				content: '确定要从系统同步最新的学生数据吗？',
				success: (res) => {
					if (res.confirm) {
						this.performSync()
					}
				}
			})
		},

		// 执行同步操作
		async performSync() {
			uni.showLoading({
				title: '同步中...'
			})

			try {
				const res = await syncStudentData()
				uni.hideLoading()

				if (res.code === 200) {
					toast('同步成功！已更新学生数据')
					// 重新加载学生列表
					this.refreshList()
				} else {
					toast(res.msg || '同步失败')
				}
			} catch (error) {
				uni.hideLoading()
				toast('同步失败，请稍后重试')
				console.error('同步学生数据失败:', error)
			}
		},

		// 加载学生列表
		async loadStudentList(isRefresh = false) {
			if (this.loading) return

			this.loading = true
			try {
				const params = {
					status: '0,2', // 显示在园(0)和请假(2)状态，不显示退园(1)状态
					pageNum: this.pageNum,
					pageSize: this.pageSize
				}

				// 添加搜索条件
				if (this.searchKeyword.trim()) {
					params.studentName = this.searchKeyword.trim()
				}

				const res = await getStudentList(params)
				if (res.code === 200) {
					const newStudents = res.rows ? res.rows.map(student => ({
						id: student.studentId,
						name: student.studentName,
						studentNo: student.studentCode || student.studentNumber || '',
						class: student.className || '未分班',
						parentName: student.parentName || '未填写',
						parentPhone: student.parentPhone || '未填写',
						balance: 0.00, // 后续可接入预存款接口
						gender: student.gender,
						birthDate: student.birthDate,
						phone: student.phone,
						address: student.address,
						enrollmentDate: student.enrollmentDate,
						status: student.status,
						avatar: student.avatar
					})) : []

					if (isRefresh || this.pageNum === 1) {
						this.studentList = newStudents
					} else {
						this.studentList = [...this.studentList, ...newStudents]
					}

					this.total = res.total || 0
					this.hasMore = this.studentList.length < this.total

					console.log('成功加载学生列表:', newStudents.length, '个学生，总计:', this.studentList.length)
				} else {
					throw new Error(res.msg || '获取学生列表失败')
				}
			} catch (error) {
				console.error('加载学生列表失败:', error)
				// 只有在列表为空时才使用模拟数据，避免覆盖已有数据
				if (this.studentList.length === 0) {
					this.studentList = [
						{
							id: 1,
							name: '张小明',
							studentNo: '2024001',
							class: '小班',
							parentName: '张爸爸',
							parentPhone: '13800138001',
							balance: 1200.00
						},
						{
							id: 2,
							name: '李小红',
							studentNo: '2024002',
							class: '小班',
							parentName: '李妈妈',
							parentPhone: '13800138002',
							balance: 800.00
						}
					]
					this.hasMore = false
					toast('网络异常，已加载示例数据')
				} else {
					toast('刷新失败，请稍后重试')
				}
			} finally {
				this.loading = false
			}
		},
		// 加载更多数据
		async loadMoreData() {
			if (this.loadingMore || !this.hasMore) return

			this.loadingMore = true
			this.pageNum += 1

			try {
				await this.loadStudentList(false)
			} catch (error) {
				console.error('加载更多数据失败:', error)
				this.pageNum -= 1 // 回退页码
				toast('加载更多失败')
			} finally {
				this.loadingMore = false
			}
		},

		// 刷新列表
		async refreshList() {
			this.pageNum = 1
			this.hasMore = true
			await this.loadStudentList(true)
		},

		// 搜索输入监听（防抖处理）
		handleSearch() {
			// 清除之前的定时器
			if (this.searchTimer) {
				clearTimeout(this.searchTimer)
			}

			// 如果搜索关键词为空，立即清空列表并重新加载
			if (!this.searchKeyword.trim()) {
				this.searchTimer = null
				this.pageNum = 1
				this.hasMore = true
				this.studentList = []
				this.loadStudentList(true)
				return
			}

			// 设置新的定时器，500ms后执行搜索
			this.searchTimer = setTimeout(() => {
				this.searchStudent()
				this.searchTimer = null
			}, 500)
		},

		// 执行搜索
		searchStudent() {
			this.pageNum = 1
			this.hasMore = true
			this.studentList = []
			this.loadStudentList(true)
		},
		
		rechargeBalance(student) {
			uni.showModal({
				title: '充值预存款',
				content: `为 ${student.name} 充值预存款`,
				confirmText: '充值',
				success: (res) => {
					if (res.confirm) {
						toast(`${student.name} 充值功能开发中...`)
						// 这里可以跳转到充值页面或显示充值弹窗
					}
				}
			})
		},
		
		// 编辑学生
		editStudent(student) {
			uni.navigateTo({
				url: `/pages/admin/student/edit?id=${student.id}`,
				events: {
					// 监听编辑页面返回的刷新事件
					refreshStudentList: () => {
						this.refreshList()
					}
				}
			})
		},

		// 删除学生
		deleteStudent(student) {
			uni.showModal({
				title: '确认删除',
				content: `确定要删除学生 ${student.name} 吗？`,
				success: async (res) => {
					if (res.confirm) {
						try {
							uni.showLoading({ title: '删除中...' })
							const result = await deleteStudent(student.id)
							uni.hideLoading()

							if (result.code === 200) {
								toast('删除成功')
								this.refreshList()
							} else {
								toast(result.msg || '删除失败')
							}
						} catch (error) {
							uni.hideLoading()
							toast('删除失败，请稍后重试')
							console.error('删除学生失败:', error)
						}
					}
				}
			})
		},

		// 清空搜索
		clearSearch() {
			// 清除搜索定时器
			if (this.searchTimer) {
				clearTimeout(this.searchTimer)
				this.searchTimer = null
			}

			this.searchKeyword = ''
			this.pageNum = 1
			this.hasMore = true
			this.studentList = []
			this.loadStudentList(true)
		},

		// 格式化性别
		formatGender(gender) {
			return gender === '0' ? '男' : '女'
		},

		// 格式化状态
		formatStatus(status) {
			switch(status) {
				case '0': return '在园'
				case '1': return '退园'
				case '2': return '请假'
				default: return '未知'
			}
		},

		// 拨打电话
		makePhoneCall(phoneNumber) {
			if (!phoneNumber || phoneNumber === '未填写') {
				toast('电话号码为空')
				return
			}
			
			// 清理电话号码格式
			const cleanPhone = phoneNumber.replace(/\D/g, '')
			if (cleanPhone.length < 11) {
				toast('电话号码格式不正确')
				return
			}

			uni.showModal({
				title: '拨打电话',
				content: `确定要拨打电话 ${phoneNumber} 吗？`,
				success: (res) => {
					if (res.confirm) {
						uni.makePhoneCall({
							phoneNumber: cleanPhone,
							success: () => {
								console.log('拨打电话成功')
							},
							fail: (err) => {
								console.error('拨打电话失败:', err)
								toast('拨打电话失败')
							}
						})
					}
				}
			})
		}
	}
}
</script>

<style lang="scss" scoped>
.container {
	min-height: 100vh;
	background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
}

/* 顶部导航栏 */
.header {
	background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
	padding: 0 30rpx;
	padding-top: var(--status-bar-height, 44rpx);
	box-shadow: 0 8rpx 32rpx rgba(102, 126, 234, 0.3);
}

.header-content {
	height: 120rpx;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.nav-left {
	width: 80rpx;
	height: 80rpx;
	border-radius: 50%;
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
	transition: all 0.3s ease;

	&:active {
		transform: scale(0.9);
		background: rgba(255, 255, 255, 0.3);
	}
}

.header-title {
	flex: 1;
	text-align: center;
}

.title-text {
	font-size: 36rpx;
	font-weight: 600;
	color: #ffffff;
	text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1);
}

/* 功能按钮区 */
.function-section {
	padding: 30rpx;
}

.function-row {
	display: flex;
	gap: 20rpx;
}

.function-btn {
	flex: 1;
	height: 100rpx;
	border-radius: 24rpx;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 16rpx;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);

	&:active {
		transform: translateY(2rpx) scale(0.98);
	}

	&.primary {
		background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
		box-shadow: 0 16rpx 40rpx rgba(40, 167, 69, 0.4);

		&:active {
			box-shadow: 0 8rpx 20rpx rgba(40, 167, 69, 0.6);
		}

		.btn-text {
			color: #ffffff;
			font-weight: 600;
		}
	}

	&.secondary {
		background: #ffffff;
		border: 2rpx solid #667eea;
		box-shadow: 0 8rpx 24rpx rgba(102, 126, 234, 0.15);

		&:active {
			background: rgba(102, 126, 234, 0.05);
			box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.25);
		}

		.btn-text {
			color: #667eea;
			font-weight: 500;
		}
	}
}

.btn-icon {
	width: 40rpx;
	height: 40rpx;
	display: flex;
	align-items: center;
	justify-content: center;
}

.btn-text {
	font-size: 28rpx;
	text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.1);
}

.search-section {
	padding: 30rpx;
	padding-bottom: 0;
}

/* 搜索框 */
.search-box {
	background: #ffffff;
	border-radius: 24rpx;
	padding: 20rpx 24rpx;
	display: flex;
	align-items: center;
	box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.08);
	position: relative;
}

.search-loading {
	margin-left: 12rpx;
	display: flex;
	align-items: center;
}

/* 自定义加载动画 */
.loading-spinner {
	width: 16rpx;
	height: 16rpx;
	border: 2rpx solid #f3f3f3;
	border-top: 2rpx solid #667eea;
	border-radius: 50%;
	animation: spin 1s linear infinite;

	&.large {
		width: 40rpx;
		height: 40rpx;
		border-width: 4rpx;
	}
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

.search-result-tip {
	margin-top: 12rpx;
	padding: 8rpx 16rpx;
	background: rgba(102, 126, 234, 0.1);
	border-radius: 12rpx;
	border-left: 4rpx solid #667eea;
}

.result-text {
	font-size: 24rpx;
	color: #667eea;
}

.student-list {
	padding: 12rpx 16rpx;
	padding-bottom: 40rpx;
}

/* 学生卡片 */
.student-item-card {
	background: #ffffff;
	border-radius: 20rpx;
	margin-bottom: 16rpx;
	overflow: hidden;
	box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	border: 1rpx solid #f0f0f0;

	&:active {
		transform: scale(0.99);
		box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.12);
	}
}

.student-card-content {
	padding: 24rpx;
	display: flex;
	align-items: flex-start;
	gap: 20rpx;
}

.student-avatar {
	width: 88rpx;
	height: 88rpx;
	border-radius: 50%;
	background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
	display: flex;
	align-items: center;
	justify-content: center;
	flex-shrink: 0;
	overflow: hidden;
	box-shadow: 0 4rpx 12rpx rgba(25, 118, 210, 0.15);
}

.avatar-img {
	width: 100%;
	height: 100%;
	border-radius: 50%;
}

.avatar-text {
	font-size: 36rpx;
}

.student-details {
	flex: 1;
	display: flex;
	flex-direction: column;
	gap: 12rpx;
}

.student-header {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 16rpx;
	margin-bottom: 4rpx;
}

.student-name {
	font-size: 34rpx;
	font-weight: 700;
	color: #1a1a1a;
	line-height: 1.2;
	flex: 1;
}

.status-badge {
	padding: 6rpx 16rpx;
	border-radius: 20rpx;
	font-size: 20rpx;
	font-weight: 500;
	background: #f5f5f5;
	color: #999;
	flexShrink: 0;

	&.active {
		background: linear-gradient(135deg, #e8f5e8 0%, #c8e6c9 100%);
		color: #2e7d32;
		border: 1rpx solid #a5d6a7;
	}
}

.student-info-row {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 24rpx;
	flex-wrap: wrap;
}

.info-item {
	display: flex;
	align-items: center;
	gap: 8rpx;
	flex: 1;
	min-width: 0;
}

.info-label {
	font-size: 22rpx;
	color: #8a8a8a;
	font-weight: 500;
	flexShrink: 0;
}

.info-value {
	font-size: 24rpx;
	color: #333333;
	font-weight: 500;
	line-height: 1.2;

	&.class-tag {
		background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
		color: #ef6c00;
		padding: 4rpx 12rpx;
		border-radius: 12rpx;
		font-size: 20rpx;
		border: 1rpx solid #ffcc02;
	}

	&.phone-number {
		color: #1976d2;
		font-family: monospace;
		position: relative;
		cursor: pointer;
		
		&:active {
			opacity: 0.7;
		}
		
		&::after {
			content: '📞';
			margin-left: 8rpx;
			font-size: 18rpx;
		}
	}
}

.balance-section {
	display: flex;
	align-items: center;
	justify-content: space-between;
	gap: 12rpx;
	margin-top: 8rpx;
	padding: 12rpx 16rpx;
	background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
	border-radius: 12rpx;
	border: 1rpx solid #e0e0e0;
}

.balance-label {
	font-size: 24rpx;
	color: #666;
	font-weight: 500;
}

.balance-amount {
	font-size: 28rpx;
	font-weight: 700;
	color: #4CAF50;
	line-height: 1;
	
	&.low-balance {
		color: #f44336;
	}
}

.student-actions {
	padding: 16rpx 24rpx;
	background: linear-gradient(135deg, #fafafa 0%, #f5f5f5 100%);
	border-top: 1rpx solid #f0f0f0;
	display: flex;
	gap: 12rpx;
	justify-content: center;
}

.action-btn {
	flex: 1;
	height: 64rpx;
	border: none;
	border-radius: 16rpx;
	font-size: 24rpx;
	font-weight: 600;
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 8rpx;
	transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
	box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
	max-width: 120rpx;
	
	&:active {
		transform: scale(0.95);
	}
	
	&.recharge {
		background: linear-gradient(135deg, #ff9800 0%, #f57c00 100%);
		color: #ffffff;
		box-shadow: 0 4rpx 12rpx rgba(255, 152, 0, 0.3);
		
		&:active {
			box-shadow: 0 2rpx 6rpx rgba(255, 152, 0, 0.4);
		}
	}
	
	&.edit {
		background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
		color: #ffffff;
		box-shadow: 0 4rpx 12rpx rgba(33, 150, 243, 0.3);
		
		&:active {
			box-shadow: 0 2rpx 6rpx rgba(33, 150, 243, 0.4);
		}
	}

	&.delete {
		background: linear-gradient(135deg, #f44336 0%, #d32f2f 100%);
		color: #ffffff;
		box-shadow: 0 4rpx 12rpx rgba(244, 67, 54, 0.3);
		
		&:active {
			box-shadow: 0 2rpx 6rpx rgba(244, 67, 54, 0.4);
		}
	}
}

.btn-text {
	font-size: 22rpx;
	font-weight: 600;
	line-height: 1;
	margin-left: 4rpx;
}

/* 加载状态 */
.loading-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 80rpx 0;
	gap: 20rpx;
}

.loading-text {
	font-size: 28rpx;
	color: #667eea;
}

.loading-spinner {
	border-radius: 50%;
	animation: spin 1s linear infinite;

	&.large {
		width: 60rpx;
		height: 60rpx;
		border: 4rpx solid #f3f3f3;
		border-top: 4rpx solid #667eea;
	}

	&.small {
		width: 32rpx;
		height: 32rpx;
		border: 3rpx solid #f3f3f3;
		border-top: 3rpx solid #667eea;
	}
}

@keyframes spin {
	0% { transform: rotate(0deg); }
	100% { transform: rotate(360deg); }
}

/* 空状态 */
.empty-container {
	display: flex;
	flex-direction: column;
	align-items: center;
	justify-content: center;
	padding: 120rpx 40rpx;
	gap: 20rpx;
}

.empty-icon {
	font-size: 80rpx;
	opacity: 0.5;
}

.empty-text {
	font-size: 32rpx;
	color: #999;
	font-weight: 500;
}

.empty-tip {
	font-size: 24rpx;
	color: #ccc;
	text-align: center;
	line-height: 1.5;
}

/* 加载更多 */
.loading-more {
	display: flex;
	align-items: center;
	justify-content: center;
	gap: 12rpx;
	padding: 30rpx 0;
}

.loading-more-text {
	font-size: 24rpx;
	color: #667eea;
}

/* 没有更多数据 */
.no-more {
	display: flex;
	align-items: center;
	justify-content: center;
	padding: 30rpx 0;
}

.no-more-text {
	font-size: 24rpx;
	color: #666666;
}
</style>

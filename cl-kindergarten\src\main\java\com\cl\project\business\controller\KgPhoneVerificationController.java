package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgPhoneVerification;
import com.cl.project.business.service.IKgPhoneVerificationService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 手机号验证码Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/verification")
public class KgPhoneVerificationController extends BaseController
{
    @Autowired
    private IKgPhoneVerificationService kgPhoneVerificationService;

    /**
     * 查询手机号验证码列表
     */
    @SaCheckPermission("business:verification:list")
    @GetMapping("/list")
    public TableDataInfo list(KgPhoneVerification kgPhoneVerification)
    {
        startPage();
        List<KgPhoneVerification> list = kgPhoneVerificationService.selectKgPhoneVerificationList(kgPhoneVerification);
        return getDataTable(list);
    }

    /**
     * 导出手机号验证码列表
     */
    @SaCheckPermission("business:verification:export")
    @Log(title = "手机号验证码", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgPhoneVerification kgPhoneVerification)
    {
        List<KgPhoneVerification> list = kgPhoneVerificationService.selectKgPhoneVerificationList(kgPhoneVerification);
        ExcelUtil<KgPhoneVerification> util = new ExcelUtil<KgPhoneVerification>(KgPhoneVerification.class);
        return util.exportExcel(list, "verification");
    }

    /**
     * 获取手机号验证码详细信息
     */
    @SaCheckPermission("business:verification:query")
    @GetMapping(value = "/{verificationId}")
    public AjaxResult getInfo(@PathVariable("verificationId") Long verificationId)
    {
        return AjaxResult.success(kgPhoneVerificationService.selectKgPhoneVerificationById(verificationId));
    }

    /**
     * 新增手机号验证码
     */
    @SaCheckPermission("business:verification:add")
    @Log(title = "手机号验证码", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgPhoneVerification kgPhoneVerification)
    {
        return toAjax(kgPhoneVerificationService.insertKgPhoneVerification(kgPhoneVerification));
    }

    /**
     * 修改手机号验证码
     */
    @SaCheckPermission("business:verification:edit")
    @Log(title = "手机号验证码", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgPhoneVerification kgPhoneVerification)
    {
        return toAjax(kgPhoneVerificationService.updateKgPhoneVerification(kgPhoneVerification));
    }

    /**
     * 删除手机号验证码
     */
    @SaCheckPermission("business:verification:remove")
    @Log(title = "手机号验证码", businessType = BusinessType.DELETE)
	@DeleteMapping("/{verificationIds}")
    public AjaxResult remove(@PathVariable Long[] verificationIds)
    {
        return toAjax(kgPhoneVerificationService.deleteKgPhoneVerificationByIds(verificationIds));
    }
}

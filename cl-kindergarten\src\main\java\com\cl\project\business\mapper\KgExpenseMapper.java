package com.cl.project.business.mapper;

import java.util.List;
import com.cl.project.business.domain.KgExpense;

/**
 * 支出记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface KgExpenseMapper 
{
    /**
     * 查询支出记录
     * 
     * @param expenseId 支出记录ID
     * @return 支出记录
     */
    public KgExpense selectKgExpenseById(Long expenseId);

    /**
     * 查询支出记录列表
     * 
     * @param kgExpense 支出记录
     * @return 支出记录集合
     */
    public List<KgExpense> selectKgExpenseList(KgExpense kgExpense);

    /**
     * 新增支出记录
     * 
     * @param kgExpense 支出记录
     * @return 结果
     */
    public int insertKgExpense(KgExpense kgExpense);

    /**
     * 修改支出记录
     * 
     * @param kgExpense 支出记录
     * @return 结果
     */
    public int updateKgExpense(KgExpense kgExpense);

    /**
     * 删除支出记录
     * 
     * @param expenseId 支出记录ID
     * @return 结果
     */
    public int deleteKgExpenseById(Long expenseId);

    /**
     * 批量删除支出记录
     * 
     * @param expenseIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteKgExpenseByIds(Long[] expenseIds);
}

package com.cl.project.business.service;

import java.util.List;
import com.cl.project.business.domain.KgStockRecord;

/**
 * 库存变动记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IKgStockRecordService 
{
    /**
     * 查询库存变动记录
     * 
     * @param recordId 库存变动记录ID
     * @return 库存变动记录
     */
    public KgStockRecord selectKgStockRecordById(Long recordId);

    /**
     * 查询库存变动记录列表
     * 
     * @param kgStockRecord 库存变动记录
     * @return 库存变动记录集合
     */
    public List<KgStockRecord> selectKgStockRecordList(KgStockRecord kgStockRecord);

    /**
     * 新增库存变动记录
     * 
     * @param kgStockRecord 库存变动记录
     * @return 结果
     */
    public int insertKgStockRecord(KgStockRecord kgStockRecord);

    /**
     * 修改库存变动记录
     * 
     * @param kgStockRecord 库存变动记录
     * @return 结果
     */
    public int updateKgStockRecord(KgStockRecord kgStockRecord);

    /**
     * 批量删除库存变动记录
     * 
     * @param recordIds 需要删除的库存变动记录ID
     * @return 结果
     */
    public int deleteKgStockRecordByIds(Long[] recordIds);

    /**
     * 删除库存变动记录信息
     * 
     * @param recordId 库存变动记录ID
     * @return 结果
     */
    public int deleteKgStockRecordById(Long recordId);
}

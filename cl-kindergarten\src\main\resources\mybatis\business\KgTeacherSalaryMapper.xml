<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgTeacherSalaryMapper">
    
    <resultMap type="KgTeacherSalary" id="KgTeacherSalaryResult">
        <result property="salaryId"    column="salary_id"    />
        <result property="teacherId"    column="teacher_id"    />
        <result property="salaryYear"    column="salary_year"    />
        <result property="salaryMonth"    column="salary_month"    />
        <result property="baseSalary"    column="base_salary"    />
        <result property="attendanceDays"    column="attendance_days"    />
        <result property="totalWorkDays"    column="total_work_days"    />
        <result property="attendanceBonus"    column="attendance_bonus"    />
        <result property="courseBonus"    column="course_bonus"    />
        <result property="enrollmentBonus"    column="enrollment_bonus"    />
        <result property="attendanceRateBonus"    column="attendance_rate_bonus"    />
        <result property="newStudentBonus"    column="new_student_bonus"    />
        <result property="withdrawalPenalty"    column="withdrawal_penalty"    />
        <result property="socialInsurance"    column="social_insurance"    />
        <result property="performanceScore"    column="performance_score"    />
        <result property="otherBonus"    column="other_bonus"    />
        <result property="otherDeduction"    column="other_deduction"    />
        <result property="grossSalary"    column="gross_salary"    />
        <result property="netSalary"    column="net_salary"    />
        <result property="salaryStatus"    column="salary_status"    />
        <result property="confirmedBy"    column="confirmed_by"    />
        <result property="confirmedTime"    column="confirmed_time"    />
        <result property="paidTime"    column="paid_time"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgTeacherSalaryVo">
        select salary_id, teacher_id, salary_year, salary_month, base_salary, attendance_days, total_work_days, attendance_bonus, course_bonus, enrollment_bonus, attendance_rate_bonus, new_student_bonus, withdrawal_penalty, social_insurance, performance_score, other_bonus, other_deduction, gross_salary, net_salary, salary_status, confirmed_by, confirmed_time, paid_time, com_id, create_by, create_time, update_by, update_time, remark from kg_teacher_salary
    </sql>

    <select id="selectKgTeacherSalaryList" parameterType="KgTeacherSalary" resultMap="KgTeacherSalaryResult">
        <include refid="selectKgTeacherSalaryVo"/>
        <where>  
            <if test="teacherId != null "> and teacher_id = #{teacherId}</if>
            <if test="salaryYear != null "> and salary_year = #{salaryYear}</if>
            <if test="salaryMonth != null "> and salary_month = #{salaryMonth}</if>
            <if test="baseSalary != null "> and base_salary = #{baseSalary}</if>
            <if test="attendanceDays != null "> and attendance_days = #{attendanceDays}</if>
            <if test="totalWorkDays != null "> and total_work_days = #{totalWorkDays}</if>
            <if test="attendanceBonus != null "> and attendance_bonus = #{attendanceBonus}</if>
            <if test="courseBonus != null "> and course_bonus = #{courseBonus}</if>
            <if test="enrollmentBonus != null "> and enrollment_bonus = #{enrollmentBonus}</if>
            <if test="attendanceRateBonus != null "> and attendance_rate_bonus = #{attendanceRateBonus}</if>
            <if test="newStudentBonus != null "> and new_student_bonus = #{newStudentBonus}</if>
            <if test="withdrawalPenalty != null "> and withdrawal_penalty = #{withdrawalPenalty}</if>
            <if test="socialInsurance != null "> and social_insurance = #{socialInsurance}</if>
            <if test="performanceScore != null "> and performance_score = #{performanceScore}</if>
            <if test="otherBonus != null "> and other_bonus = #{otherBonus}</if>
            <if test="otherDeduction != null "> and other_deduction = #{otherDeduction}</if>
            <if test="grossSalary != null "> and gross_salary = #{grossSalary}</if>
            <if test="netSalary != null "> and net_salary = #{netSalary}</if>
            <if test="salaryStatus != null  and salaryStatus != ''"> and salary_status = #{salaryStatus}</if>
            <if test="confirmedBy != null "> and confirmed_by = #{confirmedBy}</if>
            <if test="confirmedTime != null "> and confirmed_time = #{confirmedTime}</if>
            <if test="paidTime != null "> and paid_time = #{paidTime}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgTeacherSalaryById" parameterType="Long" resultMap="KgTeacherSalaryResult">
        <include refid="selectKgTeacherSalaryVo"/>
        where salary_id = #{salaryId}
    </select>
        
    <insert id="insertKgTeacherSalary" parameterType="KgTeacherSalary" useGeneratedKeys="true" keyProperty="salaryId">
        insert into kg_teacher_salary
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">teacher_id,</if>
            <if test="salaryYear != null">salary_year,</if>
            <if test="salaryMonth != null">salary_month,</if>
            <if test="baseSalary != null">base_salary,</if>
            <if test="attendanceDays != null">attendance_days,</if>
            <if test="totalWorkDays != null">total_work_days,</if>
            <if test="attendanceBonus != null">attendance_bonus,</if>
            <if test="courseBonus != null">course_bonus,</if>
            <if test="enrollmentBonus != null">enrollment_bonus,</if>
            <if test="attendanceRateBonus != null">attendance_rate_bonus,</if>
            <if test="newStudentBonus != null">new_student_bonus,</if>
            <if test="withdrawalPenalty != null">withdrawal_penalty,</if>
            <if test="socialInsurance != null">social_insurance,</if>
            <if test="performanceScore != null">performance_score,</if>
            <if test="otherBonus != null">other_bonus,</if>
            <if test="otherDeduction != null">other_deduction,</if>
            <if test="grossSalary != null">gross_salary,</if>
            <if test="netSalary != null">net_salary,</if>
            <if test="salaryStatus != null">salary_status,</if>
            <if test="confirmedBy != null">confirmed_by,</if>
            <if test="confirmedTime != null">confirmed_time,</if>
            <if test="paidTime != null">paid_time,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="teacherId != null">#{teacherId},</if>
            <if test="salaryYear != null">#{salaryYear},</if>
            <if test="salaryMonth != null">#{salaryMonth},</if>
            <if test="baseSalary != null">#{baseSalary},</if>
            <if test="attendanceDays != null">#{attendanceDays},</if>
            <if test="totalWorkDays != null">#{totalWorkDays},</if>
            <if test="attendanceBonus != null">#{attendanceBonus},</if>
            <if test="courseBonus != null">#{courseBonus},</if>
            <if test="enrollmentBonus != null">#{enrollmentBonus},</if>
            <if test="attendanceRateBonus != null">#{attendanceRateBonus},</if>
            <if test="newStudentBonus != null">#{newStudentBonus},</if>
            <if test="withdrawalPenalty != null">#{withdrawalPenalty},</if>
            <if test="socialInsurance != null">#{socialInsurance},</if>
            <if test="performanceScore != null">#{performanceScore},</if>
            <if test="otherBonus != null">#{otherBonus},</if>
            <if test="otherDeduction != null">#{otherDeduction},</if>
            <if test="grossSalary != null">#{grossSalary},</if>
            <if test="netSalary != null">#{netSalary},</if>
            <if test="salaryStatus != null">#{salaryStatus},</if>
            <if test="confirmedBy != null">#{confirmedBy},</if>
            <if test="confirmedTime != null">#{confirmedTime},</if>
            <if test="paidTime != null">#{paidTime},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgTeacherSalary" parameterType="KgTeacherSalary">
        update kg_teacher_salary
        <trim prefix="SET" suffixOverrides=",">
            <if test="teacherId != null">teacher_id = #{teacherId},</if>
            <if test="salaryYear != null">salary_year = #{salaryYear},</if>
            <if test="salaryMonth != null">salary_month = #{salaryMonth},</if>
            <if test="baseSalary != null">base_salary = #{baseSalary},</if>
            <if test="attendanceDays != null">attendance_days = #{attendanceDays},</if>
            <if test="totalWorkDays != null">total_work_days = #{totalWorkDays},</if>
            <if test="attendanceBonus != null">attendance_bonus = #{attendanceBonus},</if>
            <if test="courseBonus != null">course_bonus = #{courseBonus},</if>
            <if test="enrollmentBonus != null">enrollment_bonus = #{enrollmentBonus},</if>
            <if test="attendanceRateBonus != null">attendance_rate_bonus = #{attendanceRateBonus},</if>
            <if test="newStudentBonus != null">new_student_bonus = #{newStudentBonus},</if>
            <if test="withdrawalPenalty != null">withdrawal_penalty = #{withdrawalPenalty},</if>
            <if test="socialInsurance != null">social_insurance = #{socialInsurance},</if>
            <if test="performanceScore != null">performance_score = #{performanceScore},</if>
            <if test="otherBonus != null">other_bonus = #{otherBonus},</if>
            <if test="otherDeduction != null">other_deduction = #{otherDeduction},</if>
            <if test="grossSalary != null">gross_salary = #{grossSalary},</if>
            <if test="netSalary != null">net_salary = #{netSalary},</if>
            <if test="salaryStatus != null">salary_status = #{salaryStatus},</if>
            <if test="confirmedBy != null">confirmed_by = #{confirmedBy},</if>
            <if test="confirmedTime != null">confirmed_time = #{confirmedTime},</if>
            <if test="paidTime != null">paid_time = #{paidTime},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where salary_id = #{salaryId}
    </update>

    <delete id="deleteKgTeacherSalaryById" parameterType="Long">
        delete from kg_teacher_salary where salary_id = #{salaryId}
    </delete>

    <delete id="deleteKgTeacherSalaryByIds" parameterType="String">
        delete from kg_teacher_salary where salary_id in 
        <foreach item="salaryId" collection="array" open="(" separator="," close=")">
            #{salaryId}
        </foreach>
    </delete>
    
</mapper>
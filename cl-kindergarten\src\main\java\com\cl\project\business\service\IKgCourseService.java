package com.cl.project.business.service;

import java.util.List;
import com.cl.project.business.domain.KgCourse;

/**
 * 托管课程Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IKgCourseService 
{
    /**
     * 查询托管课程
     * 
     * @param courseId 托管课程ID
     * @return 托管课程
     */
    public KgCourse selectKgCourseById(Long courseId);

    /**
     * 查询托管课程列表
     * 
     * @param kgCourse 托管课程
     * @return 托管课程集合
     */
    public List<KgCourse> selectKgCourseList(KgCourse kgCourse);

    /**
     * 新增托管课程
     * 
     * @param kgCourse 托管课程
     * @return 结果
     */
    public int insertKgCourse(KgCourse kgCourse);

    /**
     * 修改托管课程
     * 
     * @param kgCourse 托管课程
     * @return 结果
     */
    public int updateKgCourse(KgCourse kgCourse);

    /**
     * 批量删除托管课程
     * 
     * @param courseIds 需要删除的托管课程ID
     * @return 结果
     */
    public int deleteKgCourseByIds(Long[] courseIds);

    /**
     * 删除托管课程信息
     * 
     * @param courseId 托管课程ID
     * @return 结果
     */
    public int deleteKgCourseById(Long courseId);
}

import request from '@/utils/request'

// 获取仪表板概览数据
export function getDashboardData() {
  return request({
    url: '/business/dashboard/overview',
    method: 'get'
  })
}

// 获取仪表板统计数据
export function getDashboardStatistics() {
  return request({
    url: '/business/dashboard/statistics',
    method: 'get'
  })
}

// 获取待办事项列表
export function getTodoList() {
  return request({
    url: '/business/dashboard/todos',
    method: 'get'
  })
}

// 获取通知列表
export function getNoticeList(params) {
  return request({
    url: '/business/dashboard/notices',
    method: 'get',
    params
  })
}

// 获取考勤趋势数据
export function getAttendanceTrend(params) {
  return request({
    url: '/business/dashboard/attendance-trend',
    method: 'get',
    params
  })
}

// 获取班级分布数据
export function getClassDistribution() {
  return request({
    url: '/business/dashboard/class-distribution',
    method: 'get'
  })
}

// 获取收入趋势数据
export function getIncomeTrend(params) {
  return request({
    url: '/business/dashboard/income-trend',
    method: 'get',
    params
  })
}

// 标记待办事项为已处理
export function markTodoComplete(todoId) {
  return request({
    url: '/business/dashboard/todo/' + todoId + '/complete',
    method: 'put'
  })
}

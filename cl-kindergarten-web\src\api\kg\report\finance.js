import request from '@/utils/request'

// 查询财务报表列表
export function getFinanceReport(query) {
  return request({
    url: '/kg/report/finance/list',
    method: 'get',
    params: query
  })
}

// 查询财务明细记录
export function getFinanceDetail(query) {
  return request({
    url: '/kg/report/finance/detail',
    method: 'get',
    params: query
  })
}

// 导出财务报表
export function exportFinanceReport(query) {
  return request({
    url: '/kg/report/finance/export',
    method: 'get',
    params: query
  })
}

// 获取财务图表数据
export function getFinanceChartData(query) {
  return request({
    url: '/kg/report/finance/chart',
    method: 'get',
    params: query
  })
}

// 获取财务汇总数据
export function getFinanceSummary(query) {
  return request({
    url: '/kg/report/finance/summary',
    method: 'get',
    params: query
  })
}

// 查询收入明细
export function getIncomeDetail(query) {
  return request({
    url: '/kg/report/finance/income/detail',
    method: 'get',
    params: query
  })
}

// 查询支出明细
export function getExpenseDetail(query) {
  return request({
    url: '/kg/report/finance/expense/detail',
    method: 'get',
    params: query
  })
}

// 获取收入结构分析
export function getIncomeStructure(query) {
  return request({
    url: '/kg/report/finance/income/structure',
    method: 'get',
    params: query
  })
}

// 获取支出结构分析
export function getExpenseStructure(query) {
  return request({
    url: '/kg/report/finance/expense/structure',
    method: 'get',
    params: query
  })
}

// 获取利润分析
export function getProfitAnalysis(query) {
  return request({
    url: '/kg/report/finance/profit/analysis',
    method: 'get',
    params: query
  })
}

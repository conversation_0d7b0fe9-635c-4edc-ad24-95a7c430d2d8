package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgTuitionBill;
import com.cl.project.business.service.IKgTuitionBillService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 园费账单Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/tuition-bill")
public class KgTuitionBillController extends BaseController
{
    @Autowired
    private IKgTuitionBillService kgTuitionBillService;

    /**
     * 查询园费账单列表
     */
    @SaCheckPermission("kg:finance:tuition:list")
    @GetMapping("/list")
    public TableDataInfo list(KgTuitionBill kgTuitionBill)
    {
        startPage();
        List<KgTuitionBill> list = kgTuitionBillService.selectKgTuitionBillList(kgTuitionBill);
        return getDataTable(list);
    }

    /**
     * 导出园费账单列表
     */
    @SaCheckPermission("kg:finance:tuition:view")
    @Log(title = "园费账单", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgTuitionBill kgTuitionBill)
    {
        List<KgTuitionBill> list = kgTuitionBillService.selectKgTuitionBillList(kgTuitionBill);
        ExcelUtil<KgTuitionBill> util = new ExcelUtil<KgTuitionBill>(KgTuitionBill.class);
        return util.exportExcel(list, "bill");
    }

    /**
     * 获取园费账单详细信息
     */
    @SaCheckPermission("kg:finance:tuition:view")
    @GetMapping(value = "/{billId}")
    public AjaxResult getInfo(@PathVariable("billId") Long billId)
    {
        return AjaxResult.success(kgTuitionBillService.selectKgTuitionBillById(billId));
    }

    /**
     * 新增园费账单
     */
    @SaCheckPermission("kg:finance:tuition:calculate")
    @Log(title = "园费账单", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgTuitionBill kgTuitionBill)
    {
        return toAjax(kgTuitionBillService.insertKgTuitionBill(kgTuitionBill));
    }

    /**
     * 修改园费账单
     */
    @SaCheckPermission("kg:finance:tuition:calculate")
    @Log(title = "园费账单", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgTuitionBill kgTuitionBill)
    {
        return toAjax(kgTuitionBillService.updateKgTuitionBill(kgTuitionBill));
    }

    /**
     * 删除园费账单
     */
    @SaCheckPermission("kg:finance:tuition:send")
    @Log(title = "园费账单", businessType = BusinessType.DELETE)
	@DeleteMapping("/{billIds}")
    public AjaxResult remove(@PathVariable Long[] billIds)
    {
        return toAjax(kgTuitionBillService.deleteKgTuitionBillByIds(billIds));
    }
}

package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgUserBindRequest;
import com.cl.project.business.service.IKgUserBindRequestService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 用户绑定申请记录Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/request")
public class KgUserBindRequestController extends BaseController
{
    @Autowired
    private IKgUserBindRequestService kgUserBindRequestService;

    /**
     * 查询用户绑定申请记录列表
     */
    @SaCheckPermission("business:request:list")
    @GetMapping("/list")
    public TableDataInfo list(KgUserBindRequest kgUserBindRequest)
    {
        startPage();
        List<KgUserBindRequest> list = kgUserBindRequestService.selectKgUserBindRequestList(kgUserBindRequest);
        return getDataTable(list);
    }

    /**
     * 导出用户绑定申请记录列表
     */
    @SaCheckPermission("business:request:export")
    @Log(title = "用户绑定申请记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgUserBindRequest kgUserBindRequest)
    {
        List<KgUserBindRequest> list = kgUserBindRequestService.selectKgUserBindRequestList(kgUserBindRequest);
        ExcelUtil<KgUserBindRequest> util = new ExcelUtil<KgUserBindRequest>(KgUserBindRequest.class);
        return util.exportExcel(list, "request");
    }

    /**
     * 获取用户绑定申请记录详细信息
     */
    @SaCheckPermission("business:request:query")
    @GetMapping(value = "/{requestId}")
    public AjaxResult getInfo(@PathVariable("requestId") Long requestId)
    {
        return AjaxResult.success(kgUserBindRequestService.selectKgUserBindRequestById(requestId));
    }

    /**
     * 新增用户绑定申请记录
     */
    @SaCheckPermission("business:request:add")
    @Log(title = "用户绑定申请记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgUserBindRequest kgUserBindRequest)
    {
        return toAjax(kgUserBindRequestService.insertKgUserBindRequest(kgUserBindRequest));
    }

    /**
     * 修改用户绑定申请记录
     */
    @SaCheckPermission("business:request:edit")
    @Log(title = "用户绑定申请记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgUserBindRequest kgUserBindRequest)
    {
        return toAjax(kgUserBindRequestService.updateKgUserBindRequest(kgUserBindRequest));
    }

    /**
     * 删除用户绑定申请记录
     */
    @SaCheckPermission("business:request:remove")
    @Log(title = "用户绑定申请记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{requestIds}")
    public AjaxResult remove(@PathVariable Long[] requestIds)
    {
        return toAjax(kgUserBindRequestService.deleteKgUserBindRequestByIds(requestIds));
    }
}

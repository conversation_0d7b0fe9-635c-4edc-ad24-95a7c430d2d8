package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgMobilePermissionConfig;
import com.cl.project.business.service.IKgMobilePermissionConfigService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 移动端权限配置Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/mobile-permission-config")
public class KgMobilePermissionConfigController extends BaseController
{
    @Autowired
    private IKgMobilePermissionConfigService kgMobilePermissionConfigService;

    /**
     * 查询移动端权限配置列表
     */
    @SaCheckPermission("business:config:list")
    @GetMapping("/list")
    public TableDataInfo list(KgMobilePermissionConfig kgMobilePermissionConfig)
    {
        startPage();
        List<KgMobilePermissionConfig> list = kgMobilePermissionConfigService.selectKgMobilePermissionConfigList(kgMobilePermissionConfig);
        return getDataTable(list);
    }

    /**
     * 导出移动端权限配置列表
     */
    @SaCheckPermission("business:config:export")
    @Log(title = "移动端权限配置", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgMobilePermissionConfig kgMobilePermissionConfig)
    {
        List<KgMobilePermissionConfig> list = kgMobilePermissionConfigService.selectKgMobilePermissionConfigList(kgMobilePermissionConfig);
        ExcelUtil<KgMobilePermissionConfig> util = new ExcelUtil<KgMobilePermissionConfig>(KgMobilePermissionConfig.class);
        return util.exportExcel(list, "config");
    }

    /**
     * 获取移动端权限配置详细信息
     */
    @SaCheckPermission("business:config:query")
    @GetMapping(value = "/{configId}")
    public AjaxResult getInfo(@PathVariable("configId") Long configId)
    {
        return AjaxResult.success(kgMobilePermissionConfigService.selectKgMobilePermissionConfigById(configId));
    }

    /**
     * 新增移动端权限配置
     */
    @SaCheckPermission("business:config:add")
    @Log(title = "移动端权限配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgMobilePermissionConfig kgMobilePermissionConfig)
    {
        return toAjax(kgMobilePermissionConfigService.insertKgMobilePermissionConfig(kgMobilePermissionConfig));
    }

    /**
     * 修改移动端权限配置
     */
    @SaCheckPermission("business:config:edit")
    @Log(title = "移动端权限配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgMobilePermissionConfig kgMobilePermissionConfig)
    {
        return toAjax(kgMobilePermissionConfigService.updateKgMobilePermissionConfig(kgMobilePermissionConfig));
    }

    /**
     * 删除移动端权限配置
     */
    @SaCheckPermission("business:config:remove")
    @Log(title = "移动端权限配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{configIds}")
    public AjaxResult remove(@PathVariable Long[] configIds)
    {
        return toAjax(kgMobilePermissionConfigService.deleteKgMobilePermissionConfigByIds(configIds));
    }
}

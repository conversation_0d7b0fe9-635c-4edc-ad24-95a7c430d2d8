package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgCourseEnrollment;
import com.cl.project.business.service.IKgCourseEnrollmentService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 托管报名记录Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/enrollment")
public class KgCourseEnrollmentController extends BaseController
{
    @Autowired
    private IKgCourseEnrollmentService kgCourseEnrollmentService;

    /**
     * 查询托管报名记录列表
     */
    @SaCheckPermission("business:enrollment:list")
    @GetMapping("/list")
    public TableDataInfo list(KgCourseEnrollment kgCourseEnrollment)
    {
        startPage();
        List<KgCourseEnrollment> list = kgCourseEnrollmentService.selectKgCourseEnrollmentList(kgCourseEnrollment);
        return getDataTable(list);
    }

    /**
     * 导出托管报名记录列表
     */
    @SaCheckPermission("business:enrollment:export")
    @Log(title = "托管报名记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgCourseEnrollment kgCourseEnrollment)
    {
        List<KgCourseEnrollment> list = kgCourseEnrollmentService.selectKgCourseEnrollmentList(kgCourseEnrollment);
        ExcelUtil<KgCourseEnrollment> util = new ExcelUtil<KgCourseEnrollment>(KgCourseEnrollment.class);
        return util.exportExcel(list, "enrollment");
    }

    /**
     * 获取托管报名记录详细信息
     */
    @SaCheckPermission("business:enrollment:query")
    @GetMapping(value = "/{enrollmentId}")
    public AjaxResult getInfo(@PathVariable("enrollmentId") Long enrollmentId)
    {
        return AjaxResult.success(kgCourseEnrollmentService.selectKgCourseEnrollmentById(enrollmentId));
    }

    /**
     * 新增托管报名记录
     */
    @SaCheckPermission("business:enrollment:add")
    @Log(title = "托管报名记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgCourseEnrollment kgCourseEnrollment)
    {
        return toAjax(kgCourseEnrollmentService.insertKgCourseEnrollment(kgCourseEnrollment));
    }

    /**
     * 修改托管报名记录
     */
    @SaCheckPermission("business:enrollment:edit")
    @Log(title = "托管报名记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgCourseEnrollment kgCourseEnrollment)
    {
        return toAjax(kgCourseEnrollmentService.updateKgCourseEnrollment(kgCourseEnrollment));
    }

    /**
     * 删除托管报名记录
     */
    @SaCheckPermission("business:enrollment:remove")
    @Log(title = "托管报名记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{enrollmentIds}")
    public AjaxResult remove(@PathVariable Long[] enrollmentIds)
    {
        return toAjax(kgCourseEnrollmentService.deleteKgCourseEnrollmentByIds(enrollmentIds));
    }
}

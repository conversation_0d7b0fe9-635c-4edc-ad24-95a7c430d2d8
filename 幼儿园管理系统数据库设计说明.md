# 幼儿园管理系统数据库设计说明

## 1. 系统概述

本幼儿园管理系统基于现有多租户框架扩展，支持微信小程序登录、钉钉打卡集成、移动端权限控制等功能。系统采用模块化设计，包含基础信息管理、考勤管理、托管服务、费用管理、工资管理、财务管理、库存管理等核心模块。

## 2. 数据库表结构概览

### 2.1 基础信息管理表（3张）
- `kg_student` - 幼儿信息表
- `kg_class` - 班级信息表  
- `kg_teacher` - 教师信息表

### 2.2 考勤管理表（2张）
- `kg_student_attendance` - 学生考勤记录表
- `kg_teacher_attendance` - 教师考勤记录表

### 2.3 托管服务管理表（3张）
- `kg_course` - 托管课程表
- `kg_course_enrollment` - 托管报名记录表
- `kg_course_attendance` - 托管考勤记录表

### 2.4 费用管理表（4张）
- `kg_tuition_config` - 园费配置表
- `kg_tuition_bill` - 园费账单表
- `kg_course_bill` - 托管费账单表
- `kg_course_bill_detail` - 托管费账单明细表

### 2.5 工资管理表（2张）
- `kg_salary_config` - 工资配置表
- `kg_teacher_salary` - 教师工资表

### 2.6 财务管理表（3张）
- `kg_income` - 收入记录表
- `kg_expense_type` - 支出类型表
- `kg_expense` - 支出记录表

### 2.7 库存管理表（3张）
- `kg_item_category` - 物品类别表
- `kg_item` - 物品信息表
- `kg_stock_record` - 库存变动记录表

### 2.8 钉钉集成表（1张）
- `kg_dingtalk_attendance` - 钉钉打卡记录表

### 2.9 系统配置表（1张）
- `kg_time_config` - 时间段配置表

### 2.10 微信小程序表（5张）
- `kg_wechat_user` - 微信用户表
- `kg_user_bind_request` - 用户绑定申请记录表
- `kg_phone_verification` - 手机号验证码表
- `kg_message_push` - 消息推送记录表
- `kg_wechat_login_log` - 微信登录日志表

### 2.11 移动端权限配置表（1张）
- `kg_mobile_permission_config` - 移动端权限配置表

## 3. 核心表关联关系

### 3.1 基础信息关联图
```
kg_class (班级)
    ├── kg_student.class_id (学生所属班级)
    ├── kg_teacher.teacher_id (班主任)
    └── kg_teacher.teacher_id (副班主任)

kg_teacher (教师)
    ├── sys_user.user_id (系统用户)
    ├── kg_wechat_user.openid (微信绑定)
    └── kg_class (担任班主任/副班主任)

kg_student (学生)
    ├── kg_class.class_id (所属班级)
    ├── kg_wechat_user.openid (家长微信)
    ├── kg_student_attendance (考勤记录)
    ├── kg_course_enrollment (托管报名)
    └── kg_tuition_bill (园费账单)
```

### 3.2 考勤管理关联图
```
kg_student_attendance (学生考勤)
    ├── kg_student.student_id (学生信息)
    ├── kg_class.class_id (班级信息)
    ├── kg_teacher.teacher_id (操作员)
    ├── kg_teacher.teacher_id (确认人)
    └── kg_tuition_bill (园费计算依据)

kg_teacher_attendance (教师考勤)
    ├── kg_teacher.teacher_id (教师信息)
    └── kg_teacher_salary (工资计算依据)
```

### 3.3 托管服务关联图
```
kg_course (课程)
    ├── kg_course_enrollment.course_id (报名记录)
    ├── kg_course_attendance.course_id (考勤记录)
    └── kg_course_bill_detail.course_id (费用明细)

kg_course_enrollment (报名记录)
    ├── kg_student.student_id (学生信息)
    ├── kg_course.course_id (课程信息)
    ├── kg_class.class_id (班级信息)
    └── kg_course_attendance.enrollment_id (考勤记录)

kg_course_attendance (托管考勤)
    ├── kg_course_enrollment.enrollment_id (报名记录)
    ├── kg_student.student_id (学生信息)
    ├── kg_course.course_id (课程信息)
    ├── kg_teacher.teacher_id (授课教师)
    └── kg_course_bill_detail (费用计算)
```

### 3.4 费用管理关联图
```
kg_tuition_bill (园费账单)
    ├── kg_student.student_id (学生信息)
    ├── kg_class.class_id (班级信息)
    ├── kg_student_attendance (出勤统计)
    ├── kg_tuition_config (费用标准)
    └── kg_income (收入记录)

kg_course_bill (托管费账单)
    ├── kg_student.student_id (学生信息)
    ├── kg_course_bill_detail.bill_id (账单明细)
    ├── kg_course_attendance (考勤统计)
    └── kg_income (收入记录)
```

### 3.5 工资管理关联图
```
kg_teacher_salary (教师工资)
    ├── kg_teacher.teacher_id (教师信息)
    ├── kg_teacher_attendance (考勤统计)
    ├── kg_course_attendance (课时统计)
    ├── kg_student_attendance (班级出勤率)
    ├── kg_salary_config (计算规则)
    └── kg_expense (支出记录)
```

### 3.6 微信集成关联图
```
kg_wechat_user (微信用户)
    ├── kg_student.student_id (家长绑定学生)
    ├── kg_teacher.teacher_id (教师绑定)
    ├── sys_user.user_id (管理员绑定)
    └── kg_message_push.openid (消息推送)

kg_user_bind_request (绑定申请)
    ├── kg_wechat_user.openid (微信用户)
    ├── kg_teacher.teacher_id (匹配的教师)
    └── sys_user.user_id (匹配的用户)
```

## 4. 业务流程说明

### 4.1 微信登录绑定流程
1. **家长登录**：微信授权 → 手机号验证 → 自动关联学生信息
2. **教师登录**：微信授权 → 手机号验证 → 提交绑定申请 → 管理员审核 → 绑定成功
3. **管理员登录**：微信授权 → 手机号验证 → 提交绑定申请 → 超级管理员审核 → 绑定成功

### 4.2 考勤管理流程
1. **学生考勤**：刷脸/手动签到 → 教师确认 → 生成考勤记录 → 用于园费计算
2. **教师考勤**：刷脸/手动签到 → 管理员确认 → 生成考勤记录 → 用于工资计算
3. **托管考勤**：课程时间签到 → 教师确认 → 扣除课时 → 用于托管费计算

### 4.3 费用计算流程
1. **园费计算**：
   - 根据出勤天数计算餐费（每天20元）
   - 根据出勤率计算保教费（超过50%收全额1200元，否则600元）
   - 计算余额并生成下月预交金额
   - 推送账单给家长

2. **托管费计算**：
   - 统计每月各课程出勤次数
   - 按单价计算各课程费用
   - 计算赠送课时（满10节课赠送等规则）
   - 推送账单给家长

### 4.4 工资计算流程
1. **基本工资**：根据出勤天数按比例计算
2. **满勤奖**：出勤率100%奖励200元
3. **课时费**：根据托管课程出勤人数计算（8人以下5元/课时，8人以上10元/课时）
4. **报名奖励**：班级报名率达50%按5元/课时奖励，否则1元/课时
5. **出勤率奖励**：根据班级学生出勤率给予奖励或扣款
6. **新生奖励**：每新增学生奖励50-100元
7. **退园扣款**：每退园学生扣款相应金额
8. **社保代扣**：代扣代缴社会保险

## 5. 权限控制说明

### 5.1 角色权限设计
- **园长**：全部权限
- **教务主任**：教学管理、考勤管理、学生管理、统计报表
- **班主任**：本班学生考勤、学生信息、工资查询
- **副班主任**：本班学生考勤、学生信息、工资查询（需审批）
- **托管教师**：自己课程的托管考勤、工资查询
- **财务**：费用管理、工资管理、库存管理、财务报表
- **保健医**：学生信息查看、缺勤登记、考勤统计

### 5.2 移动端权限控制
- **时间限制**：不同角色有不同的操作时间限制
- **位置限制**：部分操作需要在园区内进行
- **审批机制**：副班主任等角色的操作需要审批确认
- **数据范围**：根据角色限制可查看的数据范围

## 6. 技术特性

### 6.1 多租户支持
- 所有表都包含`com_id`字段实现数据隔离
- 支持多个幼儿园独立运营

### 6.2 审计功能
- 所有表都包含创建人、创建时间、更新人、更新时间字段
- 支持操作日志追踪

### 6.3 扩展性设计
- 支出类型表支持自定义扩展（预留项目1-10）
- 物品类别表支持层级扩展
- 工资配置表支持灵活的计算规则配置

### 6.4 集成能力
- 钉钉API集成获取打卡数据
- 微信小程序和公众号消息推送
- 人脸识别系统集成

## 7. 数据安全

### 7.1 敏感信息保护
- 身份证号、手机号等敏感信息需要加密存储
- 微信openid等隐私信息严格控制访问权限

### 7.2 数据备份
- 建议定期备份重要业务数据
- 考勤记录、费用记录等核心数据需要长期保存

### 7.3 权限控制
- 基于角色的权限控制（RBAC）
- 数据权限控制（按班级、课程等维度）
- 操作权限控制（时间、位置等限制）

## 8. 部署建议

### 8.1 索引优化
- 为高频查询字段创建合适的索引
- 考勤记录表按日期分区存储
- 定期清理过期的验证码和日志数据

### 8.2 性能优化
- 大数据量表考虑分表分库
- 统计报表可考虑使用缓存
- 图片等文件建议使用对象存储

### 8.3 监控告警
- 监控数据库性能指标
- 设置关键业务指标告警
- 定期检查数据一致性

package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgTimeConfigMapper;
import com.cl.project.business.domain.KgTimeConfig;
import com.cl.project.business.service.IKgTimeConfigService;

/**
 * 时间段配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgTimeConfigServiceImpl implements IKgTimeConfigService 
{
    @Autowired
    private KgTimeConfigMapper kgTimeConfigMapper;

    /**
     * 查询时间段配置
     * 
     * @param configId 时间段配置ID
     * @return 时间段配置
     */
    @Override
    public KgTimeConfig selectKgTimeConfigById(Long configId)
    {
        return kgTimeConfigMapper.selectKgTimeConfigById(configId);
    }

    /**
     * 查询时间段配置列表
     * 
     * @param kgTimeConfig 时间段配置
     * @return 时间段配置
     */
    @Override
    public List<KgTimeConfig> selectKgTimeConfigList(KgTimeConfig kgTimeConfig)
    {
        return kgTimeConfigMapper.selectKgTimeConfigList(kgTimeConfig);
    }

    /**
     * 新增时间段配置
     * 
     * @param kgTimeConfig 时间段配置
     * @return 结果
     */
    @Override
    public int insertKgTimeConfig(KgTimeConfig kgTimeConfig)
    {
        kgTimeConfig.setCreateTime(DateUtils.getNowDate());
        return kgTimeConfigMapper.insertKgTimeConfig(kgTimeConfig);
    }

    /**
     * 修改时间段配置
     * 
     * @param kgTimeConfig 时间段配置
     * @return 结果
     */
    @Override
    public int updateKgTimeConfig(KgTimeConfig kgTimeConfig)
    {
        kgTimeConfig.setUpdateTime(DateUtils.getNowDate());
        return kgTimeConfigMapper.updateKgTimeConfig(kgTimeConfig);
    }

    /**
     * 批量删除时间段配置
     * 
     * @param configIds 需要删除的时间段配置ID
     * @return 结果
     */
    @Override
    public int deleteKgTimeConfigByIds(Long[] configIds)
    {
        return kgTimeConfigMapper.deleteKgTimeConfigByIds(configIds);
    }

    /**
     * 删除时间段配置信息
     * 
     * @param configId 时间段配置ID
     * @return 结果
     */
    @Override
    public int deleteKgTimeConfigById(Long configId)
    {
        return kgTimeConfigMapper.deleteKgTimeConfigById(configId);
    }
}

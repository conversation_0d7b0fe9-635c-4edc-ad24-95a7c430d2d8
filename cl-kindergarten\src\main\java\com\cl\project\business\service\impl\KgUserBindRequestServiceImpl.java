package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgUserBindRequestMapper;
import com.cl.project.business.domain.KgUserBindRequest;
import com.cl.project.business.service.IKgUserBindRequestService;

/**
 * 用户绑定申请记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgUserBindRequestServiceImpl implements IKgUserBindRequestService 
{
    @Autowired
    private KgUserBindRequestMapper kgUserBindRequestMapper;

    /**
     * 查询用户绑定申请记录
     * 
     * @param requestId 用户绑定申请记录ID
     * @return 用户绑定申请记录
     */
    @Override
    public KgUserBindRequest selectKgUserBindRequestById(Long requestId)
    {
        return kgUserBindRequestMapper.selectKgUserBindRequestById(requestId);
    }

    /**
     * 查询用户绑定申请记录列表
     * 
     * @param kgUserBindRequest 用户绑定申请记录
     * @return 用户绑定申请记录
     */
    @Override
    public List<KgUserBindRequest> selectKgUserBindRequestList(KgUserBindRequest kgUserBindRequest)
    {
        return kgUserBindRequestMapper.selectKgUserBindRequestList(kgUserBindRequest);
    }

    /**
     * 新增用户绑定申请记录
     * 
     * @param kgUserBindRequest 用户绑定申请记录
     * @return 结果
     */
    @Override
    public int insertKgUserBindRequest(KgUserBindRequest kgUserBindRequest)
    {
        kgUserBindRequest.setCreateTime(DateUtils.getNowDate());
        return kgUserBindRequestMapper.insertKgUserBindRequest(kgUserBindRequest);
    }

    /**
     * 修改用户绑定申请记录
     * 
     * @param kgUserBindRequest 用户绑定申请记录
     * @return 结果
     */
    @Override
    public int updateKgUserBindRequest(KgUserBindRequest kgUserBindRequest)
    {
        kgUserBindRequest.setUpdateTime(DateUtils.getNowDate());
        return kgUserBindRequestMapper.updateKgUserBindRequest(kgUserBindRequest);
    }

    /**
     * 批量删除用户绑定申请记录
     * 
     * @param requestIds 需要删除的用户绑定申请记录ID
     * @return 结果
     */
    @Override
    public int deleteKgUserBindRequestByIds(Long[] requestIds)
    {
        return kgUserBindRequestMapper.deleteKgUserBindRequestByIds(requestIds);
    }

    /**
     * 删除用户绑定申请记录信息
     * 
     * @param requestId 用户绑定申请记录ID
     * @return 结果
     */
    @Override
    public int deleteKgUserBindRequestById(Long requestId)
    {
        return kgUserBindRequestMapper.deleteKgUserBindRequestById(requestId);
    }
}

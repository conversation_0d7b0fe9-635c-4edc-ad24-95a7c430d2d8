<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>班级详细信息</span>
        <div style="float: right;">
          <el-button type="primary" size="small" @click="handleEdit">编辑</el-button>
          <el-button size="small" @click="handleBack">返回</el-button>
        </div>
      </div>
      
      <!-- 基本信息 -->
      <div class="detail-section">
        <h4 class="section-title">基本信息</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>班级编号：</label>
              <span>{{ classInfo.classCode }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>班级名称：</label>
              <span>{{ classInfo.className }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>年级：</label>
              <span>{{ getGradeText(classInfo.grade) }}</span>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>容量上限：</label>
              <span>{{ classInfo.capacity }}人</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>当前人数：</label>
              <span>{{ classInfo.currentCount }}人</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>班级状态：</label>
              <el-tag :type="classInfo.status === '0' ? 'success' : 'danger'">
                {{ classInfo.status === '0' ? '正常' : '停课' }}
              </el-tag>
            </div>
          </el-col>
        </el-row>
        
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>教室位置：</label>
              <span>{{ classInfo.classroom || '未分配' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>班主任：</label>
              <span>{{ classInfo.headTeacherName || '未分配' }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>副班主任：</label>
              <span>{{ classInfo.assistantTeacherName || '未分配' }}</span>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 统计信息 -->
      <div class="detail-section">
        <h4 class="section-title">统计信息</h4>
        <el-row :gutter="20">
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-number">{{ statistics.totalStudents }}</div>
              <div class="stat-label">总学生数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-number">{{ statistics.maleCount }}</div>
              <div class="stat-label">男生人数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-number">{{ statistics.femaleCount }}</div>
              <div class="stat-label">女生人数</div>
            </div>
          </el-col>
          <el-col :span="6">
            <div class="stat-card">
              <div class="stat-number">{{ statistics.attendanceRate }}%</div>
              <div class="stat-label">出勤率</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 费用统计 -->
      <div class="detail-section">
        <h4 class="section-title">费用统计</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-number">¥{{ feeStats.totalAmount }}</div>
              <div class="stat-label">本月总应收费用</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-number">¥{{ feeStats.paidAmount }}</div>
              <div class="stat-label">已收费用</div>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="stat-card">
              <div class="stat-number">¥{{ feeStats.unpaidAmount }}</div>
              <div class="stat-label">未收费用</div>
            </div>
          </el-col>
        </el-row>
      </div>

      <!-- 备注信息 -->
      <div class="detail-section">
        <h4 class="section-title">备注信息</h4>
        <div class="detail-item">
          <span>{{ classInfo.remark || '无备注信息' }}</span>
        </div>
      </div>

      <!-- 系统信息 -->
      <div class="detail-section">
        <h4 class="section-title">系统信息</h4>
        <el-row :gutter="20">
          <el-col :span="8">
            <div class="detail-item">
              <label>创建人：</label>
              <span>{{ classInfo.createBy }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>创建时间：</label>
              <span>{{ classInfo.createTime }}</span>
            </div>
          </el-col>
          <el-col :span="8">
            <div class="detail-item">
              <label>更新时间：</label>
              <span>{{ classInfo.updateTime }}</span>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-card>

    <!-- 学生名单 -->
    <el-card class="box-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>学生名单</span>
        <div style="float: right;">
          <el-button type="primary" size="small" @click="handleAddStudent">添加学生</el-button>
          <el-button type="success" size="small" @click="handleExportStudents">导出名单</el-button>
        </div>
      </div>
      
      <el-table :data="studentList" style="width: 100%">
        <el-table-column type="index" label="序号" width="60" />
        <el-table-column prop="studentCode" label="学生编号" width="120" />
        <el-table-column prop="studentName" label="学生姓名" width="100" />
        <el-table-column prop="gender" label="性别" width="60">
          <template slot-scope="scope">
            {{ scope.row.gender === '0' ? '男' : '女' }}
          </template>
        </el-table-column>
        <el-table-column prop="birthDate" label="出生日期" width="100" />
        <el-table-column prop="parentName" label="家长姓名" width="100" />
        <el-table-column prop="parentPhone" label="家长电话" width="120" />
        <el-table-column prop="enrollmentDate" label="入园日期" width="100" />
        <el-table-column prop="status" label="状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="getStudentStatusType(scope.row.status)" size="mini">
              {{ getStudentStatusText(scope.row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" width="200">
          <template slot-scope="scope">
            <el-button type="text" size="small" @click="viewStudent(scope.row)">查看</el-button>
            <el-button type="text" size="small" @click="editStudent(scope.row)">编辑</el-button>
            <el-button type="text" size="small" style="color: #f56c6c;" @click="removeStudent(scope.row)">移出</el-button>
          </template>
        </el-table-column>
      </el-table>
      
      <pagination
        v-show="studentTotal > 0"
        :total="studentTotal"
        :page.sync="studentQueryParams.pageNum"
        :limit.sync="studentQueryParams.pageSize"
        @pagination="getStudentList"
      />
    </el-card>

    <!-- 课程安排 -->
    <el-card class="box-card" style="margin-top: 20px;">
      <div slot="header" class="clearfix">
        <span>课程安排</span>
        <el-button type="text" style="float: right;" @click="$router.push('/kg/course/schedule')">
          查看更多
        </el-button>
      </div>
      
      <el-table :data="courseSchedule" style="width: 100%">
        <el-table-column prop="courseName" label="课程名称" />
        <el-table-column prop="teacherName" label="授课教师" width="100" />
        <el-table-column prop="courseTime" label="上课时间" width="150" />
        <el-table-column prop="duration" label="课时长度" width="100" />
        <el-table-column prop="classroom" label="教室" width="100" />
        <el-table-column prop="courseStatus" label="状态" width="80">
          <template slot-scope="scope">
            <el-tag :type="scope.row.courseStatus === 'active' ? 'success' : 'info'" size="mini">
              {{ scope.row.courseStatus === 'active' ? '进行中' : '已结束' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>
    </el-card>

    <!-- 添加学生对话框 -->
    <el-dialog title="添加学生" :visible.sync="addStudentDialog" width="60%" :close-on-click-modal="false">
      <el-form :model="addStudentForm" label-width="100px">
        <el-form-item label="选择学生">
          <el-select
            v-model="addStudentForm.studentIds"
            multiple
            placeholder="请选择要添加到班级的学生"
            style="width: 100%"
          >
            <el-option
              v-for="student in availableStudents"
              :key="student.studentId"
              :label="`${student.studentName} (${student.studentCode})`"
              :value="student.studentId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addStudentDialog = false">取 消</el-button>
        <el-button type="primary" @click="confirmAddStudent">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getClass } from "@/api/kg/student/class";
import { listStudent } from "@/api/kg/student/info";

export default {
  name: "ClassDetail",
  data() {
    return {
      // 班级信息
      classInfo: {
        classId: undefined,
        classCode: '',
        className: '',
        grade: '',
        capacity: 0,
        currentCount: 0,
        status: '',
        classroom: '',
        headTeacherName: '',
        assistantTeacherName: '',
        remark: '',
        createBy: '',
        createTime: '',
        updateTime: ''
      },
      // 统计信息
      statistics: {
        totalStudents: 0,
        maleCount: 0,
        femaleCount: 0,
        attendanceRate: 0
      },
      // 费用统计
      feeStats: {
        totalAmount: 0,
        paidAmount: 0,
        unpaidAmount: 0
      },
      // 学生列表
      studentList: [],
      studentTotal: 0,
      studentQueryParams: {
        pageNum: 1,
        pageSize: 10,
        classId: undefined
      },
      // 课程安排
      courseSchedule: [],
      // 添加学生对话框
      addStudentDialog: false,
      addStudentForm: {
        studentIds: []
      },
      // 可添加的学生
      availableStudents: []
    };
  },
  created() {
    const classId = this.$route.query.classId || this.$route.params.classId;
    if (classId) {
      this.classInfo.classId = classId;
      this.studentQueryParams.classId = classId;
      this.getClassInfo(classId);
      this.loadStatistics(classId);
      this.loadFeeStats(classId);
      this.getStudentList();
      this.loadCourseSchedule(classId);
    }
  },
  methods: {
    // 获取班级信息
    getClassInfo(classId) {
      getClass(classId).then(response => {
        this.classInfo = response.data;
      });
    },
    
    // 加载统计信息
    loadStatistics(classId) {
      // 模拟数据，实际应该调用API
      this.statistics = {
        totalStudents: 25,
        maleCount: 13,
        femaleCount: 12,
        attendanceRate: 92.5
      };
    },
    
    // 加载费用统计
    loadFeeStats(classId) {
      // 模拟数据，实际应该调用API
      this.feeStats = {
        totalAmount: 50000,
        paidAmount: 45000,
        unpaidAmount: 5000
      };
    },
    
    // 获取学生列表
    getStudentList() {
      listStudent(this.studentQueryParams).then(response => {
        this.studentList = response.rows;
        this.studentTotal = response.total;
      });
    },
    
    // 加载课程安排
    loadCourseSchedule(classId) {
      // 模拟数据，实际应该调用API
      this.courseSchedule = [
        { courseName: '语言课', teacherName: '张老师', courseTime: '09:00-09:30', duration: '30分钟', classroom: '101', courseStatus: 'active' },
        { courseName: '数学课', teacherName: '李老师', courseTime: '10:00-10:30', duration: '30分钟', classroom: '101', courseStatus: 'active' },
        { courseName: '美术课', teacherName: '王老师', courseTime: '14:00-14:30', duration: '30分钟', classroom: '101', courseStatus: 'active' },
        { courseName: '音乐课', teacherName: '赵老师', courseTime: '15:00-15:30', duration: '30分钟', classroom: '101', courseStatus: 'active' }
      ];
    },
    
    // 获取年级文本
    getGradeText(grade) {
      const gradeMap = {
        'nursery': '托班',
        'small': '小班',
        'medium': '中班', 
        'large': '大班'
      };
      return gradeMap[grade] || '未知';
    },
    
    // 获取学生状态类型
    getStudentStatusType(status) {
      const statusMap = {
        '0': 'success',
        '1': 'danger',
        '2': 'warning'
      };
      return statusMap[status] || 'info';
    },
    
    // 获取学生状态文本
    getStudentStatusText(status) {
      const statusMap = {
        '0': '在园',
        '1': '退园',
        '2': '请假'
      };
      return statusMap[status] || '未知';
    },
    
    // 查看学生
    viewStudent(row) {
      this.$router.push({
        path: '/kg/student/info/detail',
        query: { studentId: row.studentId }
      });
    },
    
    // 编辑学生
    editStudent(row) {
      this.$router.push({
        path: '/kg/student/info/form',
        query: { studentId: row.studentId }
      });
    },
    
    // 移出学生
    removeStudent(row) {
      this.$confirm(`确认要将学生"${row.studentName}"从班级中移出吗？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        // 调用移出学生API
        this.msgSuccess('移出成功');
        this.getStudentList();
      });
    },
    
    // 添加学生
    handleAddStudent() {
      // 获取可添加的学生列表
      this.getAvailableStudents();
      this.addStudentDialog = true;
    },
    
    // 获取可添加的学生
    getAvailableStudents() {
      // 模拟数据，实际应该调用API获取未分配班级的学生
      this.availableStudents = [
        { studentId: 1, studentName: '张小明', studentCode: 'STU001' },
        { studentId: 2, studentName: '李小红', studentCode: 'STU002' },
        { studentId: 3, studentName: '王小刚', studentCode: 'STU003' }
      ];
    },
    
    // 确认添加学生
    confirmAddStudent() {
      if (this.addStudentForm.studentIds.length === 0) {
        this.msgError('请至少选择一个学生');
        return;
      }
      
      // 调用添加学生到班级API
      this.msgSuccess('添加成功');
      this.addStudentDialog = false;
      this.addStudentForm.studentIds = [];
      this.getStudentList();
    },
    
    // 导出学生名单
    handleExportStudents() {
      this.msgInfo('导出功能开发中...');
    },
    
    // 编辑班级
    handleEdit() {
      this.$router.push({
        path: '/kg/student/class/form',
        query: { classId: this.classInfo.classId }
      });
    },
    
    // 返回列表
    handleBack() {
      this.$router.push('/kg/student/class');
    }
  }
};
</script>

<style lang="scss" scoped>
.app-container {
  padding: 20px;
}

.box-card {
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12), 0 0 6px rgba(0, 0, 0, 0.04);
}

.detail-section {
  margin-bottom: 30px;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.section-title {
  color: #303133;
  font-size: 16px;
  font-weight: 600;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 2px solid #f0f0f0;
}

.detail-item {
  margin-bottom: 15px;
  display: flex;
  align-items: center;
  
  label {
    font-weight: 500;
    color: #606266;
    min-width: 100px;
    margin-right: 10px;
  }
  
  span {
    color: #303133;
    flex: 1;
  }
}

.stat-card {
  text-align: center;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
  border: 1px solid #e9ecef;
  
  .stat-number {
    font-size: 24px;
    font-weight: 600;
    color: #409eff;
    margin-bottom: 8px;
  }
  
  .stat-label {
    font-size: 14px;
    color: #606266;
  }
}

.clearfix:before,
.clearfix:after {
  display: table;
  content: "";
}

.clearfix:after {
  clear: both;
}
</style>

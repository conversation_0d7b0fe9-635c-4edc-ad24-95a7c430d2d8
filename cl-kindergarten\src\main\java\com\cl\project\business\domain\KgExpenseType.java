package com.cl.project.business.domain;

import com.cl.framework.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 支出类型对象 kg_expense_type
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgExpenseType extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 类型ID */
    private Long typeId;

    /** 支出类型名称 */
    @Excel(name = "支出类型名称")
    private String typeName;

    /** 类型编码 */
    @Excel(name = "类型编码")
    private String typeCode;

    /** 父类型ID */
    @Excel(name = "父类型ID")
    private Long parentId;

    /** 排序 */
    @Excel(name = "排序")
    private Long sortOrder;

    /** 是否系统预设（0否 1是） */
    @Excel(name = "是否系统预设", readConverterExp = "0=否,1=是")
    private Long isSystem;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    public void setTypeId(Long typeId) 
    {
        this.typeId = typeId;
    }

    public Long getTypeId() 
    {
        return typeId;
    }
    public void setTypeName(String typeName) 
    {
        this.typeName = typeName;
    }

    public String getTypeName() 
    {
        return typeName;
    }
    public void setTypeCode(String typeCode) 
    {
        this.typeCode = typeCode;
    }

    public String getTypeCode() 
    {
        return typeCode;
    }
    public void setParentId(Long parentId) 
    {
        this.parentId = parentId;
    }

    public Long getParentId() 
    {
        return parentId;
    }
    public void setSortOrder(Long sortOrder) 
    {
        this.sortOrder = sortOrder;
    }

    public Long getSortOrder() 
    {
        return sortOrder;
    }
    public void setIsSystem(Long isSystem) 
    {
        this.isSystem = isSystem;
    }

    public Long getIsSystem() 
    {
        return isSystem;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("typeId", getTypeId())
            .append("typeName", getTypeName())
            .append("typeCode", getTypeCode())
            .append("parentId", getParentId())
            .append("sortOrder", getSortOrder())
            .append("isSystem", getIsSystem())
            .append("status", getStatus())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

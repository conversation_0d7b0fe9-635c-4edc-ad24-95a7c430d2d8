import request from '@/utils/request'

// 查询园费列表
export function listTuition(query) {
  return request({
    url: '/business/tuition-bill/list',
    method: 'get',
    params: query
  })
}

// 查询园费详细
export function getTuition(tuitionId) {
  return request({
    url: '/business/tuition-bill/' + tuitionId,
    method: 'get'
  })
}

// 园费计算
export function calculateTuition(data) {
  return request({
    url: '/business/tuition-bill/calculate',
    method: 'post',
    data: data
  })
}

// 发送园费账单
export function sendTuitionBill(tuitionIds) {
  return request({
    url: '/business/tuition-bill/send',
    method: 'post',
    data: { tuitionIds: tuitionIds }
  })
}

// 导出园费
export function exportTuition(query) {
  return request({
    url: '/business/tuition-bill/export',
    method: 'get',
    params: query
  })
}

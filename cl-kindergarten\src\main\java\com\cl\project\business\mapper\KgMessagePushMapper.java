package com.cl.project.business.mapper;

import java.util.List;
import com.cl.project.business.domain.KgMessagePush;

/**
 * 消息推送记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface KgMessagePushMapper 
{
    /**
     * 查询消息推送记录
     * 
     * @param pushId 消息推送记录ID
     * @return 消息推送记录
     */
    public KgMessagePush selectKgMessagePushById(Long pushId);

    /**
     * 查询消息推送记录列表
     * 
     * @param kgMessagePush 消息推送记录
     * @return 消息推送记录集合
     */
    public List<KgMessagePush> selectKgMessagePushList(KgMessagePush kgMessagePush);

    /**
     * 新增消息推送记录
     * 
     * @param kgMessagePush 消息推送记录
     * @return 结果
     */
    public int insertKgMessagePush(KgMessagePush kgMessagePush);

    /**
     * 修改消息推送记录
     * 
     * @param kgMessagePush 消息推送记录
     * @return 结果
     */
    public int updateKgMessagePush(KgMessagePush kgMessagePush);

    /**
     * 删除消息推送记录
     * 
     * @param pushId 消息推送记录ID
     * @return 结果
     */
    public int deleteKgMessagePushById(Long pushId);

    /**
     * 批量删除消息推送记录
     * 
     * @param pushIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteKgMessagePushByIds(Long[] pushIds);
}

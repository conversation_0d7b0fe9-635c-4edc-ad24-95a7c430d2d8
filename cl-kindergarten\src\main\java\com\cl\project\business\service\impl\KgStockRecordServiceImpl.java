package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgStockRecordMapper;
import com.cl.project.business.domain.KgStockRecord;
import com.cl.project.business.service.IKgStockRecordService;

/**
 * 库存变动记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgStockRecordServiceImpl implements IKgStockRecordService 
{
    @Autowired
    private KgStockRecordMapper kgStockRecordMapper;

    /**
     * 查询库存变动记录
     * 
     * @param recordId 库存变动记录ID
     * @return 库存变动记录
     */
    @Override
    public KgStockRecord selectKgStockRecordById(Long recordId)
    {
        return kgStockRecordMapper.selectKgStockRecordById(recordId);
    }

    /**
     * 查询库存变动记录列表
     * 
     * @param kgStockRecord 库存变动记录
     * @return 库存变动记录
     */
    @Override
    public List<KgStockRecord> selectKgStockRecordList(KgStockRecord kgStockRecord)
    {
        return kgStockRecordMapper.selectKgStockRecordList(kgStockRecord);
    }

    /**
     * 新增库存变动记录
     * 
     * @param kgStockRecord 库存变动记录
     * @return 结果
     */
    @Override
    public int insertKgStockRecord(KgStockRecord kgStockRecord)
    {
        kgStockRecord.setCreateTime(DateUtils.getNowDate());
        return kgStockRecordMapper.insertKgStockRecord(kgStockRecord);
    }

    /**
     * 修改库存变动记录
     * 
     * @param kgStockRecord 库存变动记录
     * @return 结果
     */
    @Override
    public int updateKgStockRecord(KgStockRecord kgStockRecord)
    {
        kgStockRecord.setUpdateTime(DateUtils.getNowDate());
        return kgStockRecordMapper.updateKgStockRecord(kgStockRecord);
    }

    /**
     * 批量删除库存变动记录
     * 
     * @param recordIds 需要删除的库存变动记录ID
     * @return 结果
     */
    @Override
    public int deleteKgStockRecordByIds(Long[] recordIds)
    {
        return kgStockRecordMapper.deleteKgStockRecordByIds(recordIds);
    }

    /**
     * 删除库存变动记录信息
     * 
     * @param recordId 库存变动记录ID
     * @return 结果
     */
    @Override
    public int deleteKgStockRecordById(Long recordId)
    {
        return kgStockRecordMapper.deleteKgStockRecordById(recordId);
    }
}

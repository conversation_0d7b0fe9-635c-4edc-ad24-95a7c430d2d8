package com.cl.project.business.domain;

import com.cl.framework.aspectj.lang.annotation.Excel;
import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * 家长信息对象 kg_parent
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
public class KgParent extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 家长ID */
    private Long parentId;

    /** 家长姓名 */
    @Excel(name = "家长姓名")
    private String parentName;

    /** 家长电话 */
    @Excel(name = "家长电话")
    private String parentPhone;

    /** 微信openid */
    private String wechatOpenid;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String idCard;

    /** 关系类型（父亲、母亲、爷爷、奶奶等） */
    @Excel(name = "关系类型")
    private String relationshipType;

    /** 紧急联系人姓名 */
    @Excel(name = "紧急联系人")
    private String emergencyContact;

    /** 紧急联系电话 */
    @Excel(name = "紧急联系电话")
    private String emergencyPhone;

    /** 家庭住址 */
    @Excel(name = "家庭住址")
    private String address;

    /** 工作单位 */
    @Excel(name = "工作单位")
    private String workUnit;

    /** 职业 */
    @Excel(name = "职业")
    private String occupation;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 绑定状态（0未绑定 1已绑定微信） */
    @Excel(name = "绑定状态", readConverterExp = "0=未绑定,1=已绑定")
    private String bindStatus;

    /** 注册日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "注册日期", dateFormat = "yyyy-MM-dd")
    private Date registerDate;

    /** 公司ID，多租户隔离 */
    private String comId;

    /** 关联的学生数量 */
    private Integer studentCount;

    /** 关联的学生列表 */
    private List<KgStudent> studentList;

    public void setParentId(Long parentId) 
    {
        this.parentId = parentId;
    }

    public Long getParentId() 
    {
        return parentId;
    }
    public void setParentName(String parentName) 
    {
        this.parentName = parentName;
    }

    public String getParentName() 
    {
        return parentName;
    }
    public void setParentPhone(String parentPhone) 
    {
        this.parentPhone = parentPhone;
    }

    public String getParentPhone() 
    {
        return parentPhone;
    }
    public void setWechatOpenid(String wechatOpenid) 
    {
        this.wechatOpenid = wechatOpenid;
    }

    public String getWechatOpenid() 
    {
        return wechatOpenid;
    }
    public void setIdCard(String idCard) 
    {
        this.idCard = idCard;
    }

    public String getIdCard() 
    {
        return idCard;
    }
    public void setRelationshipType(String relationshipType) 
    {
        this.relationshipType = relationshipType;
    }

    public String getRelationshipType() 
    {
        return relationshipType;
    }
    public void setEmergencyContact(String emergencyContact) 
    {
        this.emergencyContact = emergencyContact;
    }

    public String getEmergencyContact() 
    {
        return emergencyContact;
    }
    public void setEmergencyPhone(String emergencyPhone) 
    {
        this.emergencyPhone = emergencyPhone;
    }

    public String getEmergencyPhone() 
    {
        return emergencyPhone;
    }
    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }
    public void setWorkUnit(String workUnit) 
    {
        this.workUnit = workUnit;
    }

    public String getWorkUnit() 
    {
        return workUnit;
    }
    public void setOccupation(String occupation) 
    {
        this.occupation = occupation;
    }

    public String getOccupation() 
    {
        return occupation;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setBindStatus(String bindStatus) 
    {
        this.bindStatus = bindStatus;
    }

    public String getBindStatus() 
    {
        return bindStatus;
    }
    public void setRegisterDate(Date registerDate) 
    {
        this.registerDate = registerDate;
    }

    public Date getRegisterDate() 
    {
        return registerDate;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    public Integer getStudentCount() {
        return studentCount;
    }

    public void setStudentCount(Integer studentCount) {
        this.studentCount = studentCount;
    }

    public List<KgStudent> getStudentList() {
        return studentList;
    }

    public void setStudentList(List<KgStudent> studentList) {
        this.studentList = studentList;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("parentId", getParentId())
            .append("parentName", getParentName())
            .append("parentPhone", getParentPhone())
            .append("wechatOpenid", getWechatOpenid())
            .append("idCard", getIdCard())
            .append("relationshipType", getRelationshipType())
            .append("emergencyContact", getEmergencyContact())
            .append("emergencyPhone", getEmergencyPhone())
            .append("address", getAddress())
            .append("workUnit", getWorkUnit())
            .append("occupation", getOccupation())
            .append("status", getStatus())
            .append("bindStatus", getBindStatus())
            .append("registerDate", getRegisterDate())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

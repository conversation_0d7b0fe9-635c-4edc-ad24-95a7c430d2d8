import request from '@/utils/request'

// 查询用户绑定申请记录列表
export function listRequest(query) {
  return request({
    url: '/business/request/list',
    method: 'get',
    params: query
  })
}

// 查询用户绑定申请记录详细
export function getRequest(requestId) {
  return request({
    url: '/business/request/' + requestId,
    method: 'get'
  })
}

// 新增用户绑定申请记录
export function addRequest(data) {
  return request({
    url: '/business/request',
    method: 'post',
    data: data
  })
}

// 修改用户绑定申请记录
export function updateRequest(data) {
  return request({
    url: '/business/request',
    method: 'put',
    data: data
  })
}

// 删除用户绑定申请记录
export function delRequest(requestId) {
  return request({
    url: '/business/request/' + requestId,
    method: 'delete'
  })
}

// 导出用户绑定申请记录
export function exportRequest(query) {
  return request({
    url: '/business/request/export',
    method: 'get',
    params: query
  })
}

package com.cl.project.business.domain.dto;

import java.util.List;

/**
 * 钉钉获取用户列表响应
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
public class DingtalkUserListResponse 
{
    private Integer errcode;
    private String errmsg;
    private String request_id;
    private Result result;
    
    public static class Result 
    {
        private List<String> userid_list;
        
        public List<String> getUserid_list() 
        {
            return userid_list;
        }
        
        public void setUserid_list(List<String> userid_list) 
        {
            this.userid_list = userid_list;
        }
    }
    
    public Integer getErrcode() 
    {
        return errcode;
    }
    
    public void setErrcode(Integer errcode) 
    {
        this.errcode = errcode;
    }
    
    public String getErrmsg() 
    {
        return errmsg;
    }
    
    public void setErrmsg(String errmsg) 
    {
        this.errmsg = errmsg;
    }
    
    public String getRequest_id() 
    {
        return request_id;
    }
    
    public void setRequest_id(String request_id) 
    {
        this.request_id = request_id;
    }
    
    public Result getResult() 
    {
        return result;
    }
    
    public void setResult(Result result) 
    {
        this.result = result;
    }
    
    public boolean isSuccess() 
    {
        return errcode != null && errcode == 0;
    }
}

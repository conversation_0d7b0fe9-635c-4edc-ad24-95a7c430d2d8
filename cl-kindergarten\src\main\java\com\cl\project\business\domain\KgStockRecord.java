package com.cl.project.business.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 库存变动记录对象 kg_stock_record
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgStockRecord extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    private Long recordId;

    /** 物品ID，关联kg_item.item_id */
    @Excel(name = "物品ID，关联kg_item.item_id")
    private Long itemId;

    /** 变动类型（in入库、out出库、adjust调整） */
    @Excel(name = "变动类型", readConverterExp = "i=n入库、out出库、adjust调整")
    private String recordType;

    /** 变动时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "变动时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date recordDate;

    /** 变动数量 */
    @Excel(name = "变动数量")
    private Long quantity;

    /** 单价 */
    @Excel(name = "单价")
    private BigDecimal unitPrice;

    /** 总金额 */
    @Excel(name = "总金额")
    private BigDecimal totalAmount;

    /** 变动前库存 */
    @Excel(name = "变动前库存")
    private Long beforeStock;

    /** 变动后库存 */
    @Excel(name = "变动后库存")
    private Long afterStock;

    /** 供应商 */
    @Excel(name = "供应商")
    private String supplier;

    /** 领用人 */
    @Excel(name = "领用人")
    private String recipient;

    /** 用途说明 */
    @Excel(name = "用途说明")
    private String purpose;

    /** 操作员ID，关联kg_teacher.teacher_id */
    @Excel(name = "操作员ID，关联kg_teacher.teacher_id")
    private Long operatorId;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    public void setRecordId(Long recordId) 
    {
        this.recordId = recordId;
    }

    public Long getRecordId() 
    {
        return recordId;
    }
    public void setItemId(Long itemId) 
    {
        this.itemId = itemId;
    }

    public Long getItemId() 
    {
        return itemId;
    }
    public void setRecordType(String recordType) 
    {
        this.recordType = recordType;
    }

    public String getRecordType() 
    {
        return recordType;
    }
    public void setRecordDate(Date recordDate) 
    {
        this.recordDate = recordDate;
    }

    public Date getRecordDate() 
    {
        return recordDate;
    }
    public void setQuantity(Long quantity) 
    {
        this.quantity = quantity;
    }

    public Long getQuantity() 
    {
        return quantity;
    }
    public void setUnitPrice(BigDecimal unitPrice) 
    {
        this.unitPrice = unitPrice;
    }

    public BigDecimal getUnitPrice() 
    {
        return unitPrice;
    }
    public void setTotalAmount(BigDecimal totalAmount) 
    {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getTotalAmount() 
    {
        return totalAmount;
    }
    public void setBeforeStock(Long beforeStock) 
    {
        this.beforeStock = beforeStock;
    }

    public Long getBeforeStock() 
    {
        return beforeStock;
    }
    public void setAfterStock(Long afterStock) 
    {
        this.afterStock = afterStock;
    }

    public Long getAfterStock() 
    {
        return afterStock;
    }
    public void setSupplier(String supplier) 
    {
        this.supplier = supplier;
    }

    public String getSupplier() 
    {
        return supplier;
    }
    public void setRecipient(String recipient) 
    {
        this.recipient = recipient;
    }

    public String getRecipient() 
    {
        return recipient;
    }
    public void setPurpose(String purpose) 
    {
        this.purpose = purpose;
    }

    public String getPurpose() 
    {
        return purpose;
    }
    public void setOperatorId(Long operatorId) 
    {
        this.operatorId = operatorId;
    }

    public Long getOperatorId() 
    {
        return operatorId;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("recordId", getRecordId())
            .append("itemId", getItemId())
            .append("recordType", getRecordType())
            .append("recordDate", getRecordDate())
            .append("quantity", getQuantity())
            .append("unitPrice", getUnitPrice())
            .append("totalAmount", getTotalAmount())
            .append("beforeStock", getBeforeStock())
            .append("afterStock", getAfterStock())
            .append("supplier", getSupplier())
            .append("recipient", getRecipient())
            .append("purpose", getPurpose())
            .append("operatorId", getOperatorId())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

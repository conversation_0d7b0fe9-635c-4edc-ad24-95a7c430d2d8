import request from '@/utils/request'

// 查询托管费列表
export function listCourseFee(query) {
  return request({
    url: '/business/course-bill/list',
    method: 'get',
    params: query
  })
}

// 查询托管费详细
export function getCourseFee(courseFeeId) {
  return request({
    url: '/business/course-bill/' + courseFeeId,
    method: 'get'
  })
}

// 托管费计算
export function calculateCourseFee(data) {
  return request({
    url: '/business/course-bill/calculate',
    method: 'post',
    data: data
  })
}

// 发送托管费账单
export function sendCourseFeeBill(courseFeeIds) {
  return request({
    url: '/business/course-bill/send',
    method: 'post',
    data: { courseFeeIds: courseFeeIds }
  })
}

// 导出托管费
export function exportCourseFee(query) {
  return request({
    url: '/business/course-bill/export',
    method: 'get',
    params: query
  })
}

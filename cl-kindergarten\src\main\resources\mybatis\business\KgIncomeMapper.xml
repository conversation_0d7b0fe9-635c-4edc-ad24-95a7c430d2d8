<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgIncomeMapper">
    
    <resultMap type="KgIncome" id="KgIncomeResult">
        <result property="incomeId"    column="income_id"    />
        <result property="incomeType"    column="income_type"    />
        <result property="incomeDate"    column="income_date"    />
        <result property="amount"    column="amount"    />
        <result property="payerId"    column="payer_id"    />
        <result property="payerName"    column="payer_name"    />
        <result property="paymentMethod"    column="payment_method"    />
        <result property="transactionNo"    column="transaction_no"    />
        <result property="description"    column="description"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgIncomeVo">
        select income_id, income_type, income_date, amount, payer_id, payer_name, payment_method, transaction_no, description, com_id, create_by, create_time, update_by, update_time, remark from kg_income
    </sql>

    <select id="selectKgIncomeList" parameterType="KgIncome" resultMap="KgIncomeResult">
        <include refid="selectKgIncomeVo"/>
        <where>  
            <if test="incomeType != null  and incomeType != ''"> and income_type = #{incomeType}</if>
            <if test="incomeDate != null "> and income_date = #{incomeDate}</if>
            <if test="amount != null "> and amount = #{amount}</if>
            <if test="payerId != null "> and payer_id = #{payerId}</if>
            <if test="payerName != null  and payerName != ''"> and payer_name like concat('%', #{payerName}, '%')</if>
            <if test="paymentMethod != null  and paymentMethod != ''"> and payment_method = #{paymentMethod}</if>
            <if test="transactionNo != null  and transactionNo != ''"> and transaction_no = #{transactionNo}</if>
            <if test="description != null  and description != ''"> and description = #{description}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgIncomeById" parameterType="Long" resultMap="KgIncomeResult">
        <include refid="selectKgIncomeVo"/>
        where income_id = #{incomeId}
    </select>
        
    <insert id="insertKgIncome" parameterType="KgIncome" useGeneratedKeys="true" keyProperty="incomeId">
        insert into kg_income
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="incomeType != null and incomeType != ''">income_type,</if>
            <if test="incomeDate != null">income_date,</if>
            <if test="amount != null">amount,</if>
            <if test="payerId != null">payer_id,</if>
            <if test="payerName != null">payer_name,</if>
            <if test="paymentMethod != null">payment_method,</if>
            <if test="transactionNo != null">transaction_no,</if>
            <if test="description != null">description,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="incomeType != null and incomeType != ''">#{incomeType},</if>
            <if test="incomeDate != null">#{incomeDate},</if>
            <if test="amount != null">#{amount},</if>
            <if test="payerId != null">#{payerId},</if>
            <if test="payerName != null">#{payerName},</if>
            <if test="paymentMethod != null">#{paymentMethod},</if>
            <if test="transactionNo != null">#{transactionNo},</if>
            <if test="description != null">#{description},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgIncome" parameterType="KgIncome">
        update kg_income
        <trim prefix="SET" suffixOverrides=",">
            <if test="incomeType != null and incomeType != ''">income_type = #{incomeType},</if>
            <if test="incomeDate != null">income_date = #{incomeDate},</if>
            <if test="amount != null">amount = #{amount},</if>
            <if test="payerId != null">payer_id = #{payerId},</if>
            <if test="payerName != null">payer_name = #{payerName},</if>
            <if test="paymentMethod != null">payment_method = #{paymentMethod},</if>
            <if test="transactionNo != null">transaction_no = #{transactionNo},</if>
            <if test="description != null">description = #{description},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where income_id = #{incomeId}
    </update>

    <delete id="deleteKgIncomeById" parameterType="Long">
        delete from kg_income where income_id = #{incomeId}
    </delete>

    <delete id="deleteKgIncomeByIds" parameterType="String">
        delete from kg_income where income_id in 
        <foreach item="incomeId" collection="array" open="(" separator="," close=")">
            #{incomeId}
        </foreach>
    </delete>
    
</mapper>
package com.cl.project.business.service.impl;

import java.util.List;
import java.util.Date;
import java.text.SimpleDateFormat;
import com.cl.common.utils.DateUtils;
import com.cl.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.cl.project.business.mapper.KgTeacherAttendanceMapper;
import com.cl.project.business.domain.KgTeacherAttendance;
import com.cl.project.business.domain.dto.TeacherAttendanceOverviewDto;
import com.cl.project.business.domain.dto.BatchTeacherCheckinDto;
import com.cl.project.business.service.IKgTeacherAttendanceService;

/**
 * 教师考勤记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgTeacherAttendanceServiceImpl implements IKgTeacherAttendanceService 
{
    @Autowired
    private KgTeacherAttendanceMapper kgTeacherAttendanceMapper;

    @Autowired
    private com.cl.project.business.mapper.KgDingtalkAttendanceMapper kgDingtalkAttendanceMapper;

    /**
     * 查询教师考勤记录
     * 
     * @param attendanceId 教师考勤记录ID
     * @return 教师考勤记录
     */
    @Override
    public KgTeacherAttendance selectKgTeacherAttendanceById(Long attendanceId)
    {
        return kgTeacherAttendanceMapper.selectKgTeacherAttendanceById(attendanceId);
    }

    /**
     * 查询教师考勤记录列表
     * 
     * @param kgTeacherAttendance 教师考勤记录
     * @return 教师考勤记录
     */
    @Override
    public List<KgTeacherAttendance> selectKgTeacherAttendanceList(KgTeacherAttendance kgTeacherAttendance)
    {
        return kgTeacherAttendanceMapper.selectKgTeacherAttendanceList(kgTeacherAttendance);
    }

    /**
     * 新增教师考勤记录
     * 
     * @param kgTeacherAttendance 教师考勤记录
     * @return 结果
     */
    @Override
    public int insertKgTeacherAttendance(KgTeacherAttendance kgTeacherAttendance)
    {
        kgTeacherAttendance.setCreateTime(DateUtils.getNowDate());
        return kgTeacherAttendanceMapper.insertKgTeacherAttendance(kgTeacherAttendance);
    }

    /**
     * 修改教师考勤记录
     * 
     * @param kgTeacherAttendance 教师考勤记录
     * @return 结果
     */
    @Override
    public int updateKgTeacherAttendance(KgTeacherAttendance kgTeacherAttendance)
    {
        kgTeacherAttendance.setUpdateTime(DateUtils.getNowDate());
        return kgTeacherAttendanceMapper.updateKgTeacherAttendance(kgTeacherAttendance);
    }

    /**
     * 批量删除教师考勤记录
     * 
     * @param attendanceIds 需要删除的教师考勤记录ID
     * @return 结果
     */
    @Override
    public int deleteKgTeacherAttendanceByIds(Long[] attendanceIds)
    {
        return kgTeacherAttendanceMapper.deleteKgTeacherAttendanceByIds(attendanceIds);
    }

    /**
     * 删除教师考勤记录信息
     * 
     * @param attendanceId 教师考勤记录ID
     * @return 结果
     */
    @Override
    public int deleteKgTeacherAttendanceById(Long attendanceId)
    {
        return kgTeacherAttendanceMapper.deleteKgTeacherAttendanceById(attendanceId);
    }

    /**
     * 查询教师考勤概览列表
     * 展示所有教师及其指定日期的考勤状态
     * 
     * @param attendanceDate 考勤日期，为null时查询当日
     * @param teacherName 教师名称
     * @param attendanceStatus 考勤状态
     * @param dataSource 数据来源
     * @return 教师考勤概览集合
     */
    @Override
    public List<TeacherAttendanceOverviewDto> selectTeacherAttendanceOverview(Date attendanceDate, String teacherName, String attendanceStatus, String dataSource)
    {
        // 如果未指定日期，默认查询当日
        if (attendanceDate == null) {
            attendanceDate = DateUtils.getNowDate();
        }
        List<TeacherAttendanceOverviewDto> overviewList = kgTeacherAttendanceMapper.selectTeacherAttendanceOverview(attendanceDate, teacherName, attendanceStatus, dataSource);
        if (overviewList == null || overviewList.isEmpty()) {
            return overviewList;
        }
        // 批量准备参数，避免N+1
        java.util.Map<Long, java.util.Date> teacherDateMap = new java.util.HashMap<>();
        for (TeacherAttendanceOverviewDto dto : overviewList) {
            if (dto.getTeacherId() != null && dto.getAttendanceDate() != null) {
                teacherDateMap.put(dto.getTeacherId(), dto.getAttendanceDate());
            }
        }
        // 查询所有教师当天的钉钉打卡记录和手动签到记录
        for (TeacherAttendanceOverviewDto dto : overviewList) {
            Long teacherId = dto.getTeacherId();
            java.util.Date date = dto.getAttendanceDate();
            if (teacherId != null && date != null) {
                java.util.Date dateFrom = com.cl.common.utils.DateUtils.getDayStart(date);
                java.util.Date dateTo = com.cl.common.utils.DateUtils.getDayEnd(date);
                // 钉钉打卡明细
                java.util.List<com.cl.project.business.domain.KgDingtalkAttendance> records = kgDingtalkAttendanceMapper.selectByEmployeeIdAndDate(teacherId, dateFrom, dateTo);
                dto.setDingtalkRecords(records);
            // 根据钉钉打卡记录设置考勤状态：有上班和下班记录为出勤(1)，否则为缺勤(3)
            if (records != null && !records.isEmpty()) {
                boolean hasOnDuty = records.stream()
                        .anyMatch(r -> "OnDuty".equals(r.getCheckType()));
                boolean hasOffDuty = records.stream()
                        .anyMatch(r -> "OffDuty".equals(r.getCheckType()));
                // 计算工作时长
                java.util.Optional<com.cl.project.business.domain.KgDingtalkAttendance> onDutyOpt = records.stream()
                        .filter(r -> "OnDuty".equals(r.getCheckType()))
                        .min(java.util.Comparator.comparing(com.cl.project.business.domain.KgDingtalkAttendance::getCheckTime));
                java.util.Optional<com.cl.project.business.domain.KgDingtalkAttendance> offDutyOpt = records.stream()
                        .filter(r -> "OffDuty".equals(r.getCheckType()))
                        .max(java.util.Comparator.comparing(com.cl.project.business.domain.KgDingtalkAttendance::getCheckTime));
                if (onDutyOpt.isPresent() && offDutyOpt.isPresent()) {
                    java.util.Date onDuty = onDutyOpt.get().getCheckTime();
                    java.util.Date offDuty = offDutyOpt.get().getCheckTime();
                    long durationMillis = offDuty.getTime() - onDuty.getTime();
                    if (durationMillis > 0) {
                        java.math.BigDecimal hours = new java.math.BigDecimal(durationMillis / (1000.0 * 60 * 60));
                        dto.setWorkHours(hours.setScale(1, java.math.RoundingMode.HALF_UP));
                    } else {
                        dto.setWorkHours(java.math.BigDecimal.ZERO);
                    }
                } else {
                    dto.setWorkHours(java.math.BigDecimal.ZERO);
                }
                if (hasOnDuty && hasOffDuty) {
                    dto.setAttendanceStatus("1"); // 出勤
                } else {
                    dto.setAttendanceStatus("3"); // 缺勤
                }
            } else {
                dto.setAttendanceStatus("3"); // 无打卡记录，默认缺勤
                dto.setWorkHours(java.math.BigDecimal.ZERO);
            }
                // 手动签到明细
                java.util.List<com.cl.project.business.domain.KgTeacherAttendance> manualList = kgTeacherAttendanceMapper.selectManualByTeacherAndDate(teacherId, dateFrom, dateTo);
                dto.setManualRecords(manualList);
                // 设置概览行的isConfirmed：全部手动签到都已确认为1，否则为0
                if (manualList != null && !manualList.isEmpty() && manualList.stream().allMatch(m -> m.getIsConfirmed() != null && m.getIsConfirmed() == 1L)) {
                    dto.setIsConfirmed(1L);
                } else {
                    dto.setIsConfirmed(0L);
                }
            // 设置checkInTime为manualList中最新签到时间
            if (manualList != null && !manualList.isEmpty()) {
                java.util.Date latestCheckInTime = manualList.stream()
                        .map(com.cl.project.business.domain.KgTeacherAttendance::getCheckInTime)
                        .filter(java.util.Objects::nonNull)
                        .max(java.util.Date::compareTo)
                        .orElse(null);
                dto.setCheckInTime(latestCheckInTime);
            }
            } else {
                dto.setDingtalkRecords(java.util.Collections.emptyList());
                dto.setManualRecords(java.util.Collections.emptyList());
            }
        }
        return overviewList;
    }


    /**
     * 批量确认教师考勤
     */
    @Override
    @Transactional
    public int batchConfirmAttendance(com.cl.project.business.domain.dto.BatchConfirmAttendanceDto dto) {
        if (dto == null || dto.getAttendanceIds() == null || dto.getAttendanceIds().isEmpty()) {
            return 0;
        }
        // 直接调用Mapper批量更新
        return kgTeacherAttendanceMapper.batchConfirmAttendance(dto.getAttendanceIds(), dto.getConfirmedBy());
    }

        /**
     * 批量教师签到
     * 支持一天多次签到，每次都创建新记录
     * 
     * @param batchDto 批量签到数据
     * @return 成功处理的记录数
     */
    @Override
    @Transactional
    public int batchTeacherCheckin(BatchTeacherCheckinDto batchDto)
    {
        int successCount = 0;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        
        try {
            Date attendanceDate = sdf.parse(batchDto.getAttendanceDate());
            String currentUser = SecurityUtils.getUsername();
            Date currentTime = DateUtils.getNowDate();
            
            for (Long teacherId : batchDto.getTeacherIds()) {
                // 每次签到都创建新记录，支持一天多次签到
                KgTeacherAttendance newRecord = new KgTeacherAttendance();
                newRecord.setTeacherId(teacherId);
                newRecord.setAttendanceDate(attendanceDate);
                newRecord.setAttendanceStatus(batchDto.getAttendanceStatus());
                
                // 设置签到时间和方式
                if ("1".equals(batchDto.getAttendanceStatus())) { // 签到
                    newRecord.setCheckInTime(currentTime);
                    newRecord.setCheckInMethod("manual");
                } else if ("3".equals(batchDto.getAttendanceStatus()) || "4".equals(batchDto.getAttendanceStatus())) {
                    // 缺勤或请假不需要设置签到时间
                    newRecord.setCheckInTime(null);
                    newRecord.setCheckInMethod(null);
                }
                
                newRecord.setRemark(batchDto.getRemark());
                newRecord.setCreateBy(currentUser);
                newRecord.setCreateTime(currentTime);
                
                if (kgTeacherAttendanceMapper.insertKgTeacherAttendance(newRecord) > 0) {
                    successCount++;
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("批量签到操作失败: " + e.getMessage(), e);
        }
        
        return successCount;
    }
    

}

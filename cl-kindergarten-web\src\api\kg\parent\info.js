import request from '@/utils/request'

// 查询家长信息列表
export function listParent(query) {
  return request({
    url: '/business/parent/list',
    method: 'get',
    params: query
  })
}

// 查询家长信息详细
export function getParent(parentId) {
  return request({
    url: '/business/parent/' + parentId,
    method: 'get'
  })
}

// 新增家长信息
export function addParent(data) {
  return request({
    url: '/business/parent',
    method: 'post',
    data: data
  })
}

// 修改家长信息
export function updateParent(data) {
  return request({
    url: '/business/parent',
    method: 'put',
    data: data
  })
}

// 删除家长信息
export function delParent(parentId) {
  return request({
    url: '/business/parent/' + parentId,
    method: 'delete'
  })
}

// 导出家长信息
export function exportParent(query) {
  return request({
    url: '/business/parent/export',
    method: 'get',
    params: query
  })
}

// 获取家长的子女信息
export function getParentChildren(parentId) {
  return request({
    url: '/business/parent/' + parentId + '/children',
    method: 'get'
  })
}

// 获取家长费用统计
export function getParentFeeStats(parentId) {
  return request({
    url: '/business/parent/' + parentId + '/feeStats',
    method: 'get'
  })
}

// 获取家长消息记录
export function getParentMessages(parentId, query) {
  return request({
    url: '/business/parent/' + parentId + '/messages',
    method: 'get',
    params: query
  })
}

// 获取家长缴费记录
export function getParentPayments(parentId, query) {
  return request({
    url: '/business/parent/' + parentId + '/payments',
    method: 'get',
    params: query
  })
}

// 微信绑定二维码
export function getWechatBindQRCode(parentId) {
  return request({
    url: '/business/parent/' + parentId + '/wechatQRCode',
    method: 'get'
  })
}

// 解除微信绑定
export function unbindWechat(parentId) {
  return request({
    url: '/business/parent/' + parentId + '/unbindWechat',
    method: 'post'
  })
}

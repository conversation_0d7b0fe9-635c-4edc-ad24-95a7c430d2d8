package com.cl.project.business.service;

import java.util.List;
import com.cl.project.business.domain.KgCourseBillDetail;

/**
 * 托管费账单明细Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IKgCourseBillDetailService 
{
    /**
     * 查询托管费账单明细
     * 
     * @param detailId 托管费账单明细ID
     * @return 托管费账单明细
     */
    public KgCourseBillDetail selectKgCourseBillDetailById(Long detailId);

    /**
     * 查询托管费账单明细列表
     * 
     * @param kgCourseBillDetail 托管费账单明细
     * @return 托管费账单明细集合
     */
    public List<KgCourseBillDetail> selectKgCourseBillDetailList(KgCourseBillDetail kgCourseBillDetail);

    /**
     * 新增托管费账单明细
     * 
     * @param kgCourseBillDetail 托管费账单明细
     * @return 结果
     */
    public int insertKgCourseBillDetail(KgCourseBillDetail kgCourseBillDetail);

    /**
     * 修改托管费账单明细
     * 
     * @param kgCourseBillDetail 托管费账单明细
     * @return 结果
     */
    public int updateKgCourseBillDetail(KgCourseBillDetail kgCourseBillDetail);

    /**
     * 批量删除托管费账单明细
     * 
     * @param detailIds 需要删除的托管费账单明细ID
     * @return 结果
     */
    public int deleteKgCourseBillDetailByIds(Long[] detailIds);

    /**
     * 删除托管费账单明细信息
     * 
     * @param detailId 托管费账单明细ID
     * @return 结果
     */
    public int deleteKgCourseBillDetailById(Long detailId);
}

-- =====================================================
-- 幼儿园管理系统数据库结构 - 第三部分
-- 工资管理、财务管理、库存管理表
-- =====================================================

-- =====================================================
-- 5. 工资管理表
-- =====================================================

-- 5.1 工资配置表
-- 功能: 配置工资计算规则，包括各种奖励和扣款标准
-- 关联关系:
--   - kg_teacher_salary (工资计算依据)
DROP TABLE IF EXISTS `kg_salary_config`;
CREATE TABLE `kg_salary_config` (
  `config_id` bigint NOT NULL AUTO_INCREMENT COMMENT '配置ID',
  `config_name` varchar(100) NOT NULL COMMENT '配置名称',
  `config_type` varchar(50) NOT NULL COMMENT '配置类型（attendance_bonus出勤奖励、course_bonus课时奖励、enrollment_bonus报名奖励等）',
  `class_type` varchar(20) COMMENT '班级类型（托班、小班、中班、大班）',
  `threshold_value` decimal(10,2) COMMENT '阈值',
  `bonus_amount` decimal(10,2) DEFAULT 0.00 COMMENT '奖励金额',
  `penalty_amount` decimal(10,2) DEFAULT 0.00 COMMENT '扣除金额',
  `calculation_rule` text COMMENT '计算规则说明',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`config_id`),
  KEY `idx_config_type` (`config_type`),
  KEY `idx_com_id` (`com_id`),
  KEY `idx_class_type` (`class_type`)
) ENGINE=InnoDB COMMENT='工资配置表';

-- 5.2 教师工资表
-- 功能: 记录教师每月工资详情，包括各项奖励和扣款
-- 关联关系:
--   - kg_teacher.teacher_id (教师信息)
--   - kg_teacher_attendance (考勤统计)
--   - kg_course_attendance (课时统计)
--   - kg_student_attendance (班级出勤率)
--   - kg_salary_config (计算规则)
--   - kg_expense (支出记录)
DROP TABLE IF EXISTS `kg_teacher_salary`;
CREATE TABLE `kg_teacher_salary` (
  `salary_id` bigint NOT NULL AUTO_INCREMENT COMMENT '工资ID',
  `teacher_id` bigint NOT NULL COMMENT '教师ID，关联kg_teacher.teacher_id',
  `salary_year` int NOT NULL COMMENT '工资年份',
  `salary_month` int NOT NULL COMMENT '工资月份',
  `base_salary` decimal(10,2) DEFAULT 0.00 COMMENT '基本工资',
  `attendance_days` int DEFAULT 0 COMMENT '出勤天数',
  `total_work_days` int DEFAULT 0 COMMENT '应出勤天数',
  `attendance_bonus` decimal(10,2) DEFAULT 0.00 COMMENT '满勤奖',
  `course_bonus` decimal(10,2) DEFAULT 0.00 COMMENT '课时费',
  `enrollment_bonus` decimal(10,2) DEFAULT 0.00 COMMENT '报名奖励',
  `attendance_rate_bonus` decimal(10,2) DEFAULT 0.00 COMMENT '出勤率奖励',
  `new_student_bonus` decimal(10,2) DEFAULT 0.00 COMMENT '新生奖励',
  `withdrawal_penalty` decimal(10,2) DEFAULT 0.00 COMMENT '退园扣款',
  `social_insurance` decimal(10,2) DEFAULT 0.00 COMMENT '社保代扣',
  `performance_score` decimal(5,2) DEFAULT 0.00 COMMENT '绩效积分',
  `other_bonus` decimal(10,2) DEFAULT 0.00 COMMENT '其他奖励',
  `other_deduction` decimal(10,2) DEFAULT 0.00 COMMENT '其他扣款',
  `gross_salary` decimal(10,2) DEFAULT 0.00 COMMENT '应发工资',
  `net_salary` decimal(10,2) DEFAULT 0.00 COMMENT '实发工资',
  `salary_status` varchar(20) DEFAULT 'calculated' COMMENT '工资状态（calculated已计算、confirmed已确认、paid已发放）',
  `confirmed_by` bigint COMMENT '确认人ID，关联kg_teacher.teacher_id',
  `confirmed_time` datetime COMMENT '确认时间',
  `paid_time` datetime COMMENT '发放时间',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`salary_id`),
  UNIQUE KEY `uk_teacher_month` (`teacher_id`, `salary_year`, `salary_month`),
  KEY `idx_com_id` (`com_id`),
  KEY `idx_salary_status` (`salary_status`)
) ENGINE=InnoDB COMMENT='教师工资表';

-- =====================================================
-- 6. 财务管理表
-- =====================================================

-- 6.1 收入记录表
-- 功能: 记录幼儿园各项收入，包括园费、托管费等
-- 关联关系:
--   - kg_student.student_id (付款学生)
--   - kg_tuition_bill (园费收入)
--   - kg_course_bill (托管费收入)
DROP TABLE IF EXISTS `kg_income`;
CREATE TABLE `kg_income` (
  `income_id` bigint NOT NULL AUTO_INCREMENT COMMENT '收入ID',
  `income_type` varchar(50) NOT NULL COMMENT '收入类型（tuition园费、course托管费、other其他）',
  `income_date` date NOT NULL COMMENT '收入日期',
  `amount` decimal(12,2) NOT NULL COMMENT '收入金额',
  `payer_id` bigint COMMENT '付款人ID（学生ID），关联kg_student.student_id',
  `payer_name` varchar(50) COMMENT '付款人姓名',
  `payment_method` varchar(20) DEFAULT 'wechat' COMMENT '支付方式（wechat微信、alipay支付宝、cash现金、bank银行转账）',
  `transaction_no` varchar(100) COMMENT '交易流水号',
  `description` varchar(200) COMMENT '收入描述',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`income_id`),
  KEY `idx_income_type_date` (`income_type`, `income_date`),
  KEY `idx_payer_id` (`payer_id`),
  KEY `idx_com_id` (`com_id`),
  KEY `idx_payment_method` (`payment_method`)
) ENGINE=InnoDB COMMENT='收入记录表';

-- 6.2 支出类型表
-- 功能: 定义支出类型的层级结构，支持自定义扩展
-- 关联关系:
--   - kg_expense.expense_type_id (支出记录)
DROP TABLE IF EXISTS `kg_expense_type`;
CREATE TABLE `kg_expense_type` (
  `type_id` bigint NOT NULL AUTO_INCREMENT COMMENT '类型ID',
  `type_name` varchar(50) NOT NULL COMMENT '支出类型名称',
  `type_code` varchar(20) NOT NULL COMMENT '类型编码',
  `parent_id` bigint DEFAULT 0 COMMENT '父类型ID',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `is_system` tinyint DEFAULT 0 COMMENT '是否系统预设（0否 1是）',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`type_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_com_id` (`com_id`),
  KEY `idx_type_code` (`type_code`)
) ENGINE=InnoDB COMMENT='支出类型表';

-- 6.3 支出记录表
-- 功能: 记录幼儿园各项支出，支持审批流程
-- 关联关系:
--   - kg_expense_type.type_id (支出类型)
--   - kg_teacher.teacher_id (审批人)
--   - kg_teacher_salary (工资支出)
DROP TABLE IF EXISTS `kg_expense`;
CREATE TABLE `kg_expense` (
  `expense_id` bigint NOT NULL AUTO_INCREMENT COMMENT '支出ID',
  `expense_type_id` bigint NOT NULL COMMENT '支出类型ID，关联kg_expense_type.type_id',
  `expense_date` date NOT NULL COMMENT '支出日期',
  `amount` decimal(12,2) NOT NULL COMMENT '支出金额',
  `payee` varchar(100) COMMENT '收款方',
  `payment_method` varchar(20) DEFAULT 'cash' COMMENT '支付方式（wechat微信、alipay支付宝、cash现金、bank银行转账）',
  `invoice_no` varchar(100) COMMENT '发票号码',
  `description` varchar(200) COMMENT '支出描述',
  `approver_id` bigint COMMENT '审批人ID，关联kg_teacher.teacher_id',
  `approval_status` varchar(20) DEFAULT 'pending' COMMENT '审批状态（pending待审批、approved已审批、rejected已拒绝）',
  `approval_time` datetime COMMENT '审批时间',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`expense_id`),
  KEY `idx_expense_type_date` (`expense_type_id`, `expense_date`),
  KEY `idx_com_id` (`com_id`),
  KEY `idx_approval_status` (`approval_status`)
) ENGINE=InnoDB COMMENT='支出记录表';

-- =====================================================
-- 7. 库存管理表
-- =====================================================

-- 7.1 物品类别表
-- 功能: 定义物品类别的层级结构
-- 关联关系:
--   - kg_item.category_id (物品信息)
DROP TABLE IF EXISTS `kg_item_category`;
CREATE TABLE `kg_item_category` (
  `category_id` bigint NOT NULL AUTO_INCREMENT COMMENT '类别ID',
  `category_name` varchar(50) NOT NULL COMMENT '类别名称',
  `category_code` varchar(20) NOT NULL COMMENT '类别编码',
  `parent_id` bigint DEFAULT 0 COMMENT '父类别ID',
  `sort_order` int DEFAULT 0 COMMENT '排序',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`category_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_com_id` (`com_id`),
  KEY `idx_category_code` (`category_code`)
) ENGINE=InnoDB COMMENT='物品类别表';

-- 7.2 物品信息表
-- 功能: 存储物品基本信息和库存状态
-- 关联关系:
--   - kg_item_category.category_id (物品类别)
--   - kg_stock_record.item_id (库存变动)
DROP TABLE IF EXISTS `kg_item`;
CREATE TABLE `kg_item` (
  `item_id` bigint NOT NULL AUTO_INCREMENT COMMENT '物品ID',
  `item_code` varchar(50) NOT NULL COMMENT '物品编码',
  `item_name` varchar(100) NOT NULL COMMENT '物品名称',
  `category_id` bigint NOT NULL COMMENT '类别ID，关联kg_item_category.category_id',
  `specification` varchar(100) COMMENT '规格型号',
  `unit` varchar(20) DEFAULT '个' COMMENT '计量单位',
  `unit_price` decimal(10,2) DEFAULT 0.00 COMMENT '单价',
  `min_stock` int DEFAULT 0 COMMENT '最低库存',
  `max_stock` int DEFAULT 0 COMMENT '最高库存',
  `current_stock` int DEFAULT 0 COMMENT '当前库存',
  `status` char(1) DEFAULT '0' COMMENT '状态（0正常 1停用）',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`item_id`),
  UNIQUE KEY `uk_item_code` (`item_code`, `com_id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_com_id` (`com_id`)
) ENGINE=InnoDB COMMENT='物品信息表';

-- 7.3 库存变动记录表
-- 功能: 记录物品的入库、出库、调整等操作
-- 关联关系:
--   - kg_item.item_id (物品信息)
--   - kg_teacher.teacher_id (操作员)
--   - kg_expense (采购支出)
DROP TABLE IF EXISTS `kg_stock_record`;
CREATE TABLE `kg_stock_record` (
  `record_id` bigint NOT NULL AUTO_INCREMENT COMMENT '记录ID',
  `item_id` bigint NOT NULL COMMENT '物品ID，关联kg_item.item_id',
  `record_type` varchar(20) NOT NULL COMMENT '变动类型（in入库、out出库、adjust调整）',
  `record_date` datetime NOT NULL COMMENT '变动时间',
  `quantity` int NOT NULL COMMENT '变动数量',
  `unit_price` decimal(10,2) DEFAULT 0.00 COMMENT '单价',
  `total_amount` decimal(12,2) DEFAULT 0.00 COMMENT '总金额',
  `before_stock` int DEFAULT 0 COMMENT '变动前库存',
  `after_stock` int DEFAULT 0 COMMENT '变动后库存',
  `supplier` varchar(100) COMMENT '供应商',
  `recipient` varchar(50) COMMENT '领用人',
  `purpose` varchar(200) COMMENT '用途说明',
  `operator_id` bigint COMMENT '操作员ID，关联kg_teacher.teacher_id',
  `com_id` varchar(20) COMMENT '公司ID，多租户隔离',
  `create_by` varchar(64) DEFAULT '' COMMENT '创建者',
  `create_time` datetime DEFAULT NULL COMMENT '创建时间',
  `update_by` varchar(64) DEFAULT '' COMMENT '更新者',
  `update_time` datetime DEFAULT NULL COMMENT '更新时间',
  `remark` varchar(500) DEFAULT NULL COMMENT '备注',
  PRIMARY KEY (`record_id`),
  KEY `idx_item_id_date` (`item_id`, `record_date`),
  KEY `idx_record_type_date` (`record_type`, `record_date`),
  KEY `idx_com_id` (`com_id`)
) ENGINE=InnoDB COMMENT='库存变动记录表';

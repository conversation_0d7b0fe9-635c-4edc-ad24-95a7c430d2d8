import request from '@/utils/request'

// 查询消息推送记录列表
export function listPush(query) {
  return request({
    url: '/business/push/list',
    method: 'get',
    params: query
  })
}

// 查询消息推送记录详细
export function getPush(pushId) {
  return request({
    url: '/business/push/' + pushId,
    method: 'get'
  })
}

// 新增消息推送记录
export function addPush(data) {
  return request({
    url: '/business/push',
    method: 'post',
    data: data
  })
}

// 修改消息推送记录
export function updatePush(data) {
  return request({
    url: '/business/push',
    method: 'put',
    data: data
  })
}

// 删除消息推送记录
export function delPush(pushId) {
  return request({
    url: '/business/push/' + pushId,
    method: 'delete'
  })
}

// 导出消息推送记录
export function exportPush(query) {
  return request({
    url: '/business/push/export',
    method: 'get',
    params: query
  })
}

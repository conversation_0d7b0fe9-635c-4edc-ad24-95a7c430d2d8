package com.cl.project.business.controller;

import java.util.List;
import java.util.Map;
import java.time.LocalDate;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.service.IKgAttendanceStatisticsService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 考勤统计Controller
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@RestController
@RequestMapping("/business/attendance-statistics")
public class KgAttendanceStatisticsController extends BaseController
{
    @Autowired
    private IKgAttendanceStatisticsService attendanceStatisticsService;

    /**
     * 获取学生考勤统计
     */
    @SaCheckPermission("kg:attendance:statistics:view")
    @GetMapping("/student")
    public AjaxResult getStudentAttendanceStatistics(
            @RequestParam(required = false) Long classId,
            @RequestParam(required = false) Long studentId,
            @RequestParam String startDate,
            @RequestParam String endDate)
    {
        Map<String, Object> statistics = attendanceStatisticsService.getStudentAttendanceStatistics(
                classId, studentId, LocalDate.parse(startDate), LocalDate.parse(endDate));
        return AjaxResult.success(statistics);
    }

    /**
     * 获取教师考勤统计
     */
    @SaCheckPermission("kg:attendance:statistics:view")
    @GetMapping("/teacher")
    public AjaxResult getTeacherAttendanceStatistics(
            @RequestParam(required = false) Long teacherId,
            @RequestParam String startDate,
            @RequestParam String endDate)
    {
        Map<String, Object> statistics = attendanceStatisticsService.getTeacherAttendanceStatistics(
                teacherId, LocalDate.parse(startDate), LocalDate.parse(endDate));
        return AjaxResult.success(statistics);
    }

    /**
     * 获取班级考勤统计
     */
    @SaCheckPermission("kg:attendance:statistics:view")
    @GetMapping("/class")
    public AjaxResult getClassAttendanceStatistics(
            @RequestParam Long classId,
            @RequestParam String startDate,
            @RequestParam String endDate)
    {
        Map<String, Object> statistics = attendanceStatisticsService.getClassAttendanceStatistics(
                classId, LocalDate.parse(startDate), LocalDate.parse(endDate));
        return AjaxResult.success(statistics);
    }

    /**
     * 获取考勤汇总报表
     */
    @SaCheckPermission("kg:attendance:statistics:report")
    @GetMapping("/summary")
    public AjaxResult getAttendanceSummary(
            @RequestParam String startDate,
            @RequestParam String endDate)
    {
        Map<String, Object> summary = attendanceStatisticsService.getAttendanceSummary(
                LocalDate.parse(startDate), LocalDate.parse(endDate));
        return AjaxResult.success(summary);
    }

    /**
     * 批量确认考勤记录
     */
    @SaCheckPermission("kg:attendance:student:confirm")
    @Log(title = "批量确认考勤", businessType = BusinessType.UPDATE)
    @PostMapping("/confirm-batch")
    public AjaxResult confirmAttendanceBatch(@RequestParam List<Long> attendanceIds)
    {
        int result = attendanceStatisticsService.confirmAttendanceBatch(attendanceIds);
        return toAjax(result);
    }

    /**
     * 生成月度考勤报表
     */
    @SaCheckPermission("kg:attendance:statistics:report")
    @Log(title = "生成月度考勤报表", businessType = BusinessType.EXPORT)
    @PostMapping("/monthly-report")
    public AjaxResult generateMonthlyReport(
            @RequestParam Integer year,
            @RequestParam Integer month)
    {
        String reportPath = attendanceStatisticsService.generateMonthlyReport(year, month);
        return AjaxResult.success("报表生成成功", reportPath);
    }

    /**
     * 考勤异常处理
     */
    @SaCheckPermission("kg:attendance:student:checkin")
    @Log(title = "考勤异常处理", businessType = BusinessType.UPDATE)
    @PostMapping("/handle-exception")
    public AjaxResult handleAttendanceException(
            @RequestParam Long attendanceId,
            @RequestParam String exceptionType,
            @RequestParam String reason)
    {
        int result = attendanceStatisticsService.handleAttendanceException(attendanceId, exceptionType, reason);
        return toAjax(result);
    }
}

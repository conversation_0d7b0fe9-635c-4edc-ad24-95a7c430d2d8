package com.cl.project.business.mapper;

import java.util.List;
import com.cl.project.business.domain.KgMobilePermissionConfig;

/**
 * 移动端权限配置Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface KgMobilePermissionConfigMapper 
{
    /**
     * 查询移动端权限配置
     * 
     * @param configId 移动端权限配置ID
     * @return 移动端权限配置
     */
    public KgMobilePermissionConfig selectKgMobilePermissionConfigById(Long configId);

    /**
     * 查询移动端权限配置列表
     * 
     * @param kgMobilePermissionConfig 移动端权限配置
     * @return 移动端权限配置集合
     */
    public List<KgMobilePermissionConfig> selectKgMobilePermissionConfigList(KgMobilePermissionConfig kgMobilePermissionConfig);

    /**
     * 新增移动端权限配置
     * 
     * @param kgMobilePermissionConfig 移动端权限配置
     * @return 结果
     */
    public int insertKgMobilePermissionConfig(KgMobilePermissionConfig kgMobilePermissionConfig);

    /**
     * 修改移动端权限配置
     * 
     * @param kgMobilePermissionConfig 移动端权限配置
     * @return 结果
     */
    public int updateKgMobilePermissionConfig(KgMobilePermissionConfig kgMobilePermissionConfig);

    /**
     * 删除移动端权限配置
     * 
     * @param configId 移动端权限配置ID
     * @return 结果
     */
    public int deleteKgMobilePermissionConfigById(Long configId);

    /**
     * 批量删除移动端权限配置
     * 
     * @param configIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteKgMobilePermissionConfigByIds(Long[] configIds);
}

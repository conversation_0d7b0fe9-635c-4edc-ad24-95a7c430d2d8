import request from '@/utils/request'

// 查询支出类型列表
export function listType(query) {
  return request({
    url: '/business/type/list',
    method: 'get',
    params: query
  })
}

// 查询支出类型详细
export function getType(typeId) {
  return request({
    url: '/business/type/' + typeId,
    method: 'get'
  })
}

// 新增支出类型
export function addType(data) {
  return request({
    url: '/business/type',
    method: 'post',
    data: data
  })
}

// 修改支出类型
export function updateType(data) {
  return request({
    url: '/business/type',
    method: 'put',
    data: data
  })
}

// 删除支出类型
export function delType(typeId) {
  return request({
    url: '/business/type/' + typeId,
    method: 'delete'
  })
}

// 导出支出类型
export function exportType(query) {
  return request({
    url: '/business/type/export',
    method: 'get',
    params: query
  })
}

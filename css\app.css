/* 整合页面样式 */

/* 应用容器 */
.app-container {
    position: relative;
    width: 100%;
    height: 100vh;
    overflow: hidden;
}

/* 页面样式 */
.page {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow-y: auto;
    transition: transform 0.3s ease-in-out;
    background: #fff;
}

.page.slide-left {
    transform: translateX(-100%);
}

.page.slide-right {
    transform: translateX(100%);
}

/* 导航动画 */
.page-transition {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 管理功能区域 */
.management-section {
    padding: 30px 20px;
    background: #f8f9fa;
    min-height: calc(100vh - 120px);
}

.management-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-top: 20px;
}

.management-item {
    background: white;
    border-radius: 12px;
    padding: 30px 20px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    cursor: pointer;
    transition: all 0.3s ease;
    border: 2px solid transparent;
}

.management-item:hover {
    transform: translateY(-3px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
    border-color: #4CAF50;
}

.management-icon {
    font-size: 40px;
    margin-bottom: 15px;
    display: block;
}

.management-item span {
    font-size: 16px;
    font-weight: 500;
    color: #333;
}

/* 头部按钮样式 */
.header-btn {
    background: rgba(255,255,255,0.2);
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.header-btn:hover {
    background: rgba(255,255,255,0.3);
}

/* 搜索区域 */
.search-section {
    padding: 15px 20px;
    background: white;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    gap: 10px;
}

.search-input {
    flex: 1;
    padding: 10px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
}

.search-input:focus {
    outline: none;
    border-color: #4CAF50;
}

.search-btn {
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 15px;
    cursor: pointer;
    font-size: 16px;
}

/* 学生列表 */
.student-list-section {
    padding: 20px;
    background: #f8f9fa;
    min-height: calc(100vh - 200px);
}

.student-item-card {
    background: white;
    border-radius: 12px;
    padding: 15px 20px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.student-item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.student-item-card .student-avatar {
    font-size: 24px;
    width: 50px;
    height: 50px;
    background: #e3f2fd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.student-details {
    flex: 1;
}

.student-details h4 {
    font-size: 16px;
    margin-bottom: 5px;
    color: #333;
}

.student-details p {
    font-size: 12px;
    color: #666;
    margin-bottom: 3px;
}

.student-actions {
    display: flex;
    gap: 8px;
}

.action-btn {
    padding: 6px 12px;
    border: none;
    border-radius: 6px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.action-btn.edit {
    background: #2196F3;
    color: white;
}

.action-btn.edit:hover {
    background: #1976D2;
}

.action-btn.delete {
    background: #f44336;
    color: white;
}

.action-btn.delete:hover {
    background: #d32f2f;
}

/* 预存款余额样式 */
.balance {
    color: #4CAF50;
    font-weight: 600;
}

.balance.low-balance {
    color: #f44336;
}

.action-btn.recharge {
    background: #FF9800;
    color: white;
}

.action-btn.recharge:hover {
    background: #F57C00;
}

/* 月份选择器 */
.month-selector {
    padding: 15px 20px;
    background: white;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.month-btn {
    background: #f5f5f5;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 18px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.month-btn:hover {
    background: #e0e0e0;
}

/* 财务概览 */
.finance-summary {
    padding: 20px;
    background: #f8f9fa;
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
}

.summary-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.summary-card h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 10px;
}

.amount {
    font-size: 18px;
    font-weight: 600;
}

.amount.income {
    color: #4CAF50;
}

.amount.expense {
    color: #f44336;
}

.amount.profit {
    color: #2196F3;
}

/* 重新核算按钮 */
.recalculate-section {
    padding: 20px;
    text-align: center;
}

.btn-recalculate {
    background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
    color: white;
    border: none;
    border-radius: 12px;
    padding: 15px 30px;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
}

.btn-recalculate:hover {
    transform: translateY(-2px);
    box-shadow: 0 6px 20px rgba(255, 152, 0, 0.4);
}

/* 账单列表 */
.bill-list-section {
    padding: 20px;
    background: #f8f9fa;
}

.bill-item {
    background: white;
    border-radius: 12px;
    padding: 15px 20px;
    margin-bottom: 15px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.bill-info h4 {
    font-size: 16px;
    margin-bottom: 5px;
    color: #333;
}

.bill-info p {
    font-size: 12px;
    color: #666;
    margin-bottom: 3px;
}

.bill-total {
    font-weight: 600;
    color: #333;
}

.bill-status {
    padding: 6px 12px;
    border-radius: 12px;
    font-size: 12px;
    font-weight: 600;
    white-space: nowrap;
}

.bill-status.paid {
    background: #e8f5e8;
    color: #4CAF50;
}

.bill-status.unpaid {
    background: #ffebee;
    color: #f44336;
}

/* 课程管理样式 */
.course-list-section {
    padding: 20px;
    background: #f8f9fa;
}

.course-item-card {
    background: white;
    border-radius: 12px;
    padding: 15px 20px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.course-item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.course-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    background: #e3f2fd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.course-details {
    flex: 1;
}

.course-details h4 {
    font-size: 16px;
    margin-bottom: 5px;
    color: #333;
}

.course-details p {
    font-size: 12px;
    color: #666;
    margin-bottom: 3px;
}

.course-actions {
    display: flex;
    gap: 8px;
}

.action-btn.view {
    background: #9C27B0;
    color: white;
}

.action-btn.view:hover {
    background: #7B1FA2;
}

/* 教师管理样式 */
.teacher-list-section {
    padding: 20px;
    background: #f8f9fa;
}

.teacher-item-card {
    background: white;
    border-radius: 12px;
    padding: 15px 20px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.teacher-item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.teacher-avatar {
    font-size: 32px;
    width: 60px;
    height: 60px;
    background: #fff3e0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.teacher-details {
    flex: 1;
}

.teacher-details h4 {
    font-size: 16px;
    margin-bottom: 5px;
    color: #333;
}

.teacher-details p {
    font-size: 12px;
    color: #666;
    margin-bottom: 3px;
}

.teacher-actions {
    display: flex;
    gap: 8px;
}

.action-btn.salary {
    background: #4CAF50;
    color: white;
}

.action-btn.salary:hover {
    background: #45a049;
}

/* 库存管理样式 */
.inventory-tabs, .user-tabs {
    padding: 15px 20px;
    background: white;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    gap: 10px;
}

.tab-btn {
    background: #f5f5f5;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn.active {
    background: #4CAF50;
    color: white;
}

.inventory-list-section {
    padding: 20px;
    background: #f8f9fa;
}

.inventory-item-card {
    background: white;
    border-radius: 12px;
    padding: 15px 20px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.inventory-item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.inventory-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    background: #f3e5f5;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.inventory-details {
    flex: 1;
}

.inventory-details h4 {
    font-size: 16px;
    margin-bottom: 5px;
    color: #333;
}

.inventory-details p {
    font-size: 12px;
    color: #666;
    margin-bottom: 3px;
}

.stock-info {
    color: #4CAF50;
    font-weight: 600;
}

.stock-info.low-stock {
    color: #f44336;
}

.inventory-actions {
    display: flex;
    gap: 8px;
}

.action-btn.in {
    background: #4CAF50;
    color: white;
}

.action-btn.in:hover {
    background: #45a049;
}

.action-btn.out {
    background: #FF9800;
    color: white;
}

.action-btn.out:hover {
    background: #F57C00;
}

/* 用户管理样式 */
.user-list-section {
    padding: 20px;
    background: #f8f9fa;
}

.user-item-card {
    background: white;
    border-radius: 12px;
    padding: 15px 20px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.user-item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.user-avatar {
    font-size: 32px;
    width: 60px;
    height: 60px;
    background: #e8f5e8;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.user-details {
    flex: 1;
}

.user-details h4 {
    font-size: 16px;
    margin-bottom: 5px;
    color: #333;
}

.user-details p {
    font-size: 12px;
    color: #666;
    margin-bottom: 3px;
}

.user-actions {
    display: flex;
    gap: 8px;
}

.action-btn.reset {
    background: #FF5722;
    color: white;
}

.action-btn.reset:hover {
    background: #E64A19;
}

/* 员工端考勤样式 */
.attendance-section {
    padding: 20px;
    background: #f8f9fa;
}

.today-overview {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 15px;
    margin-bottom: 20px;
}

.today-overview .overview-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.today-overview .card-icon {
    font-size: 24px;
    margin-bottom: 10px;
}

.today-overview .card-info h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.today-overview .number {
    font-size: 18px;
    font-weight: 600;
    color: #333;
}

.quick-actions {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
}

.action-btn {
    flex: 1;
    padding: 15px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
}

.action-btn.primary {
    background: #4CAF50;
    color: white;
}

.action-btn.primary:hover {
    background: #45a049;
}

.action-btn.secondary {
    background: #f5f5f5;
    color: #333;
}

.action-btn.secondary:hover {
    background: #e0e0e0;
}

.btn-icon {
    font-size: 18px;
}

.today-attendance {
    background: white;
    border-radius: 12px;
    padding: 20px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.today-attendance h4 {
    margin-bottom: 15px;
    color: #333;
}

.attendance-list {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.attendance-item {
    display: flex;
    align-items: center;
    gap: 15px;
    padding: 10px;
    border-radius: 8px;
    background: #f8f9fa;
}

.attendance-item .student-avatar {
    font-size: 20px;
    width: 40px;
    height: 40px;
    background: #e3f2fd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.attendance-item .student-info {
    flex: 1;
}

.attendance-item .student-info h5 {
    font-size: 14px;
    margin-bottom: 3px;
    color: #333;
}

.attendance-item .student-info p {
    font-size: 12px;
    color: #666;
}

.attendance-item .status {
    font-size: 16px;
}

.attendance-item .status.present {
    color: #4CAF50;
}

.attendance-item .status.absent {
    color: #f44336;
}

/* 考勤管理页面样式 */
.class-filter {
    padding: 15px 20px;
    background: white;
    border-bottom: 1px solid #e0e0e0;
    display: flex;
    gap: 10px;
}

.filter-btn {
    background: #f5f5f5;
    border: none;
    padding: 8px 16px;
    border-radius: 20px;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.filter-btn.active {
    background: #4CAF50;
    color: white;
}

.attendance-management-list {
    padding: 20px;
    background: #f8f9fa;
}

.class-section {
    margin-bottom: 30px;
}

.class-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
    padding-left: 10px;
    border-left: 4px solid #4CAF50;
}

.student-attendance-card {
    background: white;
    border-radius: 12px;
    padding: 15px 20px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.student-attendance-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.student-attendance-card .student-avatar {
    font-size: 20px;
    width: 40px;
    height: 40px;
    background: #e3f2fd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.student-attendance-card .student-info {
    min-width: 100px;
}

.student-attendance-card .student-info h5 {
    font-size: 14px;
    margin-bottom: 3px;
    color: #333;
}

.student-attendance-card .student-info p {
    font-size: 12px;
    color: #666;
}

.attendance-time {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 3px;
}

.attendance-time span {
    font-size: 12px;
}

.check-in {
    color: #4CAF50;
}

.check-out {
    color: #ff9800;
}

.absent-reason, .leave-reason {
    color: #f44336;
}

.attendance-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
}

.attendance-actions .status {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    white-space: nowrap;
}

.attendance-actions .status.present {
    background: #e8f5e8;
    color: #4CAF50;
}

.attendance-actions .status.absent {
    background: #ffebee;
    color: #f44336;
}

.attendance-actions .status.leave {
    background: #fff3e0;
    color: #ff9800;
}

.batch-operations {
    position: fixed;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 40px);
    max-width: 374px;
    background: white;
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    display: flex;
    justify-content: space-around;
    gap: 10px;
}

.batch-btn {
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 8px;
    padding: 10px 15px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    flex: 1;
}

.batch-btn:hover {
    background: #45a049;
    transform: translateY(-2px);
}

/* 教师考勤管理样式 */
.teacher-attendance-list {
    padding: 20px;
    background: #f8f9fa;
}

.teacher-attendance-card {
    background: white;
    border-radius: 12px;
    padding: 15px 20px;
    margin-bottom: 10px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.teacher-attendance-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.teacher-attendance-card .teacher-avatar {
    font-size: 24px;
    width: 50px;
    height: 50px;
    background: #fff3e0;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.teacher-attendance-card .teacher-info {
    min-width: 120px;
}

.teacher-attendance-card .teacher-info h5 {
    font-size: 16px;
    margin-bottom: 5px;
    color: #333;
}

.teacher-attendance-card .teacher-info p {
    font-size: 12px;
    color: #666;
}

/* 表单样式 */
.form-section {
    padding: 20px;
    background: #f8f9fa;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-size: 14px;
    background: white;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #4CAF50;
    box-shadow: 0 0 0 2px rgba(76, 175, 80, 0.1);
}

.form-group textarea {
    resize: vertical;
    min-height: 80px;
}

.form-group input[required],
.form-group select[required] {
    border-left: 3px solid #4CAF50;
}

/* 必填字段标识 */
.form-group label:after {
    content: "";
}

.form-group label:has(+ input[required]):after,
.form-group label:has(+ select[required]):after {
    content: " *";
    color: #f44336;
}

/* 班级管理样式 */
.class-list-section {
    padding: 20px;
    background: #f8f9fa;
}

.class-item-card {
    background: white;
    border-radius: 12px;
    padding: 15px 20px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.class-item-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.class-icon {
    font-size: 32px;
    width: 60px;
    height: 60px;
    background: #e8f5e8;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.class-details {
    flex: 1;
}

.class-details h4 {
    font-size: 18px;
    margin-bottom: 8px;
    color: #333;
    font-weight: 600;
}

.class-details p {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
    line-height: 1.4;
}

.class-actions {
    display: flex;
    gap: 8px;
}

/* 响应式调整 */
@media (max-width: 375px) {
    .finance-summary {
        grid-template-columns: 1fr;
    }

    .today-overview {
        grid-template-columns: 1fr;
    }

    .quick-actions {
        flex-direction: column;
    }

    .summary-card {
        padding: 15px;
    }

    .course-item-card,
    .teacher-item-card,
    .inventory-item-card,
    .user-item-card {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .course-actions,
    .teacher-actions,
    .inventory-actions,
    .user-actions {
        width: 100%;
        justify-content: flex-end;
    }

    .bill-item {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .bill-status {
        align-self: flex-end;
    }

    .student-attendance-card {
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
    }

    .attendance-actions {
        width: 100%;
        flex-direction: row;
        justify-content: space-between;
        align-items: center;
    }

    .batch-operations {
        width: calc(100% - 20px);
        padding: 12px;
    }

    .batch-btn {
        padding: 8px 12px;
        font-size: 11px;
    }
}

/* 日期选择器样式 */
.date-selector {
    background: white;
    padding: 15px 20px;
    border-bottom: 1px solid #e0e0e0;
}

.date-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.date-btn {
    background: #f5f5f5;
    border: none;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    font-size: 18px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.date-btn:hover {
    background: #e0e0e0;
}

.date-nav h3 {
    font-size: 16px;
    font-weight: 600;
    color: #333;
}

.date-stats {
    display: flex;
    justify-content: space-around;
    text-align: center;
}

.stat-item {
    font-size: 14px;
    color: #666;
}

.stat-item strong {
    color: #333;
    font-weight: 600;
}

/* 学生考勤列表样式 */
.student-attendance-list {
    padding: 0 20px 100px;
    background: #f8f9fa;
}

.student-card {
    background: white;
    border-radius: 12px;
    padding: 15px 20px;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.student-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.student-card .student-avatar {
    font-size: 20px;
    width: 40px;
    height: 40px;
    background: #e3f2fd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.student-card .student-info {
    flex: 1;
}

.student-card .student-info h4 {
    font-size: 16px;
    margin-bottom: 5px;
    color: #333;
}

.student-card .student-info p {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
}

.attendance-time {
    display: flex;
    gap: 15px;
}

.attendance-time span {
    font-size: 11px;
    color: #999;
}

.check-in {
    color: #4CAF50 !important;
}

.check-out {
    color: #ff9800 !important;
}

.leave-reason {
    color: #f44336 !important;
}

.attendance-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 8px;
}

.status-badge {
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 600;
    white-space: nowrap;
}

.status-badge.present {
    background: #e8f5e8;
    color: #4CAF50;
}

.status-badge.absent {
    background: #ffebee;
    color: #f44336;
}

.status-badge.leave {
    background: #fff3e0;
    color: #ff9800;
}

.action-btn {
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.action-btn:hover {
    background: #45a049;
}

/* 快捷操作栏 */
.quick-actions-bar {
    position: fixed;
    bottom: 80px;
    left: 50%;
    transform: translateX(-50%);
    width: calc(100% - 40px);
    max-width: 374px;
    background: white;
    border-radius: 12px;
    padding: 15px;
    box-shadow: 0 4px 20px rgba(0,0,0,0.15);
    display: flex;
    justify-content: space-around;
    gap: 10px;
}

.quick-btn {
    background: #f8f9fa;
    border: none;
    border-radius: 8px;
    padding: 12px 16px;
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    flex: 1;
}

.quick-btn:hover {
    background: #e9ecef;
    transform: translateY(-2px);
}

.btn-icon {
    font-size: 16px;
}

/* 底部导航增强 */
.bottom-nav .nav-item {
    cursor: pointer;
    transition: all 0.3s ease;
}

.bottom-nav .nav-item:hover {
    background: rgba(76, 175, 80, 0.1);
}

.bottom-nav .nav-item.active {
    color: #4CAF50;
    background: rgba(76, 175, 80, 0.1);
}

/* 加载状态 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #4CAF50;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式调整 */
@media (max-width: 375px) {
    .student-card {
        padding: 12px 15px;
        gap: 12px;
    }
    
    .attendance-actions {
        align-items: center;
    }
    
    .quick-actions-bar {
        width: calc(100% - 20px);
        padding: 12px;
    }
    
    .quick-btn {
        padding: 10px 12px;
        font-size: 11px;
    }
}

/* 页面切换动画 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.page.active {
    animation: fadeIn 0.3s ease-out;
}

/* 特殊样式覆盖 */
.app-container .container {
    max-width: 100%;
    box-shadow: none;
}

.app-container .bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    width: 100%;
    max-width: 100%;
    transform: none;
}

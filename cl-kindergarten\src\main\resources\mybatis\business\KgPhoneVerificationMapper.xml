<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgPhoneVerificationMapper">
    
    <resultMap type="KgPhoneVerification" id="KgPhoneVerificationResult">
        <result property="verificationId"    column="verification_id"    />
        <result property="phone"    column="phone"    />
        <result property="verificationCode"    column="verification_code"    />
        <result property="codeType"    column="code_type"    />
        <result property="openid"    column="openid"    />
        <result property="sendTime"    column="send_time"    />
        <result property="expireTime"    column="expire_time"    />
        <result property="isUsed"    column="is_used"    />
        <result property="usedTime"    column="used_time"    />
        <result property="ipAddress"    column="ip_address"    />
        <result property="comId"    column="com_id"    />
        <result property="createTime"    column="create_time"    />
    </resultMap>

    <sql id="selectKgPhoneVerificationVo">
        select verification_id, phone, verification_code, code_type, openid, send_time, expire_time, is_used, used_time, ip_address, com_id, create_time from kg_phone_verification
    </sql>

    <select id="selectKgPhoneVerificationList" parameterType="KgPhoneVerification" resultMap="KgPhoneVerificationResult">
        <include refid="selectKgPhoneVerificationVo"/>
        <where>  
            <if test="phone != null  and phone != ''"> and phone = #{phone}</if>
            <if test="verificationCode != null  and verificationCode != ''"> and verification_code = #{verificationCode}</if>
            <if test="codeType != null  and codeType != ''"> and code_type = #{codeType}</if>
            <if test="openid != null  and openid != ''"> and openid = #{openid}</if>
            <if test="sendTime != null "> and send_time = #{sendTime}</if>
            <if test="expireTime != null "> and expire_time = #{expireTime}</if>
            <if test="isUsed != null "> and is_used = #{isUsed}</if>
            <if test="usedTime != null "> and used_time = #{usedTime}</if>
            <if test="ipAddress != null  and ipAddress != ''"> and ip_address = #{ipAddress}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgPhoneVerificationById" parameterType="Long" resultMap="KgPhoneVerificationResult">
        <include refid="selectKgPhoneVerificationVo"/>
        where verification_id = #{verificationId}
    </select>
        
    <insert id="insertKgPhoneVerification" parameterType="KgPhoneVerification" useGeneratedKeys="true" keyProperty="verificationId">
        insert into kg_phone_verification
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="phone != null and phone != ''">phone,</if>
            <if test="verificationCode != null and verificationCode != ''">verification_code,</if>
            <if test="codeType != null and codeType != ''">code_type,</if>
            <if test="openid != null">openid,</if>
            <if test="sendTime != null">send_time,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="isUsed != null">is_used,</if>
            <if test="usedTime != null">used_time,</if>
            <if test="ipAddress != null">ip_address,</if>
            <if test="comId != null">com_id,</if>
            <if test="createTime != null">create_time,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="phone != null and phone != ''">#{phone},</if>
            <if test="verificationCode != null and verificationCode != ''">#{verificationCode},</if>
            <if test="codeType != null and codeType != ''">#{codeType},</if>
            <if test="openid != null">#{openid},</if>
            <if test="sendTime != null">#{sendTime},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="isUsed != null">#{isUsed},</if>
            <if test="usedTime != null">#{usedTime},</if>
            <if test="ipAddress != null">#{ipAddress},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createTime != null">#{createTime},</if>
         </trim>
    </insert>

    <update id="updateKgPhoneVerification" parameterType="KgPhoneVerification">
        update kg_phone_verification
        <trim prefix="SET" suffixOverrides=",">
            <if test="phone != null and phone != ''">phone = #{phone},</if>
            <if test="verificationCode != null and verificationCode != ''">verification_code = #{verificationCode},</if>
            <if test="codeType != null and codeType != ''">code_type = #{codeType},</if>
            <if test="openid != null">openid = #{openid},</if>
            <if test="sendTime != null">send_time = #{sendTime},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="isUsed != null">is_used = #{isUsed},</if>
            <if test="usedTime != null">used_time = #{usedTime},</if>
            <if test="ipAddress != null">ip_address = #{ipAddress},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
        </trim>
        where verification_id = #{verificationId}
    </update>

    <delete id="deleteKgPhoneVerificationById" parameterType="Long">
        delete from kg_phone_verification where verification_id = #{verificationId}
    </delete>

    <delete id="deleteKgPhoneVerificationByIds" parameterType="String">
        delete from kg_phone_verification where verification_id in 
        <foreach item="verificationId" collection="array" open="(" separator="," close=")">
            #{verificationId}
        </foreach>
    </delete>
    
</mapper>
package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgTuitionConfigMapper;
import com.cl.project.business.domain.KgTuitionConfig;
import com.cl.project.business.service.IKgTuitionConfigService;

/**
 * 园费配置Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgTuitionConfigServiceImpl implements IKgTuitionConfigService 
{
    @Autowired
    private KgTuitionConfigMapper kgTuitionConfigMapper;

    /**
     * 查询园费配置
     * 
     * @param configId 园费配置ID
     * @return 园费配置
     */
    @Override
    public KgTuitionConfig selectKgTuitionConfigById(Long configId)
    {
        return kgTuitionConfigMapper.selectKgTuitionConfigById(configId);
    }

    /**
     * 查询园费配置列表
     * 
     * @param kgTuitionConfig 园费配置
     * @return 园费配置
     */
    @Override
    public List<KgTuitionConfig> selectKgTuitionConfigList(KgTuitionConfig kgTuitionConfig)
    {
        return kgTuitionConfigMapper.selectKgTuitionConfigList(kgTuitionConfig);
    }

    /**
     * 新增园费配置
     * 
     * @param kgTuitionConfig 园费配置
     * @return 结果
     */
    @Override
    public int insertKgTuitionConfig(KgTuitionConfig kgTuitionConfig)
    {
        kgTuitionConfig.setCreateTime(DateUtils.getNowDate());
        return kgTuitionConfigMapper.insertKgTuitionConfig(kgTuitionConfig);
    }

    /**
     * 修改园费配置
     * 
     * @param kgTuitionConfig 园费配置
     * @return 结果
     */
    @Override
    public int updateKgTuitionConfig(KgTuitionConfig kgTuitionConfig)
    {
        kgTuitionConfig.setUpdateTime(DateUtils.getNowDate());
        return kgTuitionConfigMapper.updateKgTuitionConfig(kgTuitionConfig);
    }

    /**
     * 批量删除园费配置
     * 
     * @param configIds 需要删除的园费配置ID
     * @return 结果
     */
    @Override
    public int deleteKgTuitionConfigByIds(Long[] configIds)
    {
        return kgTuitionConfigMapper.deleteKgTuitionConfigByIds(configIds);
    }

    /**
     * 删除园费配置信息
     * 
     * @param configId 园费配置ID
     * @return 结果
     */
    @Override
    public int deleteKgTuitionConfigById(Long configId)
    {
        return kgTuitionConfigMapper.deleteKgTuitionConfigById(configId);
    }
}

package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgDingtalkAttendanceMapper;
import com.cl.project.business.domain.KgDingtalkAttendance;
import com.cl.project.business.service.IKgDingtalkAttendanceService;

/**
 * 钉钉打卡记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgDingtalkAttendanceServiceImpl implements IKgDingtalkAttendanceService 
{
    @Autowired
    private KgDingtalkAttendanceMapper kgDingtalkAttendanceMapper;

    /**
     * 查询钉钉打卡记录
     * 
     * @param recordId 钉钉打卡记录ID
     * @return 钉钉打卡记录
     */
    @Override
    public KgDingtalkAttendance selectKgDingtalkAttendanceById(Long recordId)
    {
        return kgDingtalkAttendanceMapper.selectKgDingtalkAttendanceById(recordId);
    }

    /**
     * 查询钉钉打卡记录列表
     * 
     * @param kgDingtalkAttendance 钉钉打卡记录
     * @return 钉钉打卡记录
     */
    @Override
    public List<KgDingtalkAttendance> selectKgDingtalkAttendanceList(KgDingtalkAttendance kgDingtalkAttendance)
    {
        return kgDingtalkAttendanceMapper.selectKgDingtalkAttendanceList(kgDingtalkAttendance);
    }

    /**
     * 新增钉钉打卡记录
     * 
     * @param kgDingtalkAttendance 钉钉打卡记录
     * @return 结果
     */
    @Override
    public int insertKgDingtalkAttendance(KgDingtalkAttendance kgDingtalkAttendance)
    {
        kgDingtalkAttendance.setCreateTime(DateUtils.getNowDate());
        return kgDingtalkAttendanceMapper.insertKgDingtalkAttendance(kgDingtalkAttendance);
    }

    /**
     * 修改钉钉打卡记录
     * 
     * @param kgDingtalkAttendance 钉钉打卡记录
     * @return 结果
     */
    @Override
    public int updateKgDingtalkAttendance(KgDingtalkAttendance kgDingtalkAttendance)
    {
        kgDingtalkAttendance.setUpdateTime(DateUtils.getNowDate());
        return kgDingtalkAttendanceMapper.updateKgDingtalkAttendance(kgDingtalkAttendance);
    }

    /**
     * 批量删除钉钉打卡记录
     * 
     * @param recordIds 需要删除的钉钉打卡记录ID
     * @return 结果
     */
    @Override
    public int deleteKgDingtalkAttendanceByIds(Long[] recordIds)
    {
        return kgDingtalkAttendanceMapper.deleteKgDingtalkAttendanceByIds(recordIds);
    }

    /**
     * 删除钉钉打卡记录信息
     * 
     * @param recordId 钉钉打卡记录ID
     * @return 结果
     */
    @Override
    public int deleteKgDingtalkAttendanceById(Long recordId)
    {
        return kgDingtalkAttendanceMapper.deleteKgDingtalkAttendanceById(recordId);
    }
}

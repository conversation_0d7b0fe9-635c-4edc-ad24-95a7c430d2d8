package com.cl.project.business.domain;

import java.math.BigDecimal;

import com.cl.framework.web.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 托管课程对象 kg_course
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgCourse extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 课程ID */
    private Long courseId;

    /** 课程名称 */
    @Excel(name = "课程名称")
    private String courseName;

    /** 课程类型（英语、美术、全脑、军警等） */
    @Excel(name = "课程类型", readConverterExp = "英=语、美术、全脑、军警等")
    private String courseType;

    /** 单节课价格 */
    @Excel(name = "单节课价格")
    private BigDecimal pricePerSession;

    /** 课程时长（分钟） */
    @Excel(name = "课程时长", readConverterExp = "分=钟")
    private Long duration;

    /** 最少开班人数 */
    @Excel(name = "最少开班人数")
    private Long minStudents;

    /** 最大班级人数 */
    @Excel(name = "最大班级人数")
    private Long maxStudents;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    public void setCourseId(Long courseId) 
    {
        this.courseId = courseId;
    }

    public Long getCourseId() 
    {
        return courseId;
    }
    public void setCourseName(String courseName) 
    {
        this.courseName = courseName;
    }

    public String getCourseName() 
    {
        return courseName;
    }
    public void setCourseType(String courseType) 
    {
        this.courseType = courseType;
    }

    public String getCourseType() 
    {
        return courseType;
    }
    public void setPricePerSession(BigDecimal pricePerSession) 
    {
        this.pricePerSession = pricePerSession;
    }

    public BigDecimal getPricePerSession() 
    {
        return pricePerSession;
    }
    public void setDuration(Long duration) 
    {
        this.duration = duration;
    }

    public Long getDuration() 
    {
        return duration;
    }
    public void setMinStudents(Long minStudents) 
    {
        this.minStudents = minStudents;
    }

    public Long getMinStudents() 
    {
        return minStudents;
    }
    public void setMaxStudents(Long maxStudents) 
    {
        this.maxStudents = maxStudents;
    }

    public Long getMaxStudents() 
    {
        return maxStudents;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("courseId", getCourseId())
            .append("courseName", getCourseName())
            .append("courseType", getCourseType())
            .append("pricePerSession", getPricePerSession())
            .append("duration", getDuration())
            .append("minStudents", getMinStudents())
            .append("maxStudents", getMaxStudents())
            .append("status", getStatus())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

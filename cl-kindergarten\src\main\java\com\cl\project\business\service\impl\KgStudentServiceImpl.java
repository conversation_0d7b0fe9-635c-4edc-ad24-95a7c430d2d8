package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgStudentMapper;
import com.cl.project.business.domain.KgStudent;
import com.cl.project.business.service.IKgStudentService;

/**
 * 幼儿信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgStudentServiceImpl implements IKgStudentService 
{
    @Autowired
    private KgStudentMapper kgStudentMapper;

    /**
     * 查询幼儿信息
     * 
     * @param studentId 幼儿信息ID
     * @return 幼儿信息
     */
    @Override
    public KgStudent selectKgStudentById(Long studentId)
    {
        return kgStudentMapper.selectKgStudentById(studentId);
    }

    /**
     * 查询幼儿信息列表
     * 
     * @param kgStudent 幼儿信息
     * @return 幼儿信息
     */
    @Override
    public List<KgStudent> selectKgStudentList(KgStudent kgStudent)
    {
        return kgStudentMapper.selectKgStudentList(kgStudent);
    }

    /**
     * 新增幼儿信息
     * 
     * @param kgStudent 幼儿信息
     * @return 结果
     */
    @Override
    public int insertKgStudent(KgStudent kgStudent)
    {
        // 生成唯一学生编码: STU + 日期(yyyyMMdd) + 4位随机数
        String dateStr = DateUtils.formatDate(DateUtils.getNowDate(), "yyyyMMdd");
        String randomNum = String.format("%04d", new java.util.Random().nextInt(10000));
        String studentCode = "STU" + dateStr + randomNum;
        kgStudent.setStudentCode(studentCode);
        
        kgStudent.setCreateTime(DateUtils.getNowDate());
        return kgStudentMapper.insertKgStudent(kgStudent);
    }

    /**
     * 修改幼儿信息
     * 
     * @param kgStudent 幼儿信息
     * @return 结果
     */
    @Override
    public int updateKgStudent(KgStudent kgStudent)
    {
        kgStudent.setUpdateTime(DateUtils.getNowDate());
        return kgStudentMapper.updateKgStudent(kgStudent);
    }

    /**
     * 批量删除幼儿信息
     * 
     * @param studentIds 需要删除的幼儿信息ID
     * @return 结果
     */
    @Override
    public int deleteKgStudentByIds(Long[] studentIds)
    {
        return kgStudentMapper.deleteKgStudentByIds(studentIds);
    }

    /**
     * 删除幼儿信息信息
     * 
     * @param studentId 幼儿信息ID
     * @return 结果
     */
    @Override
    public int deleteKgStudentById(Long studentId)
    {
        return kgStudentMapper.deleteKgStudentById(studentId);
    }
    
    /**
     * 根据钉钉用户ID查询学生信息
     * 
     * @param dingtalkUserId 钉钉用户ID
     * @return 学生信息
     */
    @Override
    public KgStudent selectKgStudentByDingtalkUserId(String dingtalkUserId)
    {
        return kgStudentMapper.selectKgStudentByDingtalkUserId(dingtalkUserId);
    }
}

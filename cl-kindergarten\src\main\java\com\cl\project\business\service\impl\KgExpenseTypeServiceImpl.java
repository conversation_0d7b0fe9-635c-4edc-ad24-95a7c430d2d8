package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgExpenseTypeMapper;
import com.cl.project.business.domain.KgExpenseType;
import com.cl.project.business.service.IKgExpenseTypeService;

/**
 * 支出类型Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgExpenseTypeServiceImpl implements IKgExpenseTypeService 
{
    @Autowired
    private KgExpenseTypeMapper kgExpenseTypeMapper;

    /**
     * 查询支出类型
     * 
     * @param typeId 支出类型ID
     * @return 支出类型
     */
    @Override
    public KgExpenseType selectKgExpenseTypeById(Long typeId)
    {
        return kgExpenseTypeMapper.selectKgExpenseTypeById(typeId);
    }

    /**
     * 查询支出类型列表
     * 
     * @param kgExpenseType 支出类型
     * @return 支出类型
     */
    @Override
    public List<KgExpenseType> selectKgExpenseTypeList(KgExpenseType kgExpenseType)
    {
        return kgExpenseTypeMapper.selectKgExpenseTypeList(kgExpenseType);
    }

    /**
     * 新增支出类型
     * 
     * @param kgExpenseType 支出类型
     * @return 结果
     */
    @Override
    public int insertKgExpenseType(KgExpenseType kgExpenseType)
    {
        kgExpenseType.setCreateTime(DateUtils.getNowDate());
        return kgExpenseTypeMapper.insertKgExpenseType(kgExpenseType);
    }

    /**
     * 修改支出类型
     * 
     * @param kgExpenseType 支出类型
     * @return 结果
     */
    @Override
    public int updateKgExpenseType(KgExpenseType kgExpenseType)
    {
        kgExpenseType.setUpdateTime(DateUtils.getNowDate());
        return kgExpenseTypeMapper.updateKgExpenseType(kgExpenseType);
    }

    /**
     * 批量删除支出类型
     * 
     * @param typeIds 需要删除的支出类型ID
     * @return 结果
     */
    @Override
    public int deleteKgExpenseTypeByIds(Long[] typeIds)
    {
        return kgExpenseTypeMapper.deleteKgExpenseTypeByIds(typeIds);
    }

    /**
     * 删除支出类型信息
     * 
     * @param typeId 支出类型ID
     * @return 结果
     */
    @Override
    public int deleteKgExpenseTypeById(Long typeId)
    {
        return kgExpenseTypeMapper.deleteKgExpenseTypeById(typeId);
    }
}

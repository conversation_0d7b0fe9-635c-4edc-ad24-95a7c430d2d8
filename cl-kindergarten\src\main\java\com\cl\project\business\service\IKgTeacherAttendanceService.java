package com.cl.project.business.service;

import java.util.List;
import java.util.Date;
import com.cl.project.business.domain.KgTeacherAttendance;
import com.cl.project.business.domain.dto.BatchConfirmAttendanceDto;
import com.cl.project.business.domain.dto.TeacherAttendanceOverviewDto;
import com.cl.project.business.domain.dto.BatchTeacherCheckinDto;

/**
 * 教师考勤记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IKgTeacherAttendanceService 
{
    /**
     * 查询教师考勤记录
     * 
     * @param attendanceId 教师考勤记录ID
     * @return 教师考勤记录
     */
    public KgTeacherAttendance selectKgTeacherAttendanceById(Long attendanceId);

    /**
     * 查询教师考勤记录列表
     * 
     * @param kgTeacherAttendance 教师考勤记录
     * @return 教师考勤记录集合
     */
    public List<KgTeacherAttendance> selectKgTeacherAttendanceList(KgTeacherAttendance kgTeacherAttendance);

    /**
     * 新增教师考勤记录
     * 
     * @param kgTeacherAttendance 教师考勤记录
     * @return 结果
     */
    public int insertKgTeacherAttendance(KgTeacherAttendance kgTeacherAttendance);

    /**
     * 修改教师考勤记录
     * 
     * @param kgTeacherAttendance 教师考勤记录
     * @return 结果
     */
    public int updateKgTeacherAttendance(KgTeacherAttendance kgTeacherAttendance);

    /**
     * 批量删除教师考勤记录
     * 
     * @param attendanceIds 需要删除的教师考勤记录ID
     * @return 结果
     */
    public int deleteKgTeacherAttendanceByIds(Long[] attendanceIds);

    /**
     * 删除教师考勤记录信息
     * 
     * @param attendanceId 教师考勤记录ID
     * @return 结果
     */
    public int deleteKgTeacherAttendanceById(Long attendanceId);

    /**
     * 查询教师考勤概览列表
     * 展示所有教师及其指定日期的考勤状态
     * 
     * @param attendanceDate 考勤日期，为null时查询当日
     * @return 教师考勤概览集合
     */
    public List<TeacherAttendanceOverviewDto> selectTeacherAttendanceOverview(Date attendanceDate, String teacherName, String attendanceStatus, String dataSource);

    /**
     * 批量签到教师考勤
     * @param batchDto 批量导入数据
     * @return 影响行数
     */
    public int batchTeacherCheckin(BatchTeacherCheckinDto batchDto);



    /**
     * 批量确认教师考勤
     * @param batchDto 批量确认数据
     * @return 影响行数
     */
    int batchConfirmAttendance(BatchConfirmAttendanceDto batchDto);
}

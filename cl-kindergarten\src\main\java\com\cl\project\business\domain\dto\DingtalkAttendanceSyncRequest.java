package com.cl.project.business.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;

/**
 * 钉钉考勤同步请求DTO
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
@ApiModel("钉钉考勤同步请求")
public class DingtalkAttendanceSyncRequest 
{
    /** 开始日期 */
    @ApiModelProperty("开始日期，格式: yyyy-MM-dd 或 yyyy-MM-dd HH:mm:ss")
    private String workDateFrom;
    
    /** 结束日期 */
    @ApiModelProperty("结束日期，格式: yyyy-MM-dd 或 yyyy-MM-dd HH:mm:ss") 
    private String workDateTo;
    
    /** 用户ID列表（可选，为空则同步所有用户） */
    @ApiModelProperty("用户ID列表，为空则同步所有用户")
    private List<String> userIdList;
    
    /** 数据偏移量 */
    @ApiModelProperty("数据偏移量，默认0")
    private Long offset = 0L;
    
    /** 数据限制条数 */
    @ApiModelProperty("数据限制条数，默认50")
    private Long limit = 50L;

    public DingtalkAttendanceSyncRequest() {
    }

    public DingtalkAttendanceSyncRequest(String workDateFrom, String workDateTo) {
        this.workDateFrom = workDateFrom;
        this.workDateTo = workDateTo;
    }

    public String getWorkDateFrom() {
        return workDateFrom;
    }

    public void setWorkDateFrom(String workDateFrom) {
        this.workDateFrom = workDateFrom;
    }

    public String getWorkDateTo() {
        return workDateTo;
    }

    public void setWorkDateTo(String workDateTo) {
        this.workDateTo = workDateTo;
    }

    public List<String> getUserIdList() {
        return userIdList;
    }

    public void setUserIdList(List<String> userIdList) {
        this.userIdList = userIdList;
    }

    public Long getOffset() {
        return offset;
    }

    public void setOffset(Long offset) {
        this.offset = offset;
    }

    public Long getLimit() {
        return limit;
    }

    public void setLimit(Long limit) {
        this.limit = limit;
    }

    @Override
    public String toString() {
        return "DingtalkAttendanceSyncRequest{" +
                "workDateFrom='" + workDateFrom + '\'' +
                ", workDateTo='" + workDateTo + '\'' +
                ", userIdList=" + userIdList +
                ", offset=" + offset +
                ", limit=" + limit +
                '}';
    }
}

package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgExpenseMapper;
import com.cl.project.business.domain.KgExpense;
import com.cl.project.business.service.IKgExpenseService;

/**
 * 支出记录Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgExpenseServiceImpl implements IKgExpenseService 
{
    @Autowired
    private KgExpenseMapper kgExpenseMapper;

    /**
     * 查询支出记录
     * 
     * @param expenseId 支出记录ID
     * @return 支出记录
     */
    @Override
    public KgExpense selectKgExpenseById(Long expenseId)
    {
        return kgExpenseMapper.selectKgExpenseById(expenseId);
    }

    /**
     * 查询支出记录列表
     * 
     * @param kgExpense 支出记录
     * @return 支出记录
     */
    @Override
    public List<KgExpense> selectKgExpenseList(KgExpense kgExpense)
    {
        return kgExpenseMapper.selectKgExpenseList(kgExpense);
    }

    /**
     * 新增支出记录
     * 
     * @param kgExpense 支出记录
     * @return 结果
     */
    @Override
    public int insertKgExpense(KgExpense kgExpense)
    {
        kgExpense.setCreateTime(DateUtils.getNowDate());
        return kgExpenseMapper.insertKgExpense(kgExpense);
    }

    /**
     * 修改支出记录
     * 
     * @param kgExpense 支出记录
     * @return 结果
     */
    @Override
    public int updateKgExpense(KgExpense kgExpense)
    {
        kgExpense.setUpdateTime(DateUtils.getNowDate());
        return kgExpenseMapper.updateKgExpense(kgExpense);
    }

    /**
     * 批量删除支出记录
     * 
     * @param expenseIds 需要删除的支出记录ID
     * @return 结果
     */
    @Override
    public int deleteKgExpenseByIds(Long[] expenseIds)
    {
        return kgExpenseMapper.deleteKgExpenseByIds(expenseIds);
    }

    /**
     * 删除支出记录信息
     * 
     * @param expenseId 支出记录ID
     * @return 结果
     */
    @Override
    public int deleteKgExpenseById(Long expenseId)
    {
        return kgExpenseMapper.deleteKgExpenseById(expenseId);
    }
}

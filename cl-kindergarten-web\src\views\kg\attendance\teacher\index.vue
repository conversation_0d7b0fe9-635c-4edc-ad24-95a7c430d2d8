<template>
  <div class="app-container" style="padding: 0; width: 100%;">
    <!-- 搜索表单 -->
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="80px">
      <el-form-item label="教师姓名" prop="teacherName">
        <el-input
          v-model="queryParams.teacherName"
          placeholder="请输入教师姓名"
          clearable
          size="small"
          @keyup.enter.native="getOverviewList"
        />
      </el-form-item>
      <el-form-item label="考勤日期" prop="attendanceDate">
        <el-date-picker clearable size="small" style="width: 200px"
          v-model="queryParams.attendanceDate"
          type="date"
          placeholder="选择日期"
          value-format="yyyy-MM-dd">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="考勤状态" prop="attendanceStatus">
        <el-select v-model="queryParams.attendanceStatus" placeholder="请选择考勤状态" clearable size="small">
          <el-option label="出勤" value="1" />
          <el-option label="缺勤" value="3" />
          <el-option label="迟到" value="2" />
          <el-option label="早退" value="5" />
          <el-option label="病假" value="6" />
          <el-option label="休假" value="8" />
          <el-option label="请假" value="4" />
        </el-select>
      </el-form-item>
      <el-form-item label="数据来源" prop="dataSource">
        <el-select v-model="queryParams.dataSource" placeholder="请选择数据来源" clearable size="small">
          <el-option label="钉钉打卡" value="dingtalk" />
          <el-option label="手动签到" value="manual" />
          <el-option label="混合" value="mixed" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="getOverviewList">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 操作按钮区 -->
    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
        >批量签到</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          icon="el-icon-refresh"
          size="mini"
          @click="handleDingtalkSync"
        >同步钉钉</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-check"
          size="mini"
          :disabled="multipleSelection.length === 0"
          @click="handleConfirmAttendance"
        >批量考勤确认</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col>
    </el-row>

    <!-- 教师考勤主表格 -->
    <el-table
      class="attendance-table"
      style="width: 100%;"
      table-layout="fixed"
      v-loading="loading"
      :data="attendanceList"
      @selection-change="handleSelectionChange"
      :expand-row-keys="expandedRows"
      :row-key="getRowKey"
      @expand-change="handleExpandChange">
      <el-table-column type="expand">
        <template slot-scope="props">
          <div class="expand-content">
            <!-- 钉钉打卡记录 -->
            <div class="source-section">
              <h4><i class="el-icon-mobile-phone"></i> 钉钉打卡记录</h4>
              <el-table :data="props.row.dingtalkRecords" size="mini" border>
                <el-table-column label="打卡时间" align="center">
                  <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.checkTime, '{y}-{m}-{d} {h}:{i}:{s}') }}</span>
                  </template>
                </el-table-column>
                <el-table-column label="打卡类型" align="center">
                  <template slot-scope="scope">
                    <el-tag v-if="scope.row.checkType === 'OnDuty'" type="success" size="mini">上班</el-tag>
                    <el-tag v-else type="info" size="mini">下班</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="定位状态" align="center">
                  <template slot-scope="scope">
                    <el-tag v-if="scope.row.locationResult === 'Normal'" type="success" size="mini">正常</el-tag>
                    <el-tag v-else type="danger" size="mini">异常</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="位置详情" prop="locationDetail" show-overflow-tooltip />
              </el-table>
              <div v-if="!props.row.dingtalkRecords || props.row.dingtalkRecords.length === 0" class="no-data">
                <span class="text-muted">暂无钉钉打卡记录</span>
              </div>
            </div>
            
            <!-- 手动签到记录 -->
            <div class="source-section">
              <h4><i class="el-icon-edit-outline"></i> 手动签到记录</h4>
              <el-table :data="props.row.manualRecords" size="mini" border>
                <el-table-column label="签到时间" align="center">
                  <template slot-scope="scope">
                    <span v-if="scope.row.checkInTime">
                      {{ parseTime(scope.row.checkInTime, '{y}-{m}-{d} {h}:{i}:{s}') }}
                    </span>
                    <span v-else class="text-muted">--</span>
                  </template>
                </el-table-column>
                <el-table-column label="签到方式" align="center">
                  <template slot-scope="scope">
                    <el-tag v-if="scope.row.checkInMethod === 'face'" type="success" size="mini">人脸</el-tag>
                    <el-tag v-else type="info" size="mini">手动</el-tag>
                  </template>
                </el-table-column>
                <el-table-column label="备注" prop="remark" show-overflow-tooltip />
                <el-table-column label="确认状态" align="center">
  <template slot-scope="scope">
    <el-tag v-if="scope.row.isConfirmed === 1" type="success" size="mini">已确认</el-tag>
    <el-button
      v-else
      size="mini"
      type="text"
      icon="el-icon-check"
      @click="handleSingleConfirm(scope.row)"
    >考勤确认</el-button>
  </template>
</el-table-column>
              </el-table>
              <div v-if="!props.row.manualRecords || props.row.manualRecords.length === 0" class="no-data">
                <span class="text-muted">暂无手动签到记录</span>
              </div>
            </div>
          </div>
        </template>
      </el-table-column>
      <el-table-column type="selection" align="center" />
      <el-table-column label="教师姓名" align="center" prop="teacherName" />
      <el-table-column label="考勤日期" align="center" prop="attendanceDate">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.attendanceDate, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="签到时间" align="center" prop="checkInTime">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.checkInTime" type="success" size="mini">
            {{ scope.row.checkInTime }}
          </el-tag>
          <el-tag v-else type="info" size="mini" effect="plain">未签到</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="工作时长" align="center" prop="workHours">
        <template slot-scope="scope">
          <span v-if="scope.row.workHours">{{ scope.row.workHours }}h</span>
          <span v-else class="text-muted">--</span>
        </template>
      </el-table-column>
      <el-table-column label="考勤状态" align="center" prop="attendanceStatus">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.attendanceStatus === '1'" type="success" size="mini">出勤</el-tag>
          <el-tag v-else-if="scope.row.attendanceStatus === '2'" type="warning" size="mini">迟到</el-tag>
          <el-tag v-else-if="scope.row.attendanceStatus === '3'" type="danger" size="mini">缺勤</el-tag>
          <el-tag v-else-if="scope.row.attendanceStatus === '4'" type="info" size="mini">请假</el-tag>
          <el-tag v-else-if="scope.row.attendanceStatus === '5'" type="warning" size="mini">早退</el-tag>
          <el-tag v-else-if="scope.row.attendanceStatus === '6'" type="info" size="mini">病假</el-tag>
          <el-tag v-else-if="scope.row.attendanceStatus === '8'" type="info" size="mini">休假</el-tag>
          <el-tag v-else type="info" size="mini">未知</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="确认状态" align="center" prop="isConfirmed">
        <template slot-scope="scope">
          <el-tag v-if="scope.row.isConfirmed === 1" type="success" size="mini">
            <i class="el-icon-check"></i> 已确认
          </el-tag>
          <el-tag v-else type="warning" size="mini">
            <i class="el-icon-time"></i> 待确认
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            v-if="scope.row.isConfirmed === 0"
            size="mini"
            type="text"
            icon="el-icon-check"
            @click="handleConfirm(scope.row)"
          >考勤确认</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-refresh"
            @click="handleGenerate(scope.row)"
          >重新生成</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getOverviewList"
    />

    <!-- 教师签到/签退对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" :rules="rules" label-width="80px">
        <el-form-item label="教师" prop="teacherIds">
          <el-select v-model="form.teacherIds" multiple filterable placeholder="请选择教师">
            <el-option
              v-for="item in teacherList"
              :key="item.teacherId"
              :label="item.teacherName"
              :value="item.teacherId"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="考勤日期" prop="attendanceDate">
          <el-date-picker clearable size="small" style="width: 200px"
            v-model="form.attendanceDate"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="选择考勤日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="考勤状态" prop="attendanceStatus">
          <el-radio-group v-model="form.attendanceStatus">
            <el-radio label="1">签到</el-radio>
            <el-radio label="3">缺勤</el-radio>
            <el-radio label="4">请假</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入备注" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 钉钉考勤同步对话框 -->
    <el-dialog title="钉钉考勤同步" :visible.sync="syncDialogVisible" width="600px" append-to-body class="sync-dialog">
      <el-form :model="syncForm" label-width="100px">
        <el-form-item label="同步日期">
          <el-col :span="11">
            <el-date-picker
              v-model="syncForm.workDateFrom"
              type="date"
              placeholder="开始日期"
              value-format="yyyy-MM-dd"
              style="width: 100%;"
            />
          </el-col>
          <el-col class="line" :span="2" style="text-align: center;">-</el-col>
          <el-col :span="11">
            <el-date-picker
              v-model="syncForm.workDateTo"
              type="date"
              placeholder="结束日期"
              value-format="yyyy-MM-dd"
              style="width: 100%;"
            />
          </el-col>
        </el-form-item>
        <el-form-item label="指定用户" prop="userIdList">
          <el-select
            v-model="syncForm.userIdList"
            multiple
            filterable
            placeholder="留空则同步所有用户"
            style="width: 100%;"
          >
            <el-option
              v-for="item in teacherList"
              v-if="item.dingtalkUserId"
              :key="item.dingtalkUserId"
              :label="item.teacherName"
              :value="item.dingtalkUserId"
            >
              <span style="float: left">{{ item.teacherName }}</span>
              <span style="float: right; color: #8492a6; font-size: 13px">{{ item.dingtalkUserId }}</span>
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-alert
            title="同步说明"
            type="info"
            :closable="false"
            show-icon
          >
            <div slot="default">
              <p>• 将从钉钉平台获取指定日期范围内的打卡记录</p>
              <p>• 如不指定用户，将同步所有用户的打卡记录</p>
              <p>• 已存在的记录将被跳过，不会重复同步</p>
            </div>
          </el-alert>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancelDingtalkSync">取 消</el-button>
        <el-button type="primary" @click="performDingtalkSync" :loading="syncLoading">
          {{ syncLoading ? '同步中...' : '开始同步' }}
        </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAllTeacher } from '@/api/kg/attendance/teacher'
import { syncAttendanceByUsers } from '@/api/business/dingtalk'
import {
  listTeacherAttendance,
  getTeacherAttendance,
  addTeacherAttendance,
  updateTeacherAttendance,
  delTeacherAttendance,
  exportTeacherAttendance,
  teacherCheckin,
  teacherCheckout,
  getTeacherAttendanceOverview,
  getDingtalkAttendanceRecords,
  batchTeacherCheckin,
  batchConfirmAttendance
} from '@/api/kg/attendance/teacher';
import { listTeacher } from "@/api/kg/teacher/info";

export default {

  name: "TeacherAttendance",
  dicts: ['kg_attendance_status'],
  data() {
    return {
      multipleSelection: [], // 批量选择的响应式数组
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 教师考勤表格数据
      attendanceList: [],
      // 教师列表
      teacherList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        teacherName: undefined,
        attendanceDate: new Date().toISOString().slice(0, 10), // 默认当天
        attendanceStatus: undefined,
        dataSource: undefined, // 数据来源筛选
      },
      // 钉钉同步相关
      syncLoading: false,
      syncDialogVisible: false,
      syncForm: {
        workDateFrom: undefined,
        workDateTo: undefined,
        userIdList: []
      },
      // 展开行数据
      expandedRows: [],
      expandedData: {},
      // 表单参数
      form: {
        attendanceId: undefined,
        teacherIds: [], // 多选教师ID数组
        attendanceDate: undefined,
        attendanceStatus: undefined,
        checkinTime: undefined,
        checkoutTime: undefined,
        workHours: undefined,
        remark: undefined
      },
      // 表单校验
      rules: {
        teacherIds: [
          { required: true, type: 'array', min: 1, message: "请选择至少一位教师", trigger: "change" }
        ],
        attendanceDate: [
          { required: true, message: "考勤日期不能为空", trigger: "blur" }
        ],
        attendanceStatus: [
          { required: true, message: "考勤状态不能为空", trigger: "change" }
        ]
      }
    };
  },
  created() {
    this.getOverviewList();
    this.getTeacherList();
  },
  methods: {
    /** 获取表格行唯一键 */
    getRowKey(row) {
      // 优先使用attendanceId，如果没有则使用teacherId+attendanceDate组合
      if (row.attendanceId) {
        return `attendance_${row.attendanceId}`;
      }
      // 对于没有考勤记录的教师，使用teacherId和日期组合
      const teacherId = row.teacherId || 'unknown';
      const date = row.attendanceDate || 'nodate';
      return `teacher_${teacherId}_${date}`;
    },
    /** el-table 行展开事件处理，兼容handleRowExpand逻辑 */
    handleExpandChange(row, expandedRows) {
      this.handleRowExpand(row, expandedRows);
    },
    /** 查询教师考勤列表 */
    // 获取教师考勤概览列表
    getOverviewList() {
      this.loading = true;
      const params = {
        pageNum: this.queryParams.pageNum,
        pageSize: this.queryParams.pageSize,
        teacherName: this.queryParams.teacherName,
        attendanceStatus: this.queryParams.attendanceStatus,
        dataSource: this.queryParams.dataSource
      };
      // 处理考勤日期参数，避免出现[]数组格式
      if (Array.isArray(this.queryParams.attendanceDate)) {
        params.attendanceDate = this.queryParams.attendanceDate.join(',');
      } else {
        params.attendanceDate = this.queryParams.attendanceDate;
      }
      getTeacherAttendanceOverview(params).then(response => {
        this.attendanceList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 查询所有教师列表（不分页） */
    getTeacherList() {
      listAllTeacher().then(response => {
        this.teacherList = (response.data || []).map(item => ({
          ...item,
          teacherId: String(item.teacherId)
        }));
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        attendanceId: undefined,
        teacherIds: [], // 多选教师ID数组
        attendanceDate: undefined,
        attendanceStatus: undefined,
        checkinTime: undefined,
        checkoutTime: undefined,
        workHours: undefined,
        remark: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      // 恢复查询参数为默认值
      this.queryParams = {
        pageNum: 1,
        pageSize: 10,
        teacherName: undefined,
        attendanceDate: new Date().toISOString().slice(0, 10), // 默认当天
        attendanceStatus: undefined,
        dataSource: undefined
      };
      this.$nextTick(() => {
        this.resetForm("queryForm");
        // 立即刷新数据
        this.getOverviewList();
      });
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.attendanceId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    /** 教师签到按钮操作 */
    handleCheckin() {
      this.reset();
      this.open = true;
      this.title = "教师批量签到";
      this.form.attendanceStatus = "1";
      // 设置默认日期为今天
      this.form.attendanceDate = new Date().toISOString().slice(0, 10);
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const attendanceId = row.attendanceId || this.ids
      getTeacherAttendance(attendanceId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改教师考勤";
      });
    },
    /** 提交按钮 */
    submitForm: function() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.attendanceId != undefined) {
            // 修改单个考勤记录
            updateTeacherAttendance(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess("修改成功");
                this.open = false;
                this.getOverviewList();
              }
            });
          } else if (this.form.teacherIds && this.form.teacherIds.length > 0) {
            // 批量签到
            const batchData = {
              teacherIds: this.form.teacherIds,
              attendanceDate: this.form.attendanceDate,
              attendanceStatus: this.form.attendanceStatus,
              remark: this.form.remark
            };
            batchTeacherCheckin(batchData).then(response => {
              if (response.code === 200) {
                this.msgSuccess(response.msg || "批量签到成功");
                this.open = false;
                this.getOverviewList();
              }
            }).catch(error => {
              this.msgError("批量签到失败: " + (error.response?.data?.msg || error.message));
            });
          } else {
            // 新增单个考勤记录
            addTeacherAttendance(this.form).then(response => {
              if (response.code === 200) {
                this.msgSuccess("新增成功");
                this.open = false;
                this.getOverviewList();
              }
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const attendanceIds = row.attendanceId || this.ids;
      this.$confirm('是否确认删除教师考勤记录编号为"' + attendanceIds + '"的数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return delTeacherAttendance(attendanceIds);
        }).then(async () => {
          this.getList();
          this.msgSuccess("删除成功");
        }).catch(function() {});
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有教师考勤数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportTeacherAttendance(queryParams);
        }).then(response => {
          this.download(response.msg);
        }).catch(function() {});
    },
    /** 钉钉考勤同步按钮操作 */
    handleDingtalkSync() {
      this.syncDialogVisible = true;
      // 设置默认同步日期为今天
      const today = new Date();
      const dateStr = today.getFullYear() + '-' + 
        String(today.getMonth() + 1).padStart(2, '0') + '-' + 
        String(today.getDate()).padStart(2, '0');
      this.syncForm.workDateFrom = dateStr + ' 00:00:00';
      this.syncForm.workDateTo = dateStr + ' 23:59:59';
      this.syncForm.userIdList = [];
    },
    /** 执行钉钉考勤同步 */
    async performDingtalkSync() {
      if (!this.syncForm.workDateFrom || !this.syncForm.workDateTo) {
        this.$message.error('请选择同步日期范围');
        return;
      }
      
      this.syncLoading = true;
      try {
        // 确保日期格式正确
        const requestData = {
          workDateFrom: this.syncForm.workDateFrom.includes(' ') ? 
            this.syncForm.workDateFrom : this.syncForm.workDateFrom + ' 00:00:00',
          workDateTo: this.syncForm.workDateTo.includes(' ') ? 
            this.syncForm.workDateTo : this.syncForm.workDateTo + ' 23:59:59',
          userIdList: this.syncForm.userIdList || []
        };
        
        let response;
        if (requestData.userIdList.length > 0) {
          // 同步指定用户
          response = await syncAttendanceByUsers(requestData);
        } else {
          // 同步所有用户
          response = await syncAttendance(requestData);
        }
        
        this.$message.success(`同步完成，共处理 ${response.data} 条记录`);
        this.syncDialogVisible = false;
        this.getOverviewList(); // 刷新列表
      } catch (error) {
        console.error('钉钉考勤同步失败:', error);
        const errorMsg = error.response && error.response.data && error.response.data.msg ? 
          error.response.data.msg : (error.message || '未知错误');
        this.$message.error('同步失败: ' + errorMsg);
      } finally {
        this.syncLoading = false;
      }
    },
    /** 取消钉钉同步 */
    cancelDingtalkSync() {
      this.syncDialogVisible = false;
      this.syncForm = {
        workDateFrom: undefined,
        workDateTo: undefined,
        userIdList: []
      };
    },
    /** 单条手动考勤确认按钮操作 */
    async handleSingleConfirm(record) {
      if (!record.attendanceId) {
        this.$message.warning('无效的考勤记录');
        return;
      }
      this.$confirm('是否确认该条手动考勤记录?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info"
      }).then(async () => {
        try {
          await batchConfirmAttendance({ attendanceIds: [record.attendanceId] });
          this.$message.success('确认成功');
          this.getOverviewList(); // 刷新主表
        } catch (e) {
          this.$message.error('确认失败');
        }
      }).catch(() => {});
    },
    /** 仍保留批量确认考勤按钮操作 */
    async handleConfirm(row) {
      // 单行确认，收集该行manualRecords所有attendanceId
      const ids = (row.manualRecords || []).map(r => r.attendanceId).filter(id => id != null);
      if (!ids.length) {
        this.$message.warning('该教师没有可确认的手动考勤记录');
        return;
      }
      this.$confirm('是否确认该教师所有手动考勤记录?', "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "info"
      }).then(async () => {
        try {
          await batchConfirmAttendance({ attendanceIds: ids });
          this.$message.success('确认成功');
          this.getOverviewList(); // 刷新主表
        } catch (e) {
          this.$message.error('确认失败');
        }
      }).catch(() => {});
    },
    /** 确认考勤按钮操作 */
    async handleConfirmAttendance() {
      if (!this.multipleSelection || this.multipleSelection.length === 0) {
        this.$message.warning('请先选择要确认的考勤记录');
        return;
      }
      this.$confirm('确认将选中考勤记录批量标记为“已确认”？', '提示', {
        type: 'warning'
      }).then(async () => {
        // 收集所有勾选行manualRecords中的attendanceId
        const ids = this.multipleSelection
          .flatMap(row => (row.manualRecords || []))
          .map(record => record.attendanceId)
          .filter(id => id != null);
        const confirmedBy = this.$store.getters.userId || null;
        if (!ids.length) {
          this.$message.warning('所选记录没有可确认的考勤ID');
          return;
        }
        try {
          await batchConfirmAttendance({ attendanceIds: ids });
          this.$message.success('批量确认成功');
          this.getOverviewList(); // 刷新数据
        } catch (e) {
          this.$message.error('批量确认失败');
        }
      });
    },

    /** 表格行展开事件 */
    handleRowExpand(row, expandedRows) {
      if (expandedRows.find(r => r.attendanceId === row.attendanceId)) {
        // 行被展开，加载详细数据
        this.loadExpandedData(row);
      }
    },
    /** 加载展开行的详细数据 */
    async loadExpandedData(row) {
      try {
        // 获取钉钉原始打卡数据
        const dingtalkQuery = {
          employeeId: row.teacherId,
          attendanceDate: row.attendanceDate
        };
        const dingtalkRes = await getDingtalkAttendanceRecords(dingtalkQuery);
        const dingtalkRecords = dingtalkRes.rows || [];

        // 获取手动签到记录
        const manualQuery = {
          teacherId: row.teacherId,
          attendanceDate: row.attendanceDate
        };
        const manualRes = await listTeacherAttendance(manualQuery);
        // 只保留手动签到类型
        const manualRecords = (manualRes.rows || []).filter(item => item.dataSource === 'manual');

        this.$set(this.expandedData, row.attendanceId, {
          dingtalkRecords,
          manualRecords
        });
      } catch (error) {
        console.error('加载详细数据失败:', error);
        this.$message.error('加载详细数据失败');
      }
    },
    /** 获取数据来源标签类型 */
    getSourceTagType(source) {
      const typeMap = {
        'dingtalk': 'primary',
        'manual': 'success', 
        'mixed': 'warning'
      };
      return typeMap[source] || 'info';
    },
    /** 获取数据来源文本 */
    getSourceText(source) {
      const textMap = {
        'dingtalk': '钉钉',
        'manual': '手工',
        'mixed': '混合'
      };
      return textMap[source] || '未知';
    },
    /** 获取考勤状态标签类型 */
    // 状态标签类型直接由后端返回
    getStatusTagType(status, hasAttendance) {
      if (typeof status === 'string') {
        switch(status) {
          case 'present': return 'success';
          case 'late':
          case 'early': return 'warning';
          case 'absent': return 'danger';
          case 'sick':
          case 'personal':
          case 'vacation': return 'info';
          default: return hasAttendance ? 'info' : 'default';
        }
      }
      return hasAttendance ? 'info' : 'default';
    },
    /** 多选变化时触发，存储勾选的教师数据 */
    // handleSelectionChange 方法已在上面定义，这里删除重复定义
    /** 手动签到按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "教师批量签到";
      this.form.attendanceStatus = "1";
      this.form.attendanceDate = new Date().toISOString().slice(0, 10);
      // 回显已勾选教师
      // 假设this.multipleSelection是表格多选的教师数组，且每项有teacherId字段
      this.form.teacherIds = (this.multipleSelection || []).map(item => String(item.teacherId));
    },
    /** 确认考勤按钮操作 */
    async handleConfirmAttendance() {
      if (!this.multipleSelection || this.multipleSelection.length === 0) {
        this.$message.warning('请先选择要确认的考勤记录');
        return;
      }
      this.$confirm('确认将选中考勤记录批量标记为“已确认”？', '提示', {
        type: 'warning'
      }).then(async () => {
        // 收集所有勾选行manualRecords中的attendanceId
        const ids = this.multipleSelection
          .flatMap(row => (row.manualRecords || []))
          .map(record => record.attendanceId)
          .filter(id => id != null);
        const confirmedBy = this.$store.getters.userId || null;
        if (!ids.length) {
          this.$message.warning('所选记录没有可确认的考勤ID');
          return;
        }
        try {
          await batchConfirmAttendance({ attendanceIds: ids });
          this.$message.success('批量确认成功');
          this.getOverviewList(); // 刷新数据
        } catch (e) {
          this.$message.error('批量确认失败');
        }
      });
    },
    /** 重新生成按钮操作 */
    async handleGenerate(row) {
      debugger
      const attendanceDate = row.attendanceDate;
      // 尝试兼容不同字段名
      const dingtalkUserId = row.dingtalkUserId ;
      if (!attendanceDate || !dingtalkUserId) {
        this.$message.error('缺少考勤日期或钉钉用户ID，无法同步');
        return;
      }
      this.$confirm('是否根据钉钉重新同步该教师当天考勤数据？', "提示", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(async () => {
          try {
            this.loading = true;
            const workDateFrom = attendanceDate + ' 00:00:00';
            const workDateTo = attendanceDate + ' 23:59:59';
            const res = await syncAttendanceByUsers({
              workDateFrom,
              workDateTo,
              userIdList: [dingtalkUserId]
            });
            if (res && res.code === 200) {
              this.$message.success('钉钉同步成功');
              this.getOverviewList();
            } else {
              this.$message.error(res.msg || '钉钉同步失败');
            }
          } catch (e) {
            this.$message.error('钉钉同步异常: ' + (e.response?.data?.msg || e.message));
          } finally {
            this.loading = false;
          }
        }).catch(() => {});
    }
  }
};
</script>

<style scoped>
.attendance-container {
  padding: 20px;
}

.search-form {
  background: #f5f5f5;
  padding: 20px;
  border-radius: 4px;
  margin-bottom: 20px;
}

.operation-buttons {
  margin-bottom: 20px;
}

.operation-buttons .el-button {
  margin-right: 10px;
}

.app-container, .attendance-table {
  width: 100%;
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}
.attendance-table {
  background: white;
  border-radius: 4px;
}

.expanded-content {
  padding: 20px;
  background: #fafafa;
}

.detail-section {
  margin-bottom: 20px;
}

.detail-section h4 {
  margin-bottom: 10px;
  color: #303133;
  font-weight: 600;
}

.detail-table {
  margin-bottom: 15px;
}

.source-tag {
  margin-left: 8px;
}

.status-tag {
  margin-right: 8px;
}

.sync-dialog .el-form-item {
  margin-bottom: 20px;
}

.sync-dialog .el-date-picker {
  width: 100%;
}

.sync-dialog .el-select {
  width: 100%;
}

.data-integrity-tag {
  margin-left: 5px;
}

.confirmation-status {
  display: flex;
  align-items: center;
}

.confirmation-status .el-tag {
  margin-right: 5px;
}

.work-hours {
  font-weight: 600;
  color: #409eff;
}

.teacher-name {
  font-weight: 600;
  color: #303133;
}

.attendance-date {
  color: #606266;
}

.check-times {
  font-family: 'Courier New', monospace;
  font-size: 12px;
}

.expanded-table {
  font-size: 12px;
}

.expanded-table .el-table__row {
  background: #f9f9f9;
}

.location-info {
  color: #909399;
  font-size: 12px;
}

.remark-text {
  color: #909399;
  font-style: italic;
}
</style>

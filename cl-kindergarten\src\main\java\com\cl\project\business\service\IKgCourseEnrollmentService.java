package com.cl.project.business.service;

import java.util.List;
import com.cl.project.business.domain.KgCourseEnrollment;

/**
 * 托管报名记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IKgCourseEnrollmentService 
{
    /**
     * 查询托管报名记录
     * 
     * @param enrollmentId 托管报名记录ID
     * @return 托管报名记录
     */
    public KgCourseEnrollment selectKgCourseEnrollmentById(Long enrollmentId);

    /**
     * 查询托管报名记录列表
     * 
     * @param kgCourseEnrollment 托管报名记录
     * @return 托管报名记录集合
     */
    public List<KgCourseEnrollment> selectKgCourseEnrollmentList(KgCourseEnrollment kgCourseEnrollment);

    /**
     * 新增托管报名记录
     * 
     * @param kgCourseEnrollment 托管报名记录
     * @return 结果
     */
    public int insertKgCourseEnrollment(KgCourseEnrollment kgCourseEnrollment);

    /**
     * 修改托管报名记录
     * 
     * @param kgCourseEnrollment 托管报名记录
     * @return 结果
     */
    public int updateKgCourseEnrollment(KgCourseEnrollment kgCourseEnrollment);

    /**
     * 批量删除托管报名记录
     * 
     * @param enrollmentIds 需要删除的托管报名记录ID
     * @return 结果
     */
    public int deleteKgCourseEnrollmentByIds(Long[] enrollmentIds);

    /**
     * 删除托管报名记录信息
     * 
     * @param enrollmentId 托管报名记录ID
     * @return 结果
     */
    public int deleteKgCourseEnrollmentById(Long enrollmentId);
}

package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgCourseBillMapper;
import com.cl.project.business.domain.KgCourseBill;
import com.cl.project.business.service.IKgCourseBillService;

/**
 * 托管费账单Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgCourseBillServiceImpl implements IKgCourseBillService 
{
    @Autowired
    private KgCourseBillMapper kgCourseBillMapper;

    /**
     * 查询托管费账单
     * 
     * @param billId 托管费账单ID
     * @return 托管费账单
     */
    @Override
    public KgCourseBill selectKgCourseBillById(Long billId)
    {
        return kgCourseBillMapper.selectKgCourseBillById(billId);
    }

    /**
     * 查询托管费账单列表
     * 
     * @param kgCourseBill 托管费账单
     * @return 托管费账单
     */
    @Override
    public List<KgCourseBill> selectKgCourseBillList(KgCourseBill kgCourseBill)
    {
        return kgCourseBillMapper.selectKgCourseBillList(kgCourseBill);
    }

    /**
     * 新增托管费账单
     * 
     * @param kgCourseBill 托管费账单
     * @return 结果
     */
    @Override
    public int insertKgCourseBill(KgCourseBill kgCourseBill)
    {
        kgCourseBill.setCreateTime(DateUtils.getNowDate());
        return kgCourseBillMapper.insertKgCourseBill(kgCourseBill);
    }

    /**
     * 修改托管费账单
     * 
     * @param kgCourseBill 托管费账单
     * @return 结果
     */
    @Override
    public int updateKgCourseBill(KgCourseBill kgCourseBill)
    {
        kgCourseBill.setUpdateTime(DateUtils.getNowDate());
        return kgCourseBillMapper.updateKgCourseBill(kgCourseBill);
    }

    /**
     * 批量删除托管费账单
     * 
     * @param billIds 需要删除的托管费账单ID
     * @return 结果
     */
    @Override
    public int deleteKgCourseBillByIds(Long[] billIds)
    {
        return kgCourseBillMapper.deleteKgCourseBillByIds(billIds);
    }

    /**
     * 删除托管费账单信息
     * 
     * @param billId 托管费账单ID
     * @return 结果
     */
    @Override
    public int deleteKgCourseBillById(Long billId)
    {
        return kgCourseBillMapper.deleteKgCourseBillById(billId);
    }
}

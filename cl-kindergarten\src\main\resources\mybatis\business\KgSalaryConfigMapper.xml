<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgSalaryConfigMapper">
    
    <resultMap type="KgSalaryConfig" id="KgSalaryConfigResult">
        <result property="configId"    column="config_id"    />
        <result property="configName"    column="config_name"    />
        <result property="configType"    column="config_type"    />
        <result property="classType"    column="class_type"    />
        <result property="thresholdValue"    column="threshold_value"    />
        <result property="bonusAmount"    column="bonus_amount"    />
        <result property="penaltyAmount"    column="penalty_amount"    />
        <result property="calculationRule"    column="calculation_rule"    />
        <result property="status"    column="status"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgSalaryConfigVo">
        select config_id, config_name, config_type, class_type, threshold_value, bonus_amount, penalty_amount, calculation_rule, status, com_id, create_by, create_time, update_by, update_time, remark from kg_salary_config
    </sql>

    <select id="selectKgSalaryConfigList" parameterType="KgSalaryConfig" resultMap="KgSalaryConfigResult">
        <include refid="selectKgSalaryConfigVo"/>
        <where>  
            <if test="configName != null  and configName != ''"> and config_name like concat('%', #{configName}, '%')</if>
            <if test="configType != null  and configType != ''"> and config_type = #{configType}</if>
            <if test="classType != null  and classType != ''"> and class_type = #{classType}</if>
            <if test="thresholdValue != null "> and threshold_value = #{thresholdValue}</if>
            <if test="bonusAmount != null "> and bonus_amount = #{bonusAmount}</if>
            <if test="penaltyAmount != null "> and penalty_amount = #{penaltyAmount}</if>
            <if test="calculationRule != null  and calculationRule != ''"> and calculation_rule = #{calculationRule}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgSalaryConfigById" parameterType="Long" resultMap="KgSalaryConfigResult">
        <include refid="selectKgSalaryConfigVo"/>
        where config_id = #{configId}
    </select>
        
    <insert id="insertKgSalaryConfig" parameterType="KgSalaryConfig" useGeneratedKeys="true" keyProperty="configId">
        insert into kg_salary_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="configName != null and configName != ''">config_name,</if>
            <if test="configType != null and configType != ''">config_type,</if>
            <if test="classType != null">class_type,</if>
            <if test="thresholdValue != null">threshold_value,</if>
            <if test="bonusAmount != null">bonus_amount,</if>
            <if test="penaltyAmount != null">penalty_amount,</if>
            <if test="calculationRule != null">calculation_rule,</if>
            <if test="status != null">status,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="configName != null and configName != ''">#{configName},</if>
            <if test="configType != null and configType != ''">#{configType},</if>
            <if test="classType != null">#{classType},</if>
            <if test="thresholdValue != null">#{thresholdValue},</if>
            <if test="bonusAmount != null">#{bonusAmount},</if>
            <if test="penaltyAmount != null">#{penaltyAmount},</if>
            <if test="calculationRule != null">#{calculationRule},</if>
            <if test="status != null">#{status},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgSalaryConfig" parameterType="KgSalaryConfig">
        update kg_salary_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="configName != null and configName != ''">config_name = #{configName},</if>
            <if test="configType != null and configType != ''">config_type = #{configType},</if>
            <if test="classType != null">class_type = #{classType},</if>
            <if test="thresholdValue != null">threshold_value = #{thresholdValue},</if>
            <if test="bonusAmount != null">bonus_amount = #{bonusAmount},</if>
            <if test="penaltyAmount != null">penalty_amount = #{penaltyAmount},</if>
            <if test="calculationRule != null">calculation_rule = #{calculationRule},</if>
            <if test="status != null">status = #{status},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where config_id = #{configId}
    </update>

    <delete id="deleteKgSalaryConfigById" parameterType="Long">
        delete from kg_salary_config where config_id = #{configId}
    </delete>

    <delete id="deleteKgSalaryConfigByIds" parameterType="String">
        delete from kg_salary_config where config_id in 
        <foreach item="configId" collection="array" open="(" separator="," close=")">
            #{configId}
        </foreach>
    </delete>
    
</mapper>
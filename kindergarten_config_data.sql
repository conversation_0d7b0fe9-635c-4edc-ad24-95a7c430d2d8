-- =====================================================
-- 幼儿园管理系统配置数据
-- 包含: 基础配置、工资配置、支出类型、物品类别等
-- =====================================================

SET NAMES utf8mb4;
SET FOREIGN_KEY_CHECKS = 0;

-- =====================================================
-- 1. 园费配置初始数据
-- =====================================================

-- 园费配置初始数据
INSERT INTO `kg_tuition_config` VALUES 
(1, '托班', 20.00, 1200.00, 0.50, 600.00, '2024-01-01', '0', NULL, 'admin', NOW(), 'admin', NOW(), '托班园费配置'),
(2, '小班', 20.00, 1200.00, 0.50, 600.00, '2024-01-01', '0', NULL, 'admin', NOW(), 'admin', NOW(), '小班园费配置'),
(3, '中班', 20.00, 1200.00, 0.50, 600.00, '2024-01-01', '0', NULL, 'admin', NOW(), 'admin', NOW(), '中班园费配置'),
(4, '大班', 20.00, 1200.00, 0.50, 600.00, '2024-01-01', '0', NULL, 'admin', NOW(), 'admin', NOW(), '大班园费配置');

-- =====================================================
-- 2. 托管课程初始数据
-- =====================================================

-- 托管课程初始数据
INSERT INTO `kg_course` VALUES 
(1, '英语课', '英语', 30.00, 60, 8, 20, '0', NULL, 'admin', NOW(), 'admin', NOW(), '幼儿英语启蒙课程'),
(2, '美术课', '美术', 50.00, 90, 6, 15, '0', NULL, 'admin', NOW(), 'admin', NOW(), '幼儿美术创意课程'),
(3, '全脑开发', '全脑', 25.00, 45, 10, 25, '0', NULL, 'admin', NOW(), 'admin', NOW(), '全脑开发训练课程'),
(4, '军警体能', '军警', 35.00, 60, 8, 20, '0', NULL, 'admin', NOW(), 'admin', NOW(), '军警体能训练课程'),
(5, '音乐课', '音乐', 40.00, 60, 6, 18, '0', NULL, 'admin', NOW(), 'admin', NOW(), '幼儿音乐启蒙课程'),
(6, '舞蹈课', '舞蹈', 45.00, 60, 8, 16, '0', NULL, 'admin', NOW(), 'admin', NOW(), '幼儿舞蹈基础课程');

-- =====================================================
-- 3. 工资配置初始数据
-- =====================================================

-- 工资配置初始数据
INSERT INTO `kg_salary_config` VALUES 
(1, '满勤奖', 'attendance_bonus', NULL, 1.00, 200.00, 0.00, '出勤率100%奖励200元', '0', NULL, 'admin', NOW(), 'admin', NOW(), '满勤奖配置'),
(2, '大班出勤率奖励-优秀', 'attendance_rate_bonus', '大班', 0.91, 100.00, 0.00, '大班出勤率91%以上奖励100元', '0', NULL, 'admin', NOW(), 'admin', NOW(), '大班出勤率奖励'),
(3, '大班出勤率扣款', 'attendance_rate_penalty', '大班', 0.60, 0.00, 100.00, '大班出勤率60%以下扣除100元', '0', NULL, 'admin', NOW(), 'admin', NOW(), '大班出勤率扣款'),
(4, '中班出勤率奖励-优秀', 'attendance_rate_bonus', '中班', 0.91, 100.00, 0.00, '中班出勤率91%以上奖励100元', '0', NULL, 'admin', NOW(), 'admin', NOW(), '中班出勤率奖励'),
(5, '中班出勤率扣款', 'attendance_rate_penalty', '中班', 0.60, 0.00, 100.00, '中班出勤率60%以下扣除100元', '0', NULL, 'admin', NOW(), 'admin', NOW(), '中班出勤率扣款'),
(6, '小班出勤率奖励-优秀', 'attendance_rate_bonus', '小班', 0.81, 100.00, 0.00, '小班出勤率81%以上奖励100元', '0', NULL, 'admin', NOW(), 'admin', NOW(), '小班出勤率奖励'),
(7, '小班出勤率扣款', 'attendance_rate_penalty', '小班', 0.60, 0.00, 100.00, '小班出勤率60%以下扣除100元', '0', NULL, 'admin', NOW(), 'admin', NOW(), '小班出勤率扣款'),
(8, '托班出勤率奖励-优秀', 'attendance_rate_bonus', '托班', 0.81, 100.00, 0.00, '托班出勤率81%以上奖励100元', '0', NULL, 'admin', NOW(), 'admin', NOW(), '托班出勤率奖励'),
(9, '托班出勤率扣款', 'attendance_rate_penalty', '托班', 0.60, 0.00, 100.00, '托班出勤率60%以下扣除100元', '0', NULL, 'admin', NOW(), 'admin', NOW(), '托班出勤率扣款'),
(10, '托管课时费-8人以下', 'course_fee_low', NULL, 8.00, 5.00, 0.00, '托管班级8人以下，每课时5元', '0', NULL, 'admin', NOW(), 'admin', NOW(), '托管课时费-低'),
(11, '托管课时费-8人以上', 'course_fee_high', NULL, 8.00, 10.00, 0.00, '托管班级8人以上，每课时10元', '0', NULL, 'admin', NOW(), 'admin', NOW(), '托管课时费-高'),
(12, '报名奖励-达标', 'enrollment_bonus', NULL, 0.50, 5.00, 0.00, '班级报名率达到50%，每课时奖励5元，否则1元', '0', NULL, 'admin', NOW(), 'admin', NOW(), '报名奖励配置'),
(13, '新生奖励', 'new_student_bonus', NULL, 1.00, 75.00, 0.00, '每新增一名学生奖励50-100元', '0', NULL, 'admin', NOW(), 'admin', NOW(), '新生奖励配置'),
(14, '退园扣款', 'withdrawal_penalty', NULL, 1.00, 0.00, 50.00, '每退园一名学生扣款金额', '0', NULL, 'admin', NOW(), 'admin', NOW(), '退园扣款配置');

-- =====================================================
-- 4. 支出类型初始数据
-- =====================================================

-- 支出类型初始数据
INSERT INTO `kg_expense_type` VALUES 
(1, '人员支出', 'PERSONNEL', 0, 1, 1, '0', NULL, 'admin', NOW(), 'admin', NOW(), '人员相关支出'),
(2, '工资', 'SALARY', 1, 1, 1, '0', NULL, 'admin', NOW(), 'admin', NOW(), '员工工资'),
(3, '社保', 'SOCIAL_INSURANCE', 1, 2, 1, '0', NULL, 'admin', NOW(), 'admin', NOW(), '社会保险'),
(4, '运营支出', 'OPERATION', 0, 2, 1, '0', NULL, 'admin', NOW(), 'admin', NOW(), '日常运营支出'),
(5, '伙食费', 'MEAL', 4, 1, 1, '0', NULL, 'admin', NOW(), 'admin', NOW(), '学生伙食费用'),
(6, '房租', 'RENT', 4, 2, 1, '0', NULL, 'admin', NOW(), 'admin', NOW(), '场地租金'),
(7, '水电燃气', 'UTILITIES', 4, 3, 1, '0', NULL, 'admin', NOW(), 'admin', NOW(), '水电燃气费'),
(8, '电话费', 'PHONE', 4, 4, 1, '0', NULL, 'admin', NOW(), 'admin', NOW(), '通讯费用'),
(9, '保洁费', 'CLEANING', 4, 5, 1, '0', NULL, 'admin', NOW(), 'admin', NOW(), '保洁服务费'),
(10, '办公费', 'OFFICE', 4, 6, 1, '0', NULL, 'admin', NOW(), 'admin', NOW(), '办公用品费用'),
(11, '保险费', 'INSURANCE', 4, 7, 1, '0', NULL, 'admin', NOW(), 'admin', NOW(), '各类保险费用'),
(12, '其他支出', 'OTHER', 0, 3, 1, '0', NULL, 'admin', NOW(), 'admin', NOW(), '其他类型支出'),
(13, '预留项目1', 'RESERVE1', 12, 1, 0, '0', NULL, 'admin', NOW(), 'admin', NOW(), '预留支出项目1'),
(14, '预留项目2', 'RESERVE2', 12, 2, 0, '0', NULL, 'admin', NOW(), 'admin', NOW(), '预留支出项目2'),
(15, '预留项目3', 'RESERVE3', 12, 3, 0, '0', NULL, 'admin', NOW(), 'admin', NOW(), '预留支出项目3'),
(16, '预留项目4', 'RESERVE4', 12, 4, 0, '0', NULL, 'admin', NOW(), 'admin', NOW(), '预留支出项目4'),
(17, '预留项目5', 'RESERVE5', 12, 5, 0, '0', NULL, 'admin', NOW(), 'admin', NOW(), '预留支出项目5'),
(18, '预留项目6', 'RESERVE6', 12, 6, 0, '0', NULL, 'admin', NOW(), 'admin', NOW(), '预留支出项目6'),
(19, '预留项目7', 'RESERVE7', 12, 7, 0, '0', NULL, 'admin', NOW(), 'admin', NOW(), '预留支出项目7'),
(20, '预留项目8', 'RESERVE8', 12, 8, 0, '0', NULL, 'admin', NOW(), 'admin', NOW(), '预留支出项目8'),
(21, '预留项目9', 'RESERVE9', 12, 9, 0, '0', NULL, 'admin', NOW(), 'admin', NOW(), '预留支出项目9'),
(22, '预留项目10', 'RESERVE10', 12, 10, 0, '0', NULL, 'admin', NOW(), 'admin', NOW(), '预留支出项目10');

-- =====================================================
-- 5. 物品类别初始数据
-- =====================================================

-- 物品类别初始数据
INSERT INTO `kg_item_category` VALUES 
(1, '教学用品', 'TEACHING', 0, 1, '0', NULL, 'admin', NOW(), 'admin', NOW(), '教学相关用品'),
(2, '文具用品', 'STATIONERY', 1, 1, '0', NULL, 'admin', NOW(), 'admin', NOW(), '文具类用品'),
(3, '玩具教具', 'TOYS', 1, 2, '0', NULL, 'admin', NOW(), 'admin', NOW(), '玩具和教具'),
(4, '图书资料', 'BOOKS', 1, 3, '0', NULL, 'admin', NOW(), 'admin', NOW(), '图书和学习资料'),
(5, '生活用品', 'DAILY', 0, 2, '0', NULL, 'admin', NOW(), 'admin', NOW(), '日常生活用品'),
(6, '清洁用品', 'CLEANING', 5, 1, '0', NULL, 'admin', NOW(), 'admin', NOW(), '清洁卫生用品'),
(7, '餐具用品', 'TABLEWARE', 5, 2, '0', NULL, 'admin', NOW(), 'admin', NOW(), '餐具和厨房用品'),
(8, '床上用品', 'BEDDING', 5, 3, '0', NULL, 'admin', NOW(), 'admin', NOW(), '床上用品'),
(9, '办公用品', 'OFFICE', 0, 3, '0', NULL, 'admin', NOW(), 'admin', NOW(), '办公相关用品'),
(10, '电子设备', 'ELECTRONIC', 9, 1, '0', NULL, 'admin', NOW(), 'admin', NOW(), '电子设备'),
(11, '办公文具', 'OFFICE_STATIONERY', 9, 2, '0', NULL, 'admin', NOW(), 'admin', NOW(), '办公文具'),
(12, '医疗保健', 'MEDICAL', 0, 4, '0', NULL, 'admin', NOW(), 'admin', NOW(), '医疗保健用品'),
(13, '药品', 'MEDICINE', 12, 1, '0', NULL, 'admin', NOW(), 'admin', NOW(), '常用药品'),
(14, '保健用品', 'HEALTH', 12, 2, '0', NULL, 'admin', NOW(), 'admin', NOW(), '保健护理用品');

-- =====================================================
-- 6. 时间段配置初始数据
-- =====================================================

-- 时间段配置初始数据
INSERT INTO `kg_time_config` VALUES 
(1, '园费时间段', 'tuition', '09:00:00', '15:50:00', 1, NULL, 'admin', NOW(), 'admin', NOW(), '园费计费时间段：9:00-15:50'),
(2, '托管时间段-下午', 'course', '15:00:00', '17:00:00', 1, NULL, 'admin', NOW(), 'admin', NOW(), '下午托管时间段：15:00-17:00'),
(3, '托管时间段-晚上', 'course', '18:00:00', '20:00:00', 1, NULL, 'admin', NOW(), 'admin', NOW(), '晚上托管时间段：18:00-20:00'),
(4, '托管时间段-周末', 'course', '08:00:00', '18:00:00', 1, NULL, 'admin', NOW(), 'admin', NOW(), '周末托管时间段：8:00-18:00');

-- =====================================================
-- 7. 移动端权限配置初始数据
-- =====================================================

-- 移动端权限配置初始数据
INSERT INTO `kg_mobile_permission_config` VALUES 
(1, 'kg_head_teacher', 'attendance', 'class', '{"start_time": "07:00", "end_time": "18:00", "weekdays": [1,2,3,4,5]}', 1, 0, '0', NULL, 'admin', NOW(), 'admin', NOW(), '班主任考勤权限：工作日7:00-18:00，需要位置验证'),
(2, 'kg_assistant_teacher', 'attendance', 'class', '{"start_time": "07:00", "end_time": "18:00", "weekdays": [1,2,3,4,5]}', 1, 1, '0', NULL, 'admin', NOW(), 'admin', NOW(), '副班主任考勤权限：工作日7:00-18:00，需要位置验证和审批'),
(3, 'kg_course_teacher', 'course', 'course', '{"flexible": true, "course_time_only": true}', 0, 0, '0', NULL, 'admin', NOW(), 'admin', NOW(), '托管教师课程权限：仅在课程时间内，无位置限制'),
(4, 'kg_finance', 'finance', 'all', '{"start_time": "08:00", "end_time": "18:00", "weekdays": [1,2,3,4,5]}', 0, 0, '0', NULL, 'admin', NOW(), 'admin', NOW(), '财务人员权限：工作日8:00-18:00'),
(5, 'kg_director', 'all', 'all', '{"flexible": true}', 0, 0, '0', NULL, 'admin', NOW(), 'admin', NOW(), '园长权限：无时间和位置限制'),
(6, 'kg_academic_director', 'attendance', 'all', '{"start_time": "07:00", "end_time": "19:00", "weekdays": [1,2,3,4,5,6,7]}', 0, 0, '0', NULL, 'admin', NOW(), 'admin', NOW(), '教务主任权限：全周7:00-19:00'),
(7, 'kg_health_teacher', 'student', 'all', '{"start_time": "08:00", "end_time": "17:00", "weekdays": [1,2,3,4,5]}', 1, 0, '0', NULL, 'admin', NOW(), 'admin', NOW(), '保健医权限：工作日8:00-17:00，需要位置验证');

SET FOREIGN_KEY_CHECKS = 1;

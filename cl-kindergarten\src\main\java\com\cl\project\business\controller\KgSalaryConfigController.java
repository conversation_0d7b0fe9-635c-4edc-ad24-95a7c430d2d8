package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgSalaryConfig;
import com.cl.project.business.service.IKgSalaryConfigService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 工资配置Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/salary-config")
public class KgSalaryConfigController extends BaseController
{
    @Autowired
    private IKgSalaryConfigService kgSalaryConfigService;

    /**
     * 查询工资配置列表
     */
    @SaCheckPermission("business:config:list")
    @GetMapping("/list")
    public TableDataInfo list(KgSalaryConfig kgSalaryConfig)
    {
        startPage();
        List<KgSalaryConfig> list = kgSalaryConfigService.selectKgSalaryConfigList(kgSalaryConfig);
        return getDataTable(list);
    }

    /**
     * 导出工资配置列表
     */
    @SaCheckPermission("business:config:export")
    @Log(title = "工资配置", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgSalaryConfig kgSalaryConfig)
    {
        List<KgSalaryConfig> list = kgSalaryConfigService.selectKgSalaryConfigList(kgSalaryConfig);
        ExcelUtil<KgSalaryConfig> util = new ExcelUtil<KgSalaryConfig>(KgSalaryConfig.class);
        return util.exportExcel(list, "config");
    }

    /**
     * 获取工资配置详细信息
     */
    @SaCheckPermission("business:config:query")
    @GetMapping(value = "/{configId}")
    public AjaxResult getInfo(@PathVariable("configId") Long configId)
    {
        return AjaxResult.success(kgSalaryConfigService.selectKgSalaryConfigById(configId));
    }

    /**
     * 新增工资配置
     */
    @SaCheckPermission("business:config:add")
    @Log(title = "工资配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgSalaryConfig kgSalaryConfig)
    {
        return toAjax(kgSalaryConfigService.insertKgSalaryConfig(kgSalaryConfig));
    }

    /**
     * 修改工资配置
     */
    @SaCheckPermission("business:config:edit")
    @Log(title = "工资配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgSalaryConfig kgSalaryConfig)
    {
        return toAjax(kgSalaryConfigService.updateKgSalaryConfig(kgSalaryConfig));
    }

    /**
     * 删除工资配置
     */
    @SaCheckPermission("business:config:remove")
    @Log(title = "工资配置", businessType = BusinessType.DELETE)
	@DeleteMapping("/{configIds}")
    public AjaxResult remove(@PathVariable Long[] configIds)
    {
        return toAjax(kgSalaryConfigService.deleteKgSalaryConfigByIds(configIds));
    }
}

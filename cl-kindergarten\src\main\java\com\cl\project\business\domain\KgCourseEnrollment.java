package com.cl.project.business.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 托管报名记录对象 kg_course_enrollment
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgCourseEnrollment extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 报名ID */
    private Long enrollmentId;

    /** 幼儿ID，关联kg_student.student_id */
    @Excel(name = "幼儿ID，关联kg_student.student_id")
    private Long studentId;

    /** 课程ID，关联kg_course.course_id */
    @Excel(name = "课程ID，关联kg_course.course_id")
    private Long courseId;

    /** 班级ID，关联kg_class.class_id */
    @Excel(name = "班级ID，关联kg_class.class_id")
    private Long classId;

    /** 报名日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "报名日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date enrollmentDate;

    /** 总课时数 */
    @Excel(name = "总课时数")
    private Long totalSessions;

    /** 已用课时数 */
    @Excel(name = "已用课时数")
    private Long usedSessions;

    /** 剩余课时数 */
    @Excel(name = "剩余课时数")
    private Long remainingSessions;

    /** 赠送课时数 */
    @Excel(name = "赠送课时数")
    private Long giftSessions;

    /** 总金额 */
    @Excel(name = "总金额")
    private BigDecimal totalAmount;

    /** 已付金额 */
    @Excel(name = "已付金额")
    private BigDecimal paidAmount;

    /** 状态（active活跃、suspended暂停、completed完成、cancelled取消） */
    @Excel(name = "状态", readConverterExp = "a=ctive活跃、suspended暂停、completed完成、cancelled取消")
    private String status;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    public void setEnrollmentId(Long enrollmentId) 
    {
        this.enrollmentId = enrollmentId;
    }

    public Long getEnrollmentId() 
    {
        return enrollmentId;
    }
    public void setStudentId(Long studentId) 
    {
        this.studentId = studentId;
    }

    public Long getStudentId() 
    {
        return studentId;
    }
    public void setCourseId(Long courseId) 
    {
        this.courseId = courseId;
    }

    public Long getCourseId() 
    {
        return courseId;
    }
    public void setClassId(Long classId) 
    {
        this.classId = classId;
    }

    public Long getClassId() 
    {
        return classId;
    }
    public void setEnrollmentDate(Date enrollmentDate) 
    {
        this.enrollmentDate = enrollmentDate;
    }

    public Date getEnrollmentDate() 
    {
        return enrollmentDate;
    }
    public void setTotalSessions(Long totalSessions) 
    {
        this.totalSessions = totalSessions;
    }

    public Long getTotalSessions() 
    {
        return totalSessions;
    }
    public void setUsedSessions(Long usedSessions) 
    {
        this.usedSessions = usedSessions;
    }

    public Long getUsedSessions() 
    {
        return usedSessions;
    }
    public void setRemainingSessions(Long remainingSessions) 
    {
        this.remainingSessions = remainingSessions;
    }

    public Long getRemainingSessions() 
    {
        return remainingSessions;
    }
    public void setGiftSessions(Long giftSessions) 
    {
        this.giftSessions = giftSessions;
    }

    public Long getGiftSessions() 
    {
        return giftSessions;
    }
    public void setTotalAmount(BigDecimal totalAmount) 
    {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getTotalAmount() 
    {
        return totalAmount;
    }
    public void setPaidAmount(BigDecimal paidAmount) 
    {
        this.paidAmount = paidAmount;
    }

    public BigDecimal getPaidAmount() 
    {
        return paidAmount;
    }
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("enrollmentId", getEnrollmentId())
            .append("studentId", getStudentId())
            .append("courseId", getCourseId())
            .append("classId", getClassId())
            .append("enrollmentDate", getEnrollmentDate())
            .append("totalSessions", getTotalSessions())
            .append("usedSessions", getUsedSessions())
            .append("remainingSessions", getRemainingSessions())
            .append("giftSessions", getGiftSessions())
            .append("totalAmount", getTotalAmount())
            .append("paidAmount", getPaidAmount())
            .append("status", getStatus())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

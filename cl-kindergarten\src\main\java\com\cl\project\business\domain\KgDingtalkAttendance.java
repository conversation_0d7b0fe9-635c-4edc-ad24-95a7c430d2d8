package com.cl.project.business.domain;

import java.util.Date;

import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 钉钉打卡记录对象 kg_dingtalk_attendance
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgDingtalkAttendance extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 记录ID */
    private Long recordId;

    /** 钉钉用户ID */
    @Excel(name = "钉钉用户ID")
    private String userId;

    /** 关联员工ID，关联kg_teacher.teacher_id */
    @Excel(name = "关联员工ID，关联kg_teacher.teacher_id")
    private Long employeeId;

    /** 关联学生ID，关联kg_student.student_id */
    @Excel(name = "关联学生ID，关联kg_student.student_id")
    private Long studentId;

    /** 打卡时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "打卡时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date checkTime;

    /** 打卡类型（OnDuty上班、OffDuty下班） */
    @Excel(name = "打卡类型", readConverterExp = "O=nDuty上班、OffDuty下班")
    private String checkType;

    /** 定位结果（Normal正常、Outside范围外） */
    @Excel(name = "定位结果", readConverterExp = "N=ormal正常、Outside范围外")
    private String locationResult;

    /** 位置标题 */
    @Excel(name = "位置标题")
    private String locationTitle;

    /** 位置详情 */
    @Excel(name = "位置详情")
    private String locationDetail;

    /** 计划打卡时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "计划打卡时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date planCheckTime;

    /** 是否已处理（0未处理 1已处理） */
    @Excel(name = "是否已处理", readConverterExp = "0=未处理,1=已处理")
    private Long isProcessed;

    /** 处理时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "处理时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date processedTime;

    /** 费用类型（tuition园费、course托管费） */
    @Excel(name = "费用类型", readConverterExp = "t=uition园费、course托管费")
    private String feeType;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    public void setRecordId(Long recordId) 
    {
        this.recordId = recordId;
    }

    public Long getRecordId() 
    {
        return recordId;
    }
    public void setUserId(String userId) 
    {
        this.userId = userId;
    }

    public String getUserId() 
    {
        return userId;
    }
    public void setEmployeeId(Long employeeId) 
    {
        this.employeeId = employeeId;
    }

    public Long getEmployeeId() 
    {
        return employeeId;
    }
    public void setStudentId(Long studentId) 
    {
        this.studentId = studentId;
    }

    public Long getStudentId() 
    {
        return studentId;
    }
    public void setCheckTime(Date checkTime) 
    {
        this.checkTime = checkTime;
    }

    public Date getCheckTime() 
    {
        return checkTime;
    }
    public void setCheckType(String checkType) 
    {
        this.checkType = checkType;
    }

    public String getCheckType() 
    {
        return checkType;
    }
    public void setLocationResult(String locationResult) 
    {
        this.locationResult = locationResult;
    }

    public String getLocationResult() 
    {
        return locationResult;
    }
    public void setLocationTitle(String locationTitle) 
    {
        this.locationTitle = locationTitle;
    }

    public String getLocationTitle() 
    {
        return locationTitle;
    }
    public void setLocationDetail(String locationDetail) 
    {
        this.locationDetail = locationDetail;
    }

    public String getLocationDetail() 
    {
        return locationDetail;
    }
    public void setPlanCheckTime(Date planCheckTime) 
    {
        this.planCheckTime = planCheckTime;
    }

    public Date getPlanCheckTime() 
    {
        return planCheckTime;
    }
    public void setIsProcessed(Long isProcessed) 
    {
        this.isProcessed = isProcessed;
    }

    public Long getIsProcessed() 
    {
        return isProcessed;
    }
    public void setProcessedTime(Date processedTime) 
    {
        this.processedTime = processedTime;
    }

    public Date getProcessedTime() 
    {
        return processedTime;
    }
    public void setFeeType(String feeType) 
    {
        this.feeType = feeType;
    }

    public String getFeeType() 
    {
        return feeType;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("recordId", getRecordId())
            .append("userId", getUserId())
            .append("employeeId", getEmployeeId())
            .append("studentId", getStudentId())
            .append("checkTime", getCheckTime())
            .append("checkType", getCheckType())
            .append("locationResult", getLocationResult())
            .append("locationTitle", getLocationTitle())
            .append("locationDetail", getLocationDetail())
            .append("planCheckTime", getPlanCheckTime())
            .append("isProcessed", getIsProcessed())
            .append("processedTime", getProcessedTime())
            .append("feeType", getFeeType())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

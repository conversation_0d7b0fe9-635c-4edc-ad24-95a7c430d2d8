package com.cl.framework.redis;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Redis重试工具类
 * 提供简单的异常判断和重试逻辑
 * 
 * <AUTHOR>
 */
public class RedisRetryUtils {
    
    private static final Logger log = LoggerFactory.getLogger(RedisRetryUtils.class);
    
    /**
     * 判断异常是否为连接相关异常，可以重试
     * @param e 异常
     * @return 是否可重试
     */
    public static boolean isConnectionException(Exception e) {
        if (e == null) {
            return false;
        }
        
        String exceptionClass = e.getClass().getSimpleName();
        String message = e.getMessage();
        
        // 检查异常类型
        if (exceptionClass.contains("Connection") || 
            exceptionClass.contains("RedisConnection") || 
            exceptionClass.contains("Socket")) {
            return true;
        }
        
        // 检查异常信息
        if (message != null) {
            return message.contains("Connection reset") || 
                   message.contains("connection") || 
                   message.contains("Connection") ||
                   message.contains("timeout") || 
                   message.contains("Timeout") ||
                   message.contains("I/O error") ||
                   message.contains("Broken pipe");
        }
        
        return false;
    }
    
    /**
     * 记录重试信息
     * @param operationName 操作名称
     * @param attempt 尝试次数
     * @param maxAttempts 最大尝试次数
     * @param exception 异常
     */
    public static void logRetryAttempt(String operationName, int attempt, int maxAttempts, Exception exception) {
        if (attempt == maxAttempts) {
            log.error("Redis操作最终失败: {} (尝试了{}次) - {}", 
                     operationName, maxAttempts, exception.getMessage());
        } else {
            log.warn("Redis操作失败，正在重试: {} (第{}/{}次) - {}", 
                    operationName, attempt, maxAttempts, exception.getMessage());
        }
    }
    
    /**
     * 获取重试延迟时间（毫秒）
     * @param attempt 尝试次数（从1开始）
     * @return 延迟时间
     */
    public static long getRetryDelay(int attempt) {
        // 递增延迟：1秒、2秒、3秒...
        return 1000L * attempt;
    }
}

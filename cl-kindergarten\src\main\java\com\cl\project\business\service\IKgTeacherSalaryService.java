package com.cl.project.business.service;

import java.util.List;
import com.cl.project.business.domain.KgTeacherSalary;

/**
 * 教师工资Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IKgTeacherSalaryService 
{
    /**
     * 查询教师工资
     * 
     * @param salaryId 教师工资ID
     * @return 教师工资
     */
    public KgTeacherSalary selectKgTeacherSalaryById(Long salaryId);

    /**
     * 查询教师工资列表
     * 
     * @param kgTeacherSalary 教师工资
     * @return 教师工资集合
     */
    public List<KgTeacherSalary> selectKgTeacherSalaryList(KgTeacherSalary kgTeacherSalary);

    /**
     * 新增教师工资
     * 
     * @param kgTeacherSalary 教师工资
     * @return 结果
     */
    public int insertKgTeacherSalary(KgTeacherSalary kgTeacherSalary);

    /**
     * 修改教师工资
     * 
     * @param kgTeacherSalary 教师工资
     * @return 结果
     */
    public int updateKgTeacherSalary(KgTeacherSalary kgTeacherSalary);

    /**
     * 批量删除教师工资
     * 
     * @param salaryIds 需要删除的教师工资ID
     * @return 结果
     */
    public int deleteKgTeacherSalaryByIds(Long[] salaryIds);

    /**
     * 删除教师工资信息
     * 
     * @param salaryId 教师工资ID
     * @return 结果
     */
    public int deleteKgTeacherSalaryById(Long salaryId);
}

import request from '@/utils/request'

// 查询库存记录列表
export function listRecord(query) {
  return request({
    url: '/business/stock-record/list',
    method: 'get',
    params: query
  })
}

// 查询库存记录详细
export function getRecord(recordId) {
  return request({
    url: '/business/stock-record/' + recordId,
    method: 'get'
  })
}

// 新增库存记录
export function addRecord(data) {
  return request({
    url: '/business/stock-record',
    method: 'post',
    data: data
  })
}

// 修改库存记录
export function updateRecord(data) {
  return request({
    url: '/business/stock-record',
    method: 'put',
    data: data
  })
}

// 删除库存记录
export function delRecord(recordId) {
  return request({
    url: '/business/stock-record/' + recordId,
    method: 'delete'
  })
}

// 导出库存记录
export function exportRecord(query) {
  return request({
    url: '/business/stock-record/export',
    method: 'get',
    params: query
  })
}

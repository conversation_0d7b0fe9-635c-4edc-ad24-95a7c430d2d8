package com.cl.project.business.controller;

import java.util.List;
import javax.servlet.http.HttpServletResponse;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.project.business.domain.dto.DingtalkDepartmentResponse;
import com.cl.project.business.service.IDingtalkApiService;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;

/**
 * 钉钉学生同步Controller
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
@Api(tags = "钉钉学生同步管理")
@RestController
@RequestMapping("/business/dingtalk/student")
public class DingtalkStudentSyncController extends BaseController 
{
    @Autowired
    private IDingtalkApiService dingtalkApiService;

    /**
     * 同步指定部门的学生信息
     */
    @ApiOperation("同步指定部门学生")
    @PostMapping("/syncByDepartment")
    public AjaxResult syncStudentsByDepartment(
        @ApiParam(value = "部门ID", required = true) @RequestParam Long deptId)
    {
        try 
        {
            int syncCount = dingtalkApiService.syncStudentsFromDepartment(deptId);
            return AjaxResult.success("同步完成", syncCount);
        } 
        catch (Exception e) 
        {
            return AjaxResult.error("同步失败：" + e.getMessage());
        }
    }

    /**
     * 同步所有部门的学生信息
     */
    @ApiOperation("同步所有学生")
    @PostMapping("/syncAll")
    public AjaxResult syncAllStudents()
    {
        try 
        {
            int syncCount = dingtalkApiService.syncAllStudents();
            return AjaxResult.success("同步成功，共同步 " + syncCount + " 个学生");
        } 
        catch (Exception e) 
        {
            return AjaxResult.error("同步失败：" + e.getMessage());
        }
    }

    /**
     * 获取钉钉部门列表
     */
    @ApiOperation("获取部门列表")
    @PostMapping("/departments")
    public AjaxResult getDepartments(
        @ApiParam(value = "父部门ID，默认为1（根部门）", required = false) @RequestParam(defaultValue = "1") Long parentDeptId)
    {
        try 
        {
            List<DingtalkDepartmentResponse.Department> departments = dingtalkApiService.getDepartmentList(parentDeptId);
            return AjaxResult.success(departments);
        } 
        catch (Exception e) 
        {
            return AjaxResult.error("获取部门列表失败：" + e.getMessage());
        }
    }
}

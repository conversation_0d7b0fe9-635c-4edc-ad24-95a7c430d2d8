package com.cl.project.business.domain.dto;

import java.math.BigDecimal;
import java.util.Date;
import java.util.List;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.cl.project.business.domain.KgDingtalkAttendance;
import com.cl.project.business.domain.KgTeacherAttendance;

/**
 * 教师考勤概览DTO
 * 用于展示所有教师的基本信息和当日考勤状态
 * 
 * <AUTHOR>
 * @date 2025-07-31
 */
public class TeacherAttendanceOverviewDto {
    /** 钉钉打卡记录列表 */
    private List<KgDingtalkAttendance> dingtalkRecords;

    /**
     * 手动签到记录
     */
    private List<KgTeacherAttendance> manualRecords;

    /** 教师ID */
    private Long teacherId;

    private String dingtalkUserId;
    /** 教师姓名 */
    private String teacherName;
    
    /** 考勤日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    private Date attendanceDate;
    
    /** 签到时间 */
    @JsonFormat(pattern = "HH:mm:ss")
    private Date checkInTime;

    
    /** 考勤状态 */
    private String attendanceStatus;
    
    /** 工作时长 */
    private BigDecimal workHours;
    
    /** 是否确认 */
    private Long isConfirmed;
    
    /** 是否有打卡记录 */
    private Boolean hasAttendance;
    
    /** 打卡状态描述 */
    private String attendanceStatusText;
    
    // 构造方法
    public TeacherAttendanceOverviewDto() {
        this.hasAttendance = false;
    }
    
    // Getter和Setter方法
    public java.util.List<com.cl.project.business.domain.KgDingtalkAttendance> getDingtalkRecords() {
        return dingtalkRecords;
    }
    public void setDingtalkRecords(java.util.List<com.cl.project.business.domain.KgDingtalkAttendance> dingtalkRecords) {
        this.dingtalkRecords = dingtalkRecords;
    }
    public Long getTeacherId() {
        return teacherId;
    }
    
    public void setTeacherId(Long teacherId) {
        this.teacherId = teacherId;
    }
    
    public String getTeacherName() {
        return teacherName;
    }
    
    public void setTeacherName(String teacherName) {
        this.teacherName = teacherName;
    }
    
    public Date getAttendanceDate() {
        return attendanceDate;
    }
    
    public void setAttendanceDate(Date attendanceDate) {
        this.attendanceDate = attendanceDate;
    }
    
    public Date getCheckInTime() {
        return checkInTime;
    }
    
    public void setCheckInTime(Date checkInTime) {
        this.checkInTime = checkInTime;
        this.hasAttendance = checkInTime != null;
    }
    

    
    public String getAttendanceStatus() {
        return attendanceStatus;
    }
    
    public void setAttendanceStatus(String attendanceStatus) {
        this.attendanceStatus = attendanceStatus;
        this.attendanceStatusText = getAttendanceStatusText(attendanceStatus);
    }
    
    public BigDecimal getWorkHours() {
        return workHours;
    }
    
    public void setWorkHours(BigDecimal workHours) {
        this.workHours = workHours;
    }
    
    public Long getIsConfirmed() {
        return isConfirmed;
    }
    
    public void setIsConfirmed(Long isConfirmed) {
        this.isConfirmed = isConfirmed;
    }
    
    public Boolean getHasAttendance() {
        return hasAttendance;
    }
    
    public void setHasAttendance(Boolean hasAttendance) {
        this.hasAttendance = hasAttendance;
    }
    
    public String getAttendanceStatusText() {
        return attendanceStatusText;
    }
    
    public void setAttendanceStatusText(String attendanceStatusText) {
        this.attendanceStatusText = attendanceStatusText;
    }

    public String getDingtalkUserId() {
            return dingtalkUserId;
    }
    public void setDingtalkUserId(String dingtalkUserId) {
            this.dingtalkUserId = dingtalkUserId;
    }

    /**
     * 获取考勤状态文本描述
     */
    private String getAttendanceStatusText(String status) {
        if (status == null) {
            return "未打卡";
        }
        
        switch (status) {
            case "present":
                return "出勤";
            case "absent":
                return "缺勤";
            case "late":
                return "迟到";
            case "early":
                return "早退";
            case "sick":
                return "病假";
            case "personal":
                return "事假";
            case "vacation":
                return "休假";
            default:
                return "未知";
        }
    }
    
    /**
     * 获取打卡状态标签类型
     */
    public String getStatusTagType() {
        if (!hasAttendance) {
            return "info";
        }
        
        if (attendanceStatus == null) {
            return "warning";
        }
        
        switch (attendanceStatus) {
            case "present":
                return "success";
            case "late":
            case "early":
                return "warning";
            case "absent":
                return "danger";
            case "sick":
            case "personal":
            case "vacation":
                return "info";
            default:
                return "info";
        }
    }

    public void setManualRecords(List<KgTeacherAttendance> manualList) {
        this.manualRecords = manualList;
    }
    public List<KgTeacherAttendance> getManualRecords() {
        return manualRecords;
    }
}

<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgExpenseTypeMapper">
    
    <resultMap type="KgExpenseType" id="KgExpenseTypeResult">
        <result property="typeId"    column="type_id"    />
        <result property="typeName"    column="type_name"    />
        <result property="typeCode"    column="type_code"    />
        <result property="parentId"    column="parent_id"    />
        <result property="sortOrder"    column="sort_order"    />
        <result property="isSystem"    column="is_system"    />
        <result property="status"    column="status"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgExpenseTypeVo">
        select type_id, type_name, type_code, parent_id, sort_order, is_system, status, com_id, create_by, create_time, update_by, update_time, remark from kg_expense_type
    </sql>

    <select id="selectKgExpenseTypeList" parameterType="KgExpenseType" resultMap="KgExpenseTypeResult">
        <include refid="selectKgExpenseTypeVo"/>
        <where>  
            <if test="typeName != null  and typeName != ''"> and type_name like concat('%', #{typeName}, '%')</if>
            <if test="typeCode != null  and typeCode != ''"> and type_code = #{typeCode}</if>
            <if test="parentId != null "> and parent_id = #{parentId}</if>
            <if test="sortOrder != null "> and sort_order = #{sortOrder}</if>
            <if test="isSystem != null "> and is_system = #{isSystem}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgExpenseTypeById" parameterType="Long" resultMap="KgExpenseTypeResult">
        <include refid="selectKgExpenseTypeVo"/>
        where type_id = #{typeId}
    </select>
        
    <insert id="insertKgExpenseType" parameterType="KgExpenseType" useGeneratedKeys="true" keyProperty="typeId">
        insert into kg_expense_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="typeName != null and typeName != ''">type_name,</if>
            <if test="typeCode != null and typeCode != ''">type_code,</if>
            <if test="parentId != null">parent_id,</if>
            <if test="sortOrder != null">sort_order,</if>
            <if test="isSystem != null">is_system,</if>
            <if test="status != null">status,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="typeName != null and typeName != ''">#{typeName},</if>
            <if test="typeCode != null and typeCode != ''">#{typeCode},</if>
            <if test="parentId != null">#{parentId},</if>
            <if test="sortOrder != null">#{sortOrder},</if>
            <if test="isSystem != null">#{isSystem},</if>
            <if test="status != null">#{status},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgExpenseType" parameterType="KgExpenseType">
        update kg_expense_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="typeName != null and typeName != ''">type_name = #{typeName},</if>
            <if test="typeCode != null and typeCode != ''">type_code = #{typeCode},</if>
            <if test="parentId != null">parent_id = #{parentId},</if>
            <if test="sortOrder != null">sort_order = #{sortOrder},</if>
            <if test="isSystem != null">is_system = #{isSystem},</if>
            <if test="status != null">status = #{status},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where type_id = #{typeId}
    </update>

    <delete id="deleteKgExpenseTypeById" parameterType="Long">
        delete from kg_expense_type where type_id = #{typeId}
    </delete>

    <delete id="deleteKgExpenseTypeByIds" parameterType="String">
        delete from kg_expense_type where type_id in 
        <foreach item="typeId" collection="array" open="(" separator="," close=")">
            #{typeId}
        </foreach>
    </delete>
    
</mapper>
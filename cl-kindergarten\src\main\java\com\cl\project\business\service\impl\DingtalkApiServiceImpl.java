package com.cl.project.business.service.impl;

import com.cl.project.business.domain.dto.DingtalkUserCreateResult;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.cl.common.utils.StringUtils;
import com.cl.common.utils.http.HttpUtils;
import com.cl.project.business.domain.KgTeacher;
import com.cl.project.business.domain.dto.DingtalkTokenResponse;
import com.cl.project.business.domain.dto.DingtalkUserDetailResponse;
import com.cl.project.business.domain.dto.DingtalkUserListResponse;
import com.cl.project.business.domain.dto.DingtalkDepartmentResponse;
import com.cl.project.business.domain.dto.DingtalkDepartmentRequest;
import com.cl.project.business.domain.dto.DingtalkAttendanceResponse;
import com.cl.project.business.domain.KgStudent;
import com.cl.project.business.domain.KgClass;
import com.cl.project.business.domain.KgDingtalkAttendance;
import com.cl.project.business.service.IKgStudentService;
import com.cl.project.business.service.IKgDingtalkAttendanceService;
import com.cl.project.business.service.IDingtalkApiService;
import com.cl.project.business.service.IKgTeacherService;
import com.cl.project.business.util.DingtalkConfigUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;

/**
 * 钉钉API服务实现类
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
@Service
public class DingtalkApiServiceImpl implements IDingtalkApiService 
{
    private static final Logger logger = LoggerFactory.getLogger(DingtalkApiServiceImpl.class);
    
    private static final String GET_TOKEN_URL = "https://oapi.dingtalk.com/gettoken";
    private static final String GET_USER_LIST_URL = "https://oapi.dingtalk.com/topapi/user/listid";
    private static final String GET_USER_DETAIL_URL = "https://oapi.dingtalk.com/topapi/v2/user/get";
    private static final String GET_DEPARTMENT_LIST_URL = "https://oapi.dingtalk.com/topapi/v2/department/listsub";
    private static final String CREATE_DEPARTMENT_URL = "https://oapi.dingtalk.com/topapi/v2/department/create";
    private static final String UPDATE_DEPARTMENT_URL = "https://oapi.dingtalk.com/topapi/v2/department/update";
    private static final String DELETE_DEPARTMENT_URL = "https://oapi.dingtalk.com/topapi/v2/department/delete";
    private static final String DELETE_USER_URL = "https://oapi.dingtalk.com/topapi/v2/user/delete";
    private static final String UPDATE_USER_URL = "https://oapi.dingtalk.com/topapi/v2/user/update";
    private static final String CREATE_USER_URL = "https://oapi.dingtalk.com/topapi/v2/user/create";
    private static final String ATTENDANCE_LIST_URL = "https://oapi.dingtalk.com/attendance/list";
    
    @Autowired
    private IKgTeacherService kgTeacherService;
    
    @Autowired
    private IKgStudentService kgStudentService;
    
    @Autowired
    private com.cl.project.business.service.IKgClassService kgClassService;
    
    @Autowired
    private IKgDingtalkAttendanceService kgDingtalkAttendanceService;
    
    /**
     * 获取钉钉访问令牌
     */
    @Override
    public String getAccessToken() 
    {
        try 
        {
            String appKey = DingtalkConfigUtil.getAppKey();
            String appSecret = DingtalkConfigUtil.getAppSecret();
            
            if (StringUtils.isEmpty(appKey) || StringUtils.isEmpty(appSecret)) 
            {
                logger.error("钉钉AppKey或AppSecret未配置");
                return null;
            }
            
            String url = GET_TOKEN_URL;
            String params = "appkey=" + appKey + "&appsecret=" + appSecret;
            String response = HttpUtils.sendGet(url, params);
            
            if (StringUtils.isNotEmpty(response)) 
            {
                DingtalkTokenResponse tokenResponse = JSON.parseObject(response, DingtalkTokenResponse.class);
                if (tokenResponse.isSuccess()) 
                {
                    return tokenResponse.getAccess_token();
                } 
                else 
                {
                    logger.error("获取钉钉token失败: {}", tokenResponse.getErrmsg());
                }
            }
        } 
        catch (Exception e) 
        {
            logger.error("获取钉钉token异常", e);
        }
        return null;
    }
    
    /**
     * 获取部门用户ID列表
     */
    @Override
    public List<String> getUserIdList(Long deptId) 
    {
        try 
        {
            String accessToken = getAccessToken();
            if (StringUtils.isEmpty(accessToken)) 
            {
                return new ArrayList<>();
            }
            
            String url = GET_USER_LIST_URL + "?access_token=" + accessToken;
            
            // 钉钉API需要form-data格式
            String params = "dept_id=" + deptId;
            
            String response = HttpUtils.sendPost(url, params);
            
            if (StringUtils.isNotEmpty(response)) 
            {
                DingtalkUserListResponse userListResponse = JSON.parseObject(response, DingtalkUserListResponse.class);
                if (userListResponse.isSuccess() && userListResponse.getResult() != null) 
                {
                    return userListResponse.getResult().getUserid_list();
                } 
                else 
                {
                    logger.error("获取钉钉用户列表失败: {}", userListResponse.getErrmsg());
                }
            }
        } 
        catch (Exception e) 
        {
            logger.error("获取钉钉用户列表异常", e);
        }
        return new ArrayList<>();
    }
    
    /**
     * 获取用户详细信息
     */
    @Override
    public DingtalkUserDetailResponse.Result getUserDetail(String userId) 
    {
        try 
        {
            String accessToken = getAccessToken();
            if (StringUtils.isEmpty(accessToken)) 
            {
                return null;
            }
            
            String url = GET_USER_DETAIL_URL + "?access_token=" + accessToken;
            
            // 钉钉API需要form-data格式
            String params = "userid=" + userId;
            
            String response = HttpUtils.sendPost(url, params);
            
            if (StringUtils.isNotEmpty(response)) 
            {
                DingtalkUserDetailResponse userDetailResponse = JSON.parseObject(response, DingtalkUserDetailResponse.class);
                if (userDetailResponse.isSuccess()) 
                {
                    return userDetailResponse.getResult();
                } 
                else 
                {
                    logger.error("获取钉钉用户详情失败: {}", userDetailResponse.getErrmsg());
                }
            }
        } 
        catch (Exception e) 
        {
            logger.error("获取钉钉用户详情异常", e);
        }
        return null;
    }
    
    /**
     * 同步所有用户信息到本地教师表
     */
    @Override
    public int syncAllUsers() 
    {
        int syncCount = 0;
        try 
        {
            // 获取根部门所有用户ID
            List<String> userIds = getUserIdList(1L);
            
            for (String userId : userIds) 
            {
                DingtalkUserDetailResponse.Result userDetail = getUserDetail(userId);
                if (userDetail != null) 
                {
                    // 检查是否已存在该钉钉用户
                    KgTeacher existingTeacher = kgTeacherService.selectKgTeacherByDingtalkUserId(userId);
                    
                    if (existingTeacher == null) 
                    {
                        // 创建新教师记录
                        KgTeacher teacher = new KgTeacher();
                        teacher.setDingtalkUserId(userId);
                        teacher.setTeacherName(userDetail.getName());
                        teacher.setPhone(userDetail.getMobile());
                        teacher.setPosition(userDetail.getTitle());
                        teacher.setAvatar(userDetail.getAvatar());
                        teacher.setEmail(userDetail.getEmail());
                        teacher.setJobNumber(userDetail.getJob_number());
                        
                        // 生成教师编号
                        if (StringUtils.isNotEmpty(userDetail.getJob_number())) 
                        {
                            teacher.setTeacherCode(userDetail.getJob_number());
                        } 
                        else 
                        {
                            teacher.setTeacherCode("DT" + userId);
                        }
                        
                        // 解析入职日期
                        if (StringUtils.isNotEmpty(userDetail.getHired_date())) 
                        {
                            try 
                            {
                                SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
                                teacher.setHireDate(sdf.parse(userDetail.getHired_date()));
                            } 
                            catch (ParseException e) 
                            {
                                logger.warn("解析入职日期失败: {}", userDetail.getHired_date());
                            }
                        }
                        
                        // 设置状态
                        teacher.setStatus(userDetail.getActive() ? "0" : "1");
                        teacher.setCreateBy("system");
                        teacher.setCreateTime(new Date());
                        
                        kgTeacherService.insertKgTeacher(teacher);
                        syncCount++;
                        logger.info("同步钉钉用户成功: {} - {}", userId, userDetail.getName());
                    } 
                    else 
                    {
                        // 更新现有教师信息
                        existingTeacher.setTeacherName(userDetail.getName());
                        existingTeacher.setPhone(userDetail.getMobile());
                        existingTeacher.setPosition(userDetail.getTitle());
                        existingTeacher.setStatus(userDetail.getActive() ? "0" : "1");
                        existingTeacher.setUpdateBy("system");
                        existingTeacher.setUpdateTime(new Date());
                        
                        kgTeacherService.updateKgTeacher(existingTeacher);
                        logger.info("更新钉钉用户成功: {} - {}", userId, userDetail.getName());
                    }
                }
            }
        } 
        catch (Exception e) 
        {
            logger.error("同步钉钉用户异常", e);
        }
        return syncCount;
    }
    
    /**
     * 获取指定部门的所有父部门列表
     */
    @Override
    public List<DingtalkDepartmentResponse.Department> getDepartmentList(Long deptId) 
    {
        try 
        {
            String accessToken = getAccessToken();
            if (StringUtils.isEmpty(accessToken)) 
            {
                return new ArrayList<>();
            }
            
            String url = GET_DEPARTMENT_LIST_URL + "?access_token=" + accessToken;
            String params = "dept_id=" + deptId;
            
            String response = HttpUtils.sendPost(url, params);
            
            if (StringUtils.isNotEmpty(response)) 
            {
                JSONObject jsonResponse = JSON.parseObject(response);
                if (jsonResponse.getInteger("errcode") == 0) 
                {
                    // 直接解析返回的部门列表
                    return JSON.parseArray(jsonResponse.getString("result"), DingtalkDepartmentResponse.Department.class);
                } 
                else 
                {
                    logger.error("获取钉钉部门列表失败: {}", jsonResponse.getString("errmsg"));
                }
            }
        } 
        catch (Exception e) 
        {
            logger.error("获取钉钉部门列表异常", e);
        }
        return new ArrayList<>();
    }
    
    /**
     * 同步指定部门下的所有用户到学生表
     */
    @Override
    public int syncStudentsFromDepartment(Long deptId) 
    {
        int syncCount = 0;
        try 
        {
            // 获取部门下的所有用户ID
            List<String> userIds = getUserIdList(deptId);
            
            for (String userId : userIds) 
            {
                DingtalkUserDetailResponse.Result userDetail = getUserDetail(userId);
                if (userDetail != null) 
                {
                    // 检查是否已存在该钉钉用户
                    KgStudent existingStudent = kgStudentService.selectKgStudentByDingtalkUserId(userId);
                    
                    if (existingStudent == null) 
                    {
                        // 创建新学生记录
                        KgStudent student = new KgStudent();
                        student.setDingtalkUserId(userId);
                        student.setStudentName(userDetail.getName());
                        student.setPhone(userDetail.getMobile());
                        student.setParentPhone(userDetail.getMobile()); // 同时设置家长手机号
                        student.setAvatar(userDetail.getAvatar());
                        student.setEmail(userDetail.getEmail());
                        student.setStudentNumber(userDetail.getJob_number());
                        
                        // 生成学生编号
                        if (StringUtils.isNotEmpty(userDetail.getJob_number())) 
                        {
                            student.setStudentCode(userDetail.getJob_number());
                        } 
                        else 
                        {
                            student.setStudentCode("ST" + userId);
                        }
                        
                        // 设置学生状态
                        student.setStatus(userDetail.getActive() ? "0" : "1"); // 0在园 1退园
                        student.setCreateBy("system");
                        student.setCreateTime(new Date());
                        
                        kgStudentService.insertKgStudent(student);
                        syncCount++;
                        logger.info("同步钉钉学生成功: {} - {}", userId, userDetail.getName());
                    } 
                    else 
                    {
                        // 更新现有学生信息
                        existingStudent.setStudentName(userDetail.getName());
                        existingStudent.setPhone(userDetail.getMobile());
                        existingStudent.setParentPhone(userDetail.getMobile()); // 同时更新家长手机号
                        existingStudent.setAvatar(userDetail.getAvatar());
                        existingStudent.setEmail(userDetail.getEmail());
                        existingStudent.setStudentNumber(userDetail.getJob_number());
                        existingStudent.setStatus(userDetail.getActive() ? "0" : "1");
                        existingStudent.setUpdateBy("system");
                        existingStudent.setUpdateTime(new Date());
                        
                        kgStudentService.updateKgStudent(existingStudent);
                        logger.info("更新钉钉学生成功: {} - {}", userId, userDetail.getName());
                    }
                }
            }
        } 
        catch (Exception e) 
        {
            logger.error("同步部门学生异常", e);
        }
        return syncCount;
    }
    
    /**
     * 同步所有部门的学生信息
     */
    @Override
    public int syncAllStudents() 
    {
        int totalSyncCount = 0;
        try 
        {
            // 获取所有部门列表（从根部门开始）
            List<DingtalkDepartmentResponse.Department> departments = getDepartmentList(1L);
            
            // 注意：不同步根部门(dept_id=1)，因为根部门是老师部门，学生都在子部门中
            
            // 只同步子部门的学生
            for (DingtalkDepartmentResponse.Department dept : departments) 
            {
                if (dept.getDept_id() != null && !dept.getDept_id().equals(1L)) 
                {
                    int count = syncStudentsFromDepartment(dept.getDept_id());
                    totalSyncCount += count;
                    logger.info("部门[{}]同步学生数量: {}", dept.getName(), count);
                }
            }
            
            logger.info("总计同步学生数量: {}", totalSyncCount);
        } 
        catch (Exception e) 
        {
            logger.error("同步所有学生异常", e);
        }
        return totalSyncCount;
    }
    
    // ========================= 部门管理相关实现 =========================
    
    /**
     * 创建钉钉部门
     */
    @Override
    public Long createDepartment(DingtalkDepartmentRequest.CreateRequest request) 
    {
        try 
        {
            String accessToken = getAccessToken();
            if (StringUtils.isEmpty(accessToken)) 
            {
                logger.error("获取访问令牌失败");
                return null;
            }
            
            // 准备请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("access_token", accessToken);
            
            // 准备请求体
            String requestBody = JSON.toJSONString(request);
            logger.info("创建部门请求: {}", requestBody);
            
            // 发送HTTP请求
            String urlWithParams = CREATE_DEPARTMENT_URL + "?access_token=" + accessToken;
            String response = HttpUtils.sendPostJson(urlWithParams, requestBody);
            logger.info("创建部门响应: {}", response);
            
            if (StringUtils.isEmpty(response)) 
            {
                logger.error("创建部门响应为空");
                return null;
            }
            
            // 解析响应
            DingtalkDepartmentRequest.CreateResponse createResponse = JSON.parseObject(response, DingtalkDepartmentRequest.CreateResponse.class);
            if (createResponse == null) 
            {
                logger.error("解析创建部门响应失败");
                return null;
            }
            
            if (createResponse.getErrcode() != 0) 
            {
                logger.error("创建部门失败: errcode={}, errmsg={}", createResponse.getErrcode(), createResponse.getErrmsg());
                return null;
            }
            
            if (createResponse.getResult() != null) 
            {
                Long deptId = createResponse.getResult().getDeptId();
                logger.info("创建部门成功: deptId={}", deptId);
                return deptId;
            }
            
            logger.error("创建部门响应结果为空");
            return null;
        } 
        catch (Exception e) 
        {
            logger.error("创建部门异常", e);
            return null;
        }
    }
    
    /**
     * 更新钉钉部门
     */
    @Override
    public boolean updateDepartment(DingtalkDepartmentRequest.UpdateRequest request) 
    {
        try 
        {
            String accessToken = getAccessToken();
            if (StringUtils.isEmpty(accessToken)) 
            {
                logger.error("获取访问令牌失败");
                return false;
            }
            
            // 准备请求体，根据钉钉官方文档，所有参数都在请求体中
            Map<String, Object> requestBodyMap = new HashMap<>();
            requestBodyMap.put("dept_id", request.getDeptId());
            requestBodyMap.put("name", request.getName());
            requestBodyMap.put("parent_id", request.getParentId());
            requestBodyMap.put("order", request.getOrder());
            if (request.getAutoAddUser() != null) {
                requestBodyMap.put("auto_add_user", request.getAutoAddUser());
            }
            
            String requestBody = JSON.toJSONString(requestBodyMap);
            logger.info("更新部门请求: {}", requestBody);
            
            // 发送HTTP请求，使用sendPostJson以正确设置Content-Type
            String urlWithParams = UPDATE_DEPARTMENT_URL + "?access_token=" + accessToken;
            String response = HttpUtils.sendPostJson(urlWithParams, requestBody);
            logger.info("更新部门响应: {}", response);
            
            if (StringUtils.isEmpty(response)) 
            {
                logger.error("更新部门响应为空");
                return false;
            }
            
            // 解析响应
            DingtalkDepartmentRequest.CommonResponse commonResponse = JSON.parseObject(response, DingtalkDepartmentRequest.CommonResponse.class);
            if (commonResponse == null) 
            {
                logger.error("解析更新部门响应失败");
                return false;
            }
            
            if (commonResponse.getErrcode() != 0) 
            {
                logger.error("更新部门失败: errcode={}, errmsg={}", commonResponse.getErrcode(), commonResponse.getErrmsg());
                return false;
            }
            
            logger.info("更新部门成功: deptId={}", request.getDeptId());
            return true;
        } 
        catch (Exception e) 
        {
            logger.error("更新部门异常", e);
            return false;
        }
    }
    
    /**
     * 删除钉钉部门
     */
    @Override
    public boolean deleteDepartment(Long deptId) 
    {
        try 
        {
            String accessToken = getAccessToken();
            if (StringUtils.isEmpty(accessToken)) 
            {
                logger.error("获取访问令牌失败");
                return false;
            }
            
            // 准备请求参数
            Map<String, Object> params = new HashMap<>();
            params.put("access_token", accessToken);
            
            // 准备请求体
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("dept_id", deptId);
            String requestBodyStr = JSON.toJSONString(requestBody);
            logger.info("删除部门请求: {}", requestBodyStr);
            
            // 发送HTTP请求
            String urlWithParams = DELETE_DEPARTMENT_URL + "?access_token=" + accessToken;
            String response = HttpUtils.sendPostJson(urlWithParams, requestBodyStr);
            logger.info("删除部门响应: {}", response);
            
            if (StringUtils.isEmpty(response)) 
            {
                logger.error("删除部门响应为空");
                return false;
            }
            
            // 解析响应
            DingtalkDepartmentRequest.CommonResponse commonResponse = JSON.parseObject(response, DingtalkDepartmentRequest.CommonResponse.class);
            if (commonResponse == null) 
            {
                logger.error("解析删除部门响应失败");
                return false;
            }
            
            if (commonResponse.getErrcode() != 0) 
            {
                logger.error("删除部门失败: errcode={}, errmsg={}", commonResponse.getErrcode(), commonResponse.getErrmsg());
                return false;
            }
            
            logger.info("删除部门成功: deptId={}", deptId);
            return true;
        } 
        catch (Exception e) 
        {
            logger.error("删除部门异常", e);
            return false;
        }
    }
    
    /**
     * 同步钉钉部门列表到本地班级
     */
    @Override
    public int syncDepartmentsToClasses(Long parentDeptId) 
    {
        int syncCount = 0;
        try 
        {
            Long deptId = parentDeptId != null ? parentDeptId : 1L; // 默认从根部门开始
            List<DingtalkDepartmentResponse.Department> departments = getDepartmentList(deptId);
            
            if (departments == null || departments.isEmpty()) 
            {
                logger.info("没有找到部门数据");
                return 0;
            }
            
            for (DingtalkDepartmentResponse.Department dept : departments) 
            {
                try 
                {
                    // 跳过根部门（dept_id = 1），只同步子部门
                    if (dept.getDept_id().equals(1L)) 
                    {
                        logger.info("跳过根部门: {}", dept.getName());
                        continue;
                    }
                    
                    // 检查部门是否已存在对应的班级
                    KgClass existingClass = kgClassService.selectKgClassByDingtalkDeptId(dept.getDept_id());
                    
                    if (existingClass == null) 
                    {
                        // 创建新班级
                        KgClass newClass = new KgClass();
                        newClass.setClassName(dept.getName());
                        newClass.setDingtalkDeptId(dept.getDept_id());
                        newClass.setClassType("普通班"); // 默认类型
                        newClass.setCapacity(30L); // 默认容量
                        newClass.setCurrentCount(0L);
                        newClass.setStatus("0"); // 正常状态
                        newClass.setComId(dept.getFrom_union_org()); // 使用组织ID
                        
                        int result = kgClassService.insertKgClass(newClass);
                        if (result > 0) 
                        {
                            syncCount++;
                            logger.info("同步新增班级: {} -> {}", dept.getName(), dept.getDept_id());
                        }
                    } 
                    else 
                    {
                        // 更新已存在的班级
                        existingClass.setClassName(dept.getName());
                        int result = kgClassService.updateKgClass(existingClass);
                        if (result > 0) 
                        {
                            syncCount++;
                            logger.info("同步更新班级: {} -> {}", dept.getName(), dept.getDept_id());
                        }
                    }
                } 
                catch (Exception e) 
                {
                    logger.error("同步部门失败: {}", dept.getName(), e);
                }
            }
            
            logger.info("部门同步完成，共同步 {} 个班级", syncCount);
        } 
        catch (Exception e) 
        {
            logger.error("同步部门列表异常", e);
        }
        return syncCount;
    }
    
    /**
     * 删除钉钉用户
     */
    @Override
    public boolean deleteUser(String userid) 
    {
        try 
        {
            String accessToken = getAccessToken();
            if (StringUtils.isEmpty(accessToken)) 
            {
                logger.error("获取访问令牌失败");
                return false;
            }
            
            // 准备请求体
            Map<String, Object> requestBodyMap = new HashMap<>();
            requestBodyMap.put("userid", userid);
            
            String requestBody = JSON.toJSONString(requestBodyMap);
            logger.info("删除用户请求: {}", requestBody);
            
            // 发送HTTP请求
            String urlWithParams = DELETE_USER_URL + "?access_token=" + accessToken;
            String response = HttpUtils.sendPostJson(urlWithParams, requestBody);
            logger.info("删除用户响应: {}", response);
            
            if (StringUtils.isEmpty(response)) 
            {
                logger.error("删除用户响应为空");
                return false;
            }
            
            // 解析响应
            DingtalkDepartmentRequest.CommonResponse commonResponse = JSON.parseObject(response, DingtalkDepartmentRequest.CommonResponse.class);
            if (commonResponse == null) 
            {
                logger.error("解析删除用户响应失败");
                return false;
            }
            
            if (commonResponse.getErrcode() != 0) 
            {
                logger.error("删除用户失败: errcode={}, errmsg={}", commonResponse.getErrcode(), commonResponse.getErrmsg());
                return false;
            }
            
            logger.info("删除用户成功: userid={}", userid);
            return true;
        } 
        catch (Exception e) 
        {
            logger.error("删除用户异常", e);
            return false;
        }
    }
    
    /**
     * 更新钉钉用户信息
     */
    @Override
    public boolean updateUser(String userid, String name, String mobile, String email, String title) 
    {
        try 
        {
            String accessToken = getAccessToken();
            if (StringUtils.isEmpty(accessToken)) 
            {
                logger.error("获取访问令牌失败");
                return false;
            }
            
            // 准备请求体
            Map<String, Object> requestBodyMap = new HashMap<>();
            requestBodyMap.put("userid", userid);
            if (StringUtils.isNotEmpty(name)) {
                requestBodyMap.put("name", name);
            }
            if (StringUtils.isNotEmpty(mobile)) {
                requestBodyMap.put("mobile", mobile);
            }
            if (StringUtils.isNotEmpty(email)) {
                requestBodyMap.put("email", email);
            }
            if (StringUtils.isNotEmpty(title)) {
                requestBodyMap.put("title", title);
            }
            
            String requestBody = JSON.toJSONString(requestBodyMap);
            logger.info("更新用户请求: {}", requestBody);
            
            // 发送HTTP请求
            String urlWithParams = UPDATE_USER_URL + "?access_token=" + accessToken;
            String response = HttpUtils.sendPostJson(urlWithParams, requestBody);
            logger.info("更新用户响应: {}", response);
            
            if (StringUtils.isEmpty(response)) 
            {
                logger.error("更新用户响应为空");
                return false;
            }
            
            // 解析响应
            DingtalkDepartmentRequest.CommonResponse commonResponse = JSON.parseObject(response, DingtalkDepartmentRequest.CommonResponse.class);
            if (commonResponse == null) 
            {
                logger.error("解析更新用户响应失败");
                return false;
            }
            
            if (commonResponse.getErrcode() != 0) 
            {
                logger.error("更新用户失败: errcode={}, errmsg={}", commonResponse.getErrcode(), commonResponse.getErrmsg());
                return false;
            }
            
            logger.info("更新用户成功: userid={}", userid);
            return true;
        } 
        catch (Exception e) 
        {
            logger.error("更新用户异常", e);
            return false;
        }
    }
    
    /**
     * 创建钉钉用户
     */
    @Override
    public DingtalkUserCreateResult createUser(String userid, String name, String mobile, String email, String title, Long deptId) 
    {
        try 
        {
            String accessToken = getAccessToken();
            if (StringUtils.isEmpty(accessToken)) 
            {
                logger.error("获取访问令牌失败");
                return null;
            }
            
            // 准备请求体
            Map<String, Object> requestBodyMap = new HashMap<>();
            requestBodyMap.put("userid", userid);
            requestBodyMap.put("name", name);
            
            if (StringUtils.isNotEmpty(mobile)) {
                requestBodyMap.put("mobile", mobile);
            }
            if (StringUtils.isNotEmpty(email)) {
                requestBodyMap.put("email", email);
            }
            if (StringUtils.isNotEmpty(title)) {
                requestBodyMap.put("title", title);
            }
            
            // 设置部门ID列表
            if (deptId != null) {
                requestBodyMap.put("dept_id_list", deptId.toString());
            } else {
                // 默认分配到根部门
                requestBodyMap.put("dept_id_list", "1");
            }
            
            String requestBody = JSON.toJSONString(requestBodyMap);
            logger.info("创建用户请求: {}", requestBody);
            
            // 发送HTTP请求
            String urlWithParams = CREATE_USER_URL + "?access_token=" + accessToken;
            String response = HttpUtils.sendPostJson(urlWithParams, requestBody);
            logger.info("创建用户响应: {}", response);
            
            if (StringUtils.isEmpty(response)) 
            {
                logger.error("创建用户响应为空");
                return new DingtalkUserCreateResult(false, "创建用户响应为空", -1);
            }
            
            // 解析响应
            DingtalkDepartmentRequest.CommonResponse commonResponse = JSON.parseObject(response, DingtalkDepartmentRequest.CommonResponse.class);
            if (commonResponse == null) 
            {
                logger.error("解析创建用户响应失败");
                return new DingtalkUserCreateResult(false, "解析创建用户响应失败", -2);
            }
            
            if (commonResponse.getErrcode() != 0) 
            {
                logger.error("创建用户失败: errcode={}, errmsg={}", commonResponse.getErrcode(), commonResponse.getErrmsg());
                return new DingtalkUserCreateResult(false, commonResponse.getErrmsg(), commonResponse.getErrcode());
            }
            
            logger.info("创建用户成功: userid={}, name={}", userid, name);
            return new DingtalkUserCreateResult(true, null, 0);
        } 
        catch (Exception e) 
        {
            logger.error("创建用户异常", e);
            return new DingtalkUserCreateResult(false, e.getMessage(), -99);
        }
    }
    
    // ========================= 打卡记录相关方法 =========================
    
    /**
     * 获取钉钉打卡记录
     */
    @Override
    public List<DingtalkAttendanceResponse.AttendanceRecord> getAttendanceRecords(
            List<String> userIdList, String workDateFrom, String workDateTo, Long offset, Long limit) 
    {
        try 
        {
            String accessToken = getAccessToken();
            if (StringUtils.isEmpty(accessToken)) 
            {
                logger.error("获取访问令牌失败");
                return new ArrayList<>();
            }
            
            // 准备请求参数
            Map<String, Object> params = new HashMap<>();
            
            // 确保日期格式为 yyyy-MM-dd HH:mm:ss
            String formattedFrom = workDateFrom;
            String formattedTo = workDateTo;
            if (workDateFrom.length() == 10) {
                formattedFrom = workDateFrom + " 00:00:00";
            }
            if (workDateTo.length() == 10) {
                formattedTo = workDateTo + " 23:59:59";
            }
            
            params.put("workDateFrom", formattedFrom);
            params.put("workDateTo", formattedTo);
            // 关键修复：userIdList需要是JSON字符串格式，不是List对象
            params.put("userIdList", JSON.toJSONString(userIdList));
            params.put("offset", offset != null ? offset : 0L);
            params.put("limit", limit != null ? Math.min(limit, 50L) : 50L);
            // 注意：根据示例，去掉isI18n参数
            
            String requestBody = JSON.toJSONString(params);
            logger.info("获取打卡记录请求参数: {}", requestBody);
            
            // 发送POST请求
            String response = HttpUtils.sendPostJson(ATTENDANCE_LIST_URL + "?access_token=" + accessToken, requestBody);
            if (StringUtils.isEmpty(response)) 
            {
                logger.error("获取打卡记录响应为空");
                return new ArrayList<>();
            }
            
            logger.info("获取打卡记录响应: {}", response);
            
            // 解析响应
            DingtalkAttendanceResponse attendanceResponse = JSON.parseObject(response, DingtalkAttendanceResponse.class);
            if (attendanceResponse == null) 
            {
                logger.error("解析打卡记录响应失败");
                return new ArrayList<>();
            }
            
            if (attendanceResponse.getErrcode() != 0) 
            {
                logger.error("获取打卡记录失败: errcode={}, errmsg={}", 
                    attendanceResponse.getErrcode(), attendanceResponse.getErrmsg());
                return new ArrayList<>();
            }
            
            // 优先使用直接的recordresult字段（新格式）
            List<DingtalkAttendanceResponse.AttendanceRecord> records = attendanceResponse.getRecordresult();
            
            // 如果直接字段为空，尝试从result中获取（兼容旧格式）
            if (records == null && attendanceResponse.getResult() != null) 
            {
                records = attendanceResponse.getResult().getRecordresult();
            }
            
            if (records == null) 
            {
                logger.warn("打卡记录结果为空");
                return new ArrayList<>();
            }
            
            logger.info("成功获取打卡记录 {} 条", records.size());
            return records;
        } 
        catch (Exception e) 
        {
            logger.error("获取打卡记录异常", e);
            return new ArrayList<>();
        }
    }
    
    /**
     * 同步钉钉打卡记录到本地数据库
     */
    @Override
    public int syncAttendanceRecords(String workDateFrom, String workDateTo) 
    {
        try 
        {
            // 获取所有教师和学生的钉钉用户ID
            List<String> allUserIds = new ArrayList<>();
            
            // 获取教师用户ID
            List<KgTeacher> teachers = kgTeacherService.selectKgTeacherList(new KgTeacher());
            for (KgTeacher teacher : teachers) 
            {
                if (StringUtils.isNotEmpty(teacher.getDingtalkUserId())) 
                {
                    allUserIds.add(teacher.getDingtalkUserId());
                }
            }
            
            // 获取学生用户ID  
            List<KgStudent> students = kgStudentService.selectKgStudentList(new KgStudent());
            for (KgStudent student : students) 
            {
                if (StringUtils.isNotEmpty(student.getDingtalkUserId())) 
                {
                    allUserIds.add(student.getDingtalkUserId());
                }
            }
            
            if (allUserIds.isEmpty()) 
            {
                logger.warn("没有找到有钉钉用户ID的用户，跳过同步");
                return 0;
            }
            
            logger.info("开始同步打卡记录，用户数量: {}, 日期范围: {} - {}", allUserIds.size(), workDateFrom, workDateTo);
            
            return syncAttendanceRecordsByUsers(allUserIds, workDateFrom, workDateTo);
        } 
        catch (Exception e) 
        {
            logger.error("同步打卡记录异常", e);
            return 0;
        }
    }
    
    /**
     * 同步指定用户的打卡记录
     */
    @Override
    public int syncAttendanceRecordsByUsers(List<String> userIdList, String workDateFrom, String workDateTo) 
    {
        if (userIdList == null || userIdList.isEmpty()) 
        {
            logger.warn("用户ID列表为空，跳过同步");
            return 0;
        }
        
        int syncCount = 0;
        int batchSize = 50; // 钉钉API限制每次最多50个用户
        
        try 
        {
            // 分批处理用户列表
            for (int i = 0; i < userIdList.size(); i += batchSize) 
            {
                int endIndex = Math.min(i + batchSize, userIdList.size());
                List<String> batchUserIds = userIdList.subList(i, endIndex);
                
                logger.info("正在同步第 {}-{} 个用户的打卡记录", i + 1, endIndex);
                
                // 分页获取打卡记录
                Long offset = 0L;
                Long limit = 50L;
                boolean hasMore = true;
                
                while (hasMore) 
                {
                    List<DingtalkAttendanceResponse.AttendanceRecord> records = 
                        getAttendanceRecords(batchUserIds, workDateFrom, workDateTo, offset, limit);
                    
                    if (records.isEmpty()) 
                    {
                        break;
                    }
                    
                    // 保存记录到数据库
                    for (DingtalkAttendanceResponse.AttendanceRecord record : records) 
                    {
                        try 
                        {
                            KgDingtalkAttendance attendance = convertToEntity(record);
                            if (attendance != null) 
                            {
                                // 检查记录是否已存在（基于用户ID和打卡时间）
                                KgDingtalkAttendance existing = new KgDingtalkAttendance();
                                existing.setUserId(attendance.getUserId());
                                existing.setCheckTime(attendance.getCheckTime());
                                
                                List<KgDingtalkAttendance> existingRecords = 
                                    kgDingtalkAttendanceService.selectKgDingtalkAttendanceList(existing);
                                
                                if (existingRecords.isEmpty()) 
                                {
                                    int result = kgDingtalkAttendanceService.insertKgDingtalkAttendance(attendance);
                                    if (result > 0) 
                                    {
                                        syncCount++;
                                    }
                                }
                                else 
                                {
                                    logger.debug("打卡记录已存在，跳过: userId={}, checkTime={}", 
                                        attendance.getUserId(), attendance.getCheckTime());
                                }
                            }
                        } 
                        catch (Exception e) 
                        {
                            logger.error("保存打卡记录失败: userId={}", record.getUserId(), e);
                        }
                    }
                    
                    // 检查是否有更多数据
                    if (records.size() < limit) 
                    {
                        hasMore = false;
                    } 
                    else 
                    {
                        offset += limit;
                    }
                }
                
                // 避免频繁调用API
                if (i + batchSize < userIdList.size()) 
                {
                    Thread.sleep(1000); // 暂停1秒
                }
            }
            
            logger.info("打卡记录同步完成，共同步 {} 条记录", syncCount);
        } 
        catch (Exception e) 
        {
            logger.error("同步指定用户打卡记录异常", e);
        }
        
        return syncCount;
    }
    
    /**
     * 将钉钉打卡记录转换为实体对象
     */
    private KgDingtalkAttendance convertToEntity(DingtalkAttendanceResponse.AttendanceRecord record) 
    {
        if (record == null) return null;
        
        try 
        {
            KgDingtalkAttendance attendance = new KgDingtalkAttendance();
            attendance.setUserId(record.getUserId());
            
            // 转换打卡时间（钉钉返回的是时间戳）
            if (record.getUserCheckTime() != null) 
            {
                attendance.setCheckTime(new Date(record.getUserCheckTime()));
            }
            
            // 设置打卡类型
            attendance.setCheckType(record.getCheckType());
            
            // 设置定位结果
            attendance.setLocationResult(record.getLocationResult());
            attendance.setLocationDetail(record.getUserAddress());
            
            // 转换计划打卡时间
            if (record.getPlanCheckTime() != null) 
            {
                attendance.setPlanCheckTime(new Date(record.getPlanCheckTime()));
            }
            
            // 关联用户信息
            linkUserInfo(attendance, record.getUserId());
            
            // 设置默认值
            attendance.setIsProcessed(0L); // 未处理
            attendance.setCreateTime(new Date());
            attendance.setRemark("钉钉同步");
            
            return attendance;
        } 
        catch (Exception e) 
        {
            logger.error("转换打卡记录失败: userId={}", record.getUserId(), e);
            return null;
        }
    }
    
    /**
     * 关联用户信息（教师或学生）
     */
    private void linkUserInfo(KgDingtalkAttendance attendance, String dingtalkUserId) {
        if (StringUtils.isEmpty(dingtalkUserId)) {
            return;
        }
        
        try {
            // 首先尝试按钉钉用户ID查询教师
            KgTeacher teacher = kgTeacherService.selectKgTeacherByDingtalkUserId(dingtalkUserId);
            if (teacher != null) {
                attendance.setEmployeeId(teacher.getTeacherId());
                return;
            }
            
            // 如果没有找到教师，尝试按钉钉用户ID查询学生
            KgStudent student = kgStudentService.selectKgStudentByDingtalkUserId(dingtalkUserId);
            if (student != null) {
                attendance.setStudentId(student.getStudentId());
                // 根据学生打卡时间判断费用类型
                determineFeeType(attendance);
                return;
            }
            
            // 如果没有找到匹配的钉钉用户ID，回退到旧的格式处理（兼容旧数据）
            // 处理 teacher_X 格式的userId
            if (dingtalkUserId.startsWith("teacher_")) {
                String teacherIdStr = dingtalkUserId.substring("teacher_".length());
                try {
                    Long teacherId = Long.parseLong(teacherIdStr);
                    teacher = kgTeacherService.selectKgTeacherById(teacherId);
                    if (teacher != null) {
                        attendance.setEmployeeId(teacher.getTeacherId());
                        return;
                    }
                } catch (NumberFormatException e) {
                    logger.warn("Invalid teacher ID format: {}", dingtalkUserId);
                }
            }
            // 处理 student_X 格式的userId
            else if (dingtalkUserId.startsWith("student_")) {
                String studentIdStr = dingtalkUserId.substring("student_".length());
                try {
                    Long studentId = Long.parseLong(studentIdStr);
                    student = kgStudentService.selectKgStudentById(studentId);
                    if (student != null) {
                        attendance.setStudentId(student.getStudentId());
                        // 根据学生打卡时间判断费用类型
                        determineFeeType(attendance);
                        return;
                    }
                } catch (NumberFormatException e) {
                    logger.warn("Invalid student ID format: {}", dingtalkUserId);
                }
            }
        } catch (Exception e) {
            logger.error("关联用户信息失败: dingtalkUserId={}", dingtalkUserId, e);
        }
    }
    
    /**
     * 根据学生打卡时间判断费用类型
     */
    private void determineFeeType(KgDingtalkAttendance attendance) 
    {
        if (attendance.getCheckTime() == null) return;
        
        Calendar cal = Calendar.getInstance();
        cal.setTime(attendance.getCheckTime());
        int hour = cal.get(Calendar.HOUR_OF_DAY);
        
        // 根据打卡时间判断费用类型
        if (hour >= 7 && hour <= 12) 
        {
            // 上午打卡，园费
            attendance.setFeeType("tuition");
        } 
        else if (hour >= 17 && hour <= 19) 
        {
            // 下午/晚上打卡，托管费
            attendance.setFeeType("course");
        }
    }
}

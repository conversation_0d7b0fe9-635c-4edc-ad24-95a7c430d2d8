import request from '@/utils/request'

// 计算教师月度工资
export function calculateMonthlySalary(teacherId, year, month) {
  return request({
    url: '/business/salary-calculation/calculate-monthly',
    method: 'post',
    params: { teacherId, year, month }
  })
}

// 批量计算全体教师工资
export function calculateAllSalaryBatch(year, month) {
  return request({
    url: '/business/salary-calculation/calculate-all-batch',
    method: 'post',
    params: { year, month }
  })
}

// 预览工资计算结果
export function previewSalaryCalculation(teacherId, year, month) {
  return request({
    url: '/business/salary-calculation/preview',
    method: 'get',
    params: { teacherId, year, month }
  })
}

// 获取工资计算规则
export function getSalaryCalculationRules(teacherId) {
  return request({
    url: '/business/salary-calculation/rules',
    method: 'get',
    params: { teacherId }
  })
}

// 生成工资单
export function generatePayslips(calculations) {
  return request({
    url: '/business/salary-calculation/generate-payslips',
    method: 'post',
    data: calculations
  })
}

// 重新计算工资
export function recalculateSalary(salaryId, reason) {
  return request({
    url: '/business/salary-calculation/recalculate',
    method: 'post',
    params: { salaryId, reason }
  })
}

// 工资调整
export function adjustSalary(salaryId, adjustType, adjustAmount, reason) {
  return request({
    url: '/business/salary-calculation/adjust',
    method: 'post',
    params: { salaryId, adjustType, adjustAmount, reason }
  })
}

// 获取工资统计
export function getSalaryStatistics(year, month) {
  return request({
    url: '/business/salary-calculation/statistics',
    method: 'get',
    params: { year, month }
  })
}

// 获取教师工资明细
export function getSalaryDetails(teacherId, year, month) {
  return request({
    url: '/business/salary-calculation/details',
    method: 'get',
    params: { teacherId, year, month }
  })
}

// 工资发放确认
export function confirmSalaryPayment(salaryIds) {
  return request({
    url: '/business/salary-calculation/confirm-payment',
    method: 'post',
    params: { salaryIds }
  })
}

// 生成工资报表
export function generateSalaryReport(year, month) {
  return request({
    url: '/business/salary-calculation/generate-report',
    method: 'post',
    params: { year, month }
  })
}

// 计算课时费统计
export function getCourseFeeStatistics(teacherId, year, month) {
  return request({
    url: '/business/salary-calculation/course-fee-statistics',
    method: 'get',
    params: { teacherId, year, month }
  })
}

# 幼儿园管理系统数据库文件说明

## 文件列表

### 1. 数据库表结构文件

#### 1.1 `kindergarten_database_complete.sql`
- **内容**: 基础信息管理表、考勤管理表
- **包含表**:
  - `kg_student` - 幼儿信息表
  - `kg_class` - 班级信息表
  - `kg_teacher` - 教师信息表
  - `kg_student_attendance` - 学生考勤记录表
  - `kg_teacher_attendance` - 教师考勤记录表

#### 1.2 `kindergarten_database_part2.sql`
- **内容**: 托管服务管理表、费用管理表
- **包含表**:
  - `kg_course` - 托管课程表
  - `kg_course_enrollment` - 托管报名记录表
  - `kg_course_attendance` - 托管考勤记录表
  - `kg_tuition_config` - 园费配置表
  - `kg_tuition_bill` - 园费账单表
  - `kg_course_bill` - 托管费账单表
  - `kg_course_bill_detail` - 托管费账单明细表

#### 1.3 `kindergarten_database_part3.sql`
- **内容**: 工资管理表、财务管理表、库存管理表
- **包含表**:
  - `kg_salary_config` - 工资配置表
  - `kg_teacher_salary` - 教师工资表
  - `kg_income` - 收入记录表
  - `kg_expense_type` - 支出类型表
  - `kg_expense` - 支出记录表
  - `kg_item_category` - 物品类别表
  - `kg_item` - 物品信息表
  - `kg_stock_record` - 库存变动记录表

#### 1.4 `kindergarten_database_part4.sql`
- **内容**: 钉钉集成表、微信小程序表、系统配置表、权限管理表
- **包含表**:
  - `kg_dingtalk_attendance` - 钉钉打卡记录表
  - `kg_time_config` - 时间段配置表
  - `kg_wechat_user` - 微信用户表
  - `kg_user_bind_request` - 用户绑定申请记录表
  - `kg_phone_verification` - 手机号验证码表
  - `kg_message_push` - 消息推送记录表
  - `kg_wechat_login_log` - 微信登录日志表
  - `kg_mobile_permission_config` - 移动端权限配置表

### 2. 初始数据文件

#### 2.1 `kindergarten_data_complete.sql`
- **内容**: 角色权限、菜单权限数据
- **包含数据**:
  - 幼儿园专用角色（园长、教务主任、班主任等）
  - 移动端菜单权限（考勤管理、托管管理、费用管理等）
  - 功能权限按钮（签到、签退、确认、查询等）
  - 角色菜单权限关联

#### 2.2 `kindergarten_config_data.sql`
- **内容**: 基础配置数据
- **包含数据**:
  - 园费配置（不同班级类型的收费标准）
  - 托管课程（英语、美术、全脑、军警等）
  - 工资配置（满勤奖、出勤率奖励、课时费等）
  - 支出类型（人员支出、运营支出、其他支出等）
  - 物品类别（教学用品、生活用品、办公用品等）
  - 时间段配置（园费时间段、托管时间段）
  - 移动端权限配置（不同角色的权限限制）

### 3. 设计说明文档

#### 3.1 `幼儿园管理系统数据库设计说明.md`
- **内容**: 完整的数据库设计说明文档
- **包含内容**:
  - 系统概述
  - 数据库表结构概览
  - 核心表关联关系图
  - 业务流程说明
  - 权限控制说明
  - 技术特性
  - 数据安全
  - 部署建议

## 使用说明

### 1. 数据库初始化步骤

1. **创建数据库表结构**（按顺序执行）:
   ```sql
   -- 执行表结构文件
   source kindergarten_database_complete.sql;
   source kindergarten_database_part2.sql;
   source kindergarten_database_part3.sql;
   source kindergarten_database_part4.sql;
   ```

2. **插入初始数据**:
   ```sql
   -- 执行权限数据
   source kindergarten_data_complete.sql;
   
   -- 执行配置数据
   source kindergarten_config_data.sql;
   ```

3. **扩展现有用户表**（如果需要微信绑定功能）:
   ```sql
   -- 为系统用户表添加微信相关字段
   ALTER TABLE `sys_user` 
   ADD COLUMN `wechat_openid` varchar(100) COMMENT '绑定的微信openid',
   ADD COLUMN `is_wechat_bound` tinyint DEFAULT 0 COMMENT '是否已绑定微信（0否 1是）',
   ADD COLUMN `wechat_bind_time` datetime COMMENT '微信绑定时间';
   
   -- 添加索引
   ALTER TABLE `sys_user` 
   ADD KEY `idx_wechat_openid` (`wechat_openid`);
   ```

### 2. 重要注意事项

1. **多租户支持**: 所有表都包含`com_id`字段，确保数据隔离
2. **权限控制**: 基于现有Sa-Token框架，使用`@SaCheckPermission`注解
3. **数据权限**: 使用`@DataScope`注解控制数据访问范围
4. **索引优化**: 为高频查询字段创建了合适的索引
5. **扩展性**: 支出类型、物品类别等支持自定义扩展

### 3. 核心功能模块

1. **基础信息管理**: 学生、教师、班级信息管理
2. **考勤管理**: 学生和教师考勤记录，支持人脸识别
3. **托管服务**: 课程管理、报名、考勤统计
4. **费用管理**: 园费和托管费的自动计算和账单生成
5. **工资管理**: 教师工资的多维度计算
6. **财务管理**: 收入支出的详细记录和统计
7. **库存管理**: 物品的入库出库管理
8. **钉钉集成**: 打卡数据同步和处理
9. **微信集成**: 小程序用户管理和消息推送
10. **权限控制**: 移动端细粒度权限管理

### 4. 业务特色功能

1. **预交费机制**: 园费和托管费都支持预交和余额管理
2. **智能费用计算**: 
   - 园费根据出勤率自动计算保教费（超过50%收全额）
   - 托管费根据实际出勤次数计算
3. **工资自动计算**: 
   - 基本工资按出勤率计算
   - 满勤奖、课时费、报名奖励等多维度计算
4. **微信消息推送**: 账单、考勤通知等自动推送给家长
5. **权限精细控制**: 不同角色有不同的时间、位置、审批限制

### 5. 技术架构特点

1. **基于现有框架**: 完全兼容现有多租户框架
2. **移动端优先**: 专为移动端设计的权限控制
3. **集成能力强**: 支持钉钉、微信等第三方平台集成
4. **扩展性好**: 支持自定义配置和灵活扩展
5. **安全性高**: 多层次的权限控制和数据保护

## 联系方式

如有问题或需要技术支持，请联系开发团队。

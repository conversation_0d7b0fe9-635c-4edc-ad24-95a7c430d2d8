import request from '@/utils/request'

// 查询托管报名记录列表
export function listEnrollment(query) {
  return request({
    url: '/business/enrollment/list',
    method: 'get',
    params: query
  })
}

// 查询托管报名记录详细
export function getEnrollment(enrollmentId) {
  return request({
    url: '/business/enrollment/' + enrollmentId,
    method: 'get'
  })
}

// 新增托管报名记录
export function addEnrollment(data) {
  return request({
    url: '/business/enrollment',
    method: 'post',
    data: data
  })
}

// 修改托管报名记录
export function updateEnrollment(data) {
  return request({
    url: '/business/enrollment',
    method: 'put',
    data: data
  })
}

// 删除托管报名记录
export function delEnrollment(enrollmentId) {
  return request({
    url: '/business/enrollment/' + enrollmentId,
    method: 'delete'
  })
}

// 导出托管报名记录
export function exportEnrollment(query) {
  return request({
    url: '/business/enrollment/export',
    method: 'get',
    params: query
  })
}

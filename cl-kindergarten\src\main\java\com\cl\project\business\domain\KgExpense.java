package com.cl.project.business.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 支出记录对象 kg_expense
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgExpense extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 支出ID */
    private Long expenseId;

    /** 支出类型ID，关联kg_expense_type.type_id */
    @Excel(name = "支出类型ID，关联kg_expense_type.type_id")
    private Long expenseTypeId;

    /** 支出日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支出日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date expenseDate;

    /** 支出金额 */
    @Excel(name = "支出金额")
    private BigDecimal amount;

    /** 收款方 */
    @Excel(name = "收款方")
    private String payee;

    /** 支付方式（wechat微信、alipay支付宝、cash现金、bank银行转账） */
    @Excel(name = "支付方式", readConverterExp = "w=echat微信、alipay支付宝、cash现金、bank银行转账")
    private String paymentMethod;

    /** 发票号码 */
    @Excel(name = "发票号码")
    private String invoiceNo;

    /** 支出描述 */
    @Excel(name = "支出描述")
    private String description;

    /** 审批人ID，关联kg_teacher.teacher_id */
    @Excel(name = "审批人ID，关联kg_teacher.teacher_id")
    private Long approverId;

    /** 审批状态（pending待审批、approved已审批、rejected已拒绝） */
    @Excel(name = "审批状态", readConverterExp = "p=ending待审批、approved已审批、rejected已拒绝")
    private String approvalStatus;

    /** 审批时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "审批时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date approvalTime;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    public void setExpenseId(Long expenseId) 
    {
        this.expenseId = expenseId;
    }

    public Long getExpenseId() 
    {
        return expenseId;
    }
    public void setExpenseTypeId(Long expenseTypeId) 
    {
        this.expenseTypeId = expenseTypeId;
    }

    public Long getExpenseTypeId() 
    {
        return expenseTypeId;
    }
    public void setExpenseDate(Date expenseDate) 
    {
        this.expenseDate = expenseDate;
    }

    public Date getExpenseDate() 
    {
        return expenseDate;
    }
    public void setAmount(BigDecimal amount) 
    {
        this.amount = amount;
    }

    public BigDecimal getAmount() 
    {
        return amount;
    }
    public void setPayee(String payee) 
    {
        this.payee = payee;
    }

    public String getPayee() 
    {
        return payee;
    }
    public void setPaymentMethod(String paymentMethod) 
    {
        this.paymentMethod = paymentMethod;
    }

    public String getPaymentMethod() 
    {
        return paymentMethod;
    }
    public void setInvoiceNo(String invoiceNo) 
    {
        this.invoiceNo = invoiceNo;
    }

    public String getInvoiceNo() 
    {
        return invoiceNo;
    }
    public void setDescription(String description) 
    {
        this.description = description;
    }

    public String getDescription() 
    {
        return description;
    }
    public void setApproverId(Long approverId) 
    {
        this.approverId = approverId;
    }

    public Long getApproverId() 
    {
        return approverId;
    }
    public void setApprovalStatus(String approvalStatus) 
    {
        this.approvalStatus = approvalStatus;
    }

    public String getApprovalStatus() 
    {
        return approvalStatus;
    }
    public void setApprovalTime(Date approvalTime) 
    {
        this.approvalTime = approvalTime;
    }

    public Date getApprovalTime() 
    {
        return approvalTime;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("expenseId", getExpenseId())
            .append("expenseTypeId", getExpenseTypeId())
            .append("expenseDate", getExpenseDate())
            .append("amount", getAmount())
            .append("payee", getPayee())
            .append("paymentMethod", getPaymentMethod())
            .append("invoiceNo", getInvoiceNo())
            .append("description", getDescription())
            .append("approverId", getApproverId())
            .append("approvalStatus", getApprovalStatus())
            .append("approvalTime", getApprovalTime())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

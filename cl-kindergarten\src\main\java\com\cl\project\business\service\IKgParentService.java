package com.cl.project.business.service;

import java.util.List;
import java.util.Map;
import com.cl.project.business.domain.KgParent;
import com.cl.project.business.domain.KgStudent;
import com.cl.project.business.domain.KgMessagePush;

/**
 * 家长信息Service接口
 * 
 * <AUTHOR>
 * @date 2024-07-29
 */
public interface IKgParentService 
{
    /**
     * 查询家长信息
     * 
     * @param parentId 家长信息主键
     * @return 家长信息
     */
    public KgParent selectKgParentByParentId(Long parentId);

    /**
     * 查询家长信息列表
     * 
     * @param kgParent 家长信息
     * @return 家长信息集合
     */
    public List<KgParent> selectKgParentList(KgParent kgParent);

    /**
     * 新增家长信息
     * 
     * @param kgParent 家长信息
     * @return 结果
     */
    public int insertKgParent(KgParent kgParent);

    /**
     * 修改家长信息
     * 
     * @param kgParent 家长信息
     * @return 结果
     */
    public int updateKgParent(KgParent kgParent);

    /**
     * 批量删除家长信息
     * 
     * @param parentIds 需要删除的家长信息主键集合
     * @return 结果
     */
    public int deleteKgParentByParentIds(Long[] parentIds);

    /**
     * 删除家长信息信息
     * 
     * @param parentId 家长信息主键
     * @return 结果
     */
    public int deleteKgParentByParentId(Long parentId);

    /**
     * 根据手机号查询家长信息
     * 
     * @param parentPhone 家长手机号
     * @return 家长信息
     */
    public KgParent selectKgParentByPhone(String parentPhone);

    /**
     * 根据微信openid查询家长信息
     * 
     * @param wechatOpenid 微信openid
     * @return 家长信息
     */
    public KgParent selectKgParentByWechatOpenid(String wechatOpenid);

    /**
     * 批量绑定微信
     * 
     * @param parentIds 家长ID数组
     * @param wechatOpenid 微信openid
     * @return 结果
     */
    public int batchBindWechat(Long[] parentIds, String wechatOpenid);

    /**
     * 批量解绑微信
     * 
     * @param parentIds 家长ID数组
     * @return 结果
     */
    public int batchUnbindWechat(Long[] parentIds);

    /**
     * 校验家长手机号是否唯一
     * 
     * @param kgParent 家长信息
     * @return 结果
     */
    public String checkParentPhoneUnique(KgParent kgParent);

    /**
     * 导入家长数据
     * 
     * @param parentList 家长数据列表
     * @param isUpdateSupport 是否更新支持，如果已存在，则进行更新数据
     * @param operName 操作用户
     * @return 结果
     */
    public String importParent(List<KgParent> parentList, Boolean isUpdateSupport, String operName);

    /**
     * 获取家长的子女信息
     * 
     * @param parentId 家长主键
     * @return 子女信息集合
     */
    public List<KgStudent> getParentChildren(Long parentId);

    /**
     * 获取家长的费用统计
     * 
     * @param parentId 家长主键
     * @return 费用统计信息
     */
    public Map<String, Object> getParentFeeStats(Long parentId);

    /**
     * 获取家长的消息记录
     * 
     * @param parentId 家长主键
     * @return 消息记录集合
     */
    public List<KgMessagePush> getParentMessages(Long parentId);
}

package com.cl.project.business.service;

import java.util.List;
import java.util.Map;

/**
 * 工资计算Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IKgSalaryCalculationService 
{
    /**
     * 计算教师月度工资
     * 
     * @param teacherId 教师ID
     * @param year 年份
     * @param month 月份
     * @return 计算结果
     */
    Map<String, Object> calculateMonthlySalary(Long teacherId, Integer year, Integer month);

    /**
     * 批量计算全体教师工资
     * 
     * @param year 年份
     * @param month 月份
     * @return 计算汇总结果
     */
    Map<String, Object> calculateAllSalaryBatch(Integer year, Integer month);

    /**
     * 预览工资计算结果
     * 
     * @param teacherId 教师ID
     * @param year 年份
     * @param month 月份
     * @return 预览结果
     */
    Map<String, Object> previewSalaryCalculation(Long teacherId, Integer year, Integer month);

    /**
     * 获取工资计算规则
     * 
     * @param teacherId 教师ID
     * @return 计算规则
     */
    Map<String, Object> getSalaryCalculationRules(Long teacherId);

    /**
     * 生成工资单
     * 
     * @param calculations 计算结果列表
     * @return 生成的工资单数量
     */
    int generatePayslips(List<Map<String, Object>> calculations);

    /**
     * 重新计算工资
     * 
     * @param salaryId 工资记录ID
     * @param reason 重新计算原因
     * @return 重新计算结果
     */
    Map<String, Object> recalculateSalary(Long salaryId, String reason);

    /**
     * 工资调整
     * 
     * @param salaryId 工资记录ID
     * @param adjustType 调整类型
     * @param adjustAmount 调整金额
     * @param reason 调整原因
     * @return 调整结果
     */
    int adjustSalary(Long salaryId, String adjustType, Double adjustAmount, String reason);

    /**
     * 获取工资统计
     * 
     * @param year 年份
     * @param month 月份
     * @return 工资统计
     */
    Map<String, Object> getSalaryStatistics(Integer year, Integer month);

    /**
     * 获取教师工资明细
     * 
     * @param teacherId 教师ID
     * @param year 年份
     * @param month 月份
     * @return 工资明细
     */
    Map<String, Object> getSalaryDetails(Long teacherId, Integer year, Integer month);

    /**
     * 工资发放确认
     * 
     * @param salaryIds 工资记录ID列表
     * @return 确认数量
     */
    int confirmSalaryPayment(List<Long> salaryIds);

    /**
     * 生成工资报表
     * 
     * @param year 年份
     * @param month 月份
     * @return 报表文件路径
     */
    String generateSalaryReport(Integer year, Integer month);

    /**
     * 计算课时费统计
     * 
     * @param teacherId 教师ID
     * @param year 年份
     * @param month 月份
     * @return 课时费统计
     */
    Map<String, Object> getCourseFeeStatistics(Long teacherId, Integer year, Integer month);

    /**
     * 计算基本工资
     * 
     * @param teacherId 教师ID
     * @param attendanceDays 出勤天数
     * @param workDays 应出勤天数
     * @return 基本工资
     */
    Double calculateBaseSalary(Long teacherId, Integer attendanceDays, Integer workDays);

    /**
     * 计算满勤奖
     * 
     * @param teacherId 教师ID
     * @param attendanceRate 出勤率
     * @return 满勤奖金额
     */
    Double calculateAttendanceBonus(Long teacherId, Double attendanceRate);

    /**
     * 计算课时费
     * 
     * @param teacherId 教师ID
     * @param year 年份
     * @param month 月份
     * @return 课时费金额
     */
    Double calculateCourseFee(Long teacherId, Integer year, Integer month);

    /**
     * 计算报名奖励
     * 
     * @param teacherId 教师ID
     * @param year 年份
     * @param month 月份
     * @return 报名奖励金额
     */
    Double calculateEnrollmentBonus(Long teacherId, Integer year, Integer month);

    /**
     * 计算出勤率奖励
     * 
     * @param teacherId 教师ID
     * @param year 年份
     * @param month 月份
     * @return 出勤率奖励金额
     */
    Double calculateAttendanceRateBonus(Long teacherId, Integer year, Integer month);

    /**
     * 计算新生奖励
     * 
     * @param teacherId 教师ID
     * @param year 年份
     * @param month 月份
     * @return 新生奖励金额
     */
    Double calculateNewStudentBonus(Long teacherId, Integer year, Integer month);

    /**
     * 计算退园扣款
     * 
     * @param teacherId 教师ID
     * @param year 年份
     * @param month 月份
     * @return 退园扣款金额
     */
    Double calculateWithdrawalDeduction(Long teacherId, Integer year, Integer month);

    /**
     * 计算社保代扣
     * 
     * @param teacherId 教师ID
     * @param grossSalary 税前工资
     * @return 社保代扣金额
     */
    Double calculateSocialInsuranceDeduction(Long teacherId, Double grossSalary);
}

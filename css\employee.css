/* 员工端样式 */

/* 头部信息栏 */
.header {
    background: linear-gradient(135deg, #FF9800 0%, #F57C00 100%);
    color: white;
    padding: 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.user-info {
    display: flex;
    align-items: center;
    gap: 15px;
}

.avatar {
    font-size: 40px;
    width: 60px;
    height: 60px;
    background: rgba(255,255,255,0.2);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.info h2 {
    font-size: 18px;
    margin-bottom: 5px;
}

.info p {
    font-size: 14px;
    opacity: 0.9;
}

.date-info {
    text-align: right;
}

.date-info p {
    margin-bottom: 5px;
    font-size: 14px;
}

.status-checked {
    color: #4CAF50;
    font-weight: 600;
}

/* 概览区域 */
.overview-section {
    padding: 20px;
    background: #f8f9fa;
}

.section-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 15px;
    color: #333;
}

.overview-cards {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.overview-card {
    background: white;
    border-radius: 12px;
    padding: 20px;
    display: flex;
    align-items: center;
    gap: 15px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.card-icon {
    font-size: 24px;
}

.card-info h4 {
    font-size: 14px;
    color: #666;
    margin-bottom: 5px;
}

.number {
    font-size: 20px;
    font-weight: 600;
    color: #333;
}

/* 快捷操作 */
.quick-actions {
    padding: 20px;
}

.action-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 15px;
}

.action-item {
    background: white;
    border-radius: 12px;
    padding: 20px;
    text-align: center;
    text-decoration: none;
    color: #333;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
}

.action-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 4px 15px rgba(0,0,0,0.15);
}

.action-icon {
    font-size: 32px;
    margin-bottom: 10px;
}

.action-item span {
    font-size: 14px;
    font-weight: 500;
}

/* 待办事项 */
.todo-section {
    padding: 20px;
    background: #f8f9fa;
}

.todo-list {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.todo-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    gap: 15px;
}

.todo-item:last-child {
    border-bottom: none;
}

.todo-icon {
    font-size: 20px;
    width: 40px;
    height: 40px;
    background: #f8f9fa;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.todo-content {
    flex: 1;
}

.todo-content h4 {
    font-size: 14px;
    margin-bottom: 5px;
    color: #333;
}

.todo-content p {
    font-size: 12px;
    color: #666;
    margin-bottom: 5px;
}

.todo-time {
    font-size: 11px;
    color: #999;
}

.todo-action {
    background: #4CAF50;
    color: white;
    border: none;
    border-radius: 6px;
    padding: 6px 12px;
    font-size: 12px;
    cursor: pointer;
}

/* 学生考勤 */
.student-attendance {
    padding: 20px;
}

.student-list {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.student-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    gap: 15px;
}

.student-item:last-child {
    border-bottom: none;
}

.student-avatar {
    font-size: 20px;
    width: 40px;
    height: 40px;
    background: #e3f2fd;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.student-info {
    flex: 1;
}

.student-info h4 {
    font-size: 14px;
    margin-bottom: 5px;
    color: #333;
}

.student-info p {
    font-size: 12px;
    color: #666;
}

.attendance-status {
    font-size: 18px;
}

.attendance-status.present {
    color: #4CAF50;
}

.attendance-status.absent {
    color: #f44336;
}

.attendance-status.sick {
    color: #ff9800;
}

/* 托管课程 */
.course-reminder {
    padding: 20px;
    background: #f8f9fa;
}

.course-list {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

.course-item {
    display: flex;
    align-items: center;
    padding: 15px 20px;
    border-bottom: 1px solid #f0f0f0;
    gap: 15px;
}

.course-item:last-child {
    border-bottom: none;
}

.course-time {
    background: #e3f2fd;
    color: #1976d2;
    padding: 8px 12px;
    border-radius: 8px;
    font-size: 12px;
    font-weight: 600;
    white-space: nowrap;
}

.course-info {
    flex: 1;
}

.course-info h4 {
    font-size: 14px;
    margin-bottom: 5px;
    color: #333;
}

.course-info p {
    font-size: 12px;
    color: #666;
}

/* 底部导航调整 */
.bottom-nav {
    padding-bottom: env(safe-area-inset-bottom);
}

/* 响应式调整 */
@media (max-width: 375px) {
    .header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .overview-cards {
        grid-template-columns: 1fr;
    }
    
    .action-grid {
        grid-template-columns: 1fr;
    }
}

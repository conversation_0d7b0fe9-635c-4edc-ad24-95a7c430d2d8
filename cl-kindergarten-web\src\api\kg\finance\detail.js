import request from '@/utils/request'

// 查询托管费账单明细列表
export function listDetail(query) {
  return request({
    url: '/business/detail/list',
    method: 'get',
    params: query
  })
}

// 查询托管费账单明细详细
export function getDetail(detailId) {
  return request({
    url: '/business/detail/' + detailId,
    method: 'get'
  })
}

// 新增托管费账单明细
export function addDetail(data) {
  return request({
    url: '/business/detail',
    method: 'post',
    data: data
  })
}

// 修改托管费账单明细
export function updateDetail(data) {
  return request({
    url: '/business/detail',
    method: 'put',
    data: data
  })
}

// 删除托管费账单明细
export function delDetail(detailId) {
  return request({
    url: '/business/detail/' + detailId,
    method: 'delete'
  })
}

// 导出托管费账单明细
export function exportDetail(query) {
  return request({
    url: '/business/detail/export',
    method: 'get',
    params: query
  })
}

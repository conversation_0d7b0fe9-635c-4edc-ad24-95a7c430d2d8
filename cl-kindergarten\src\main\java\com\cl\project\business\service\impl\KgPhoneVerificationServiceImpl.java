package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgPhoneVerificationMapper;
import com.cl.project.business.domain.KgPhoneVerification;
import com.cl.project.business.service.IKgPhoneVerificationService;

/**
 * 手机号验证码Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgPhoneVerificationServiceImpl implements IKgPhoneVerificationService 
{
    @Autowired
    private KgPhoneVerificationMapper kgPhoneVerificationMapper;

    /**
     * 查询手机号验证码
     * 
     * @param verificationId 手机号验证码ID
     * @return 手机号验证码
     */
    @Override
    public KgPhoneVerification selectKgPhoneVerificationById(Long verificationId)
    {
        return kgPhoneVerificationMapper.selectKgPhoneVerificationById(verificationId);
    }

    /**
     * 查询手机号验证码列表
     * 
     * @param kgPhoneVerification 手机号验证码
     * @return 手机号验证码
     */
    @Override
    public List<KgPhoneVerification> selectKgPhoneVerificationList(KgPhoneVerification kgPhoneVerification)
    {
        return kgPhoneVerificationMapper.selectKgPhoneVerificationList(kgPhoneVerification);
    }

    /**
     * 新增手机号验证码
     * 
     * @param kgPhoneVerification 手机号验证码
     * @return 结果
     */
    @Override
    public int insertKgPhoneVerification(KgPhoneVerification kgPhoneVerification)
    {
        kgPhoneVerification.setCreateTime(DateUtils.getNowDate());
        return kgPhoneVerificationMapper.insertKgPhoneVerification(kgPhoneVerification);
    }

    /**
     * 修改手机号验证码
     * 
     * @param kgPhoneVerification 手机号验证码
     * @return 结果
     */
    @Override
    public int updateKgPhoneVerification(KgPhoneVerification kgPhoneVerification)
    {
        return kgPhoneVerificationMapper.updateKgPhoneVerification(kgPhoneVerification);
    }

    /**
     * 批量删除手机号验证码
     * 
     * @param verificationIds 需要删除的手机号验证码ID
     * @return 结果
     */
    @Override
    public int deleteKgPhoneVerificationByIds(Long[] verificationIds)
    {
        return kgPhoneVerificationMapper.deleteKgPhoneVerificationByIds(verificationIds);
    }

    /**
     * 删除手机号验证码信息
     * 
     * @param verificationId 手机号验证码ID
     * @return 结果
     */
    @Override
    public int deleteKgPhoneVerificationById(Long verificationId)
    {
        return kgPhoneVerificationMapper.deleteKgPhoneVerificationById(verificationId);
    }
}

package com.cl.project.business.service.impl;

import java.math.BigDecimal;
import java.util.List;
import com.cl.common.utils.DateUtils;
import com.cl.common.utils.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import com.cl.project.business.mapper.KgItemMapper;
import com.cl.project.business.domain.KgItem;
import com.cl.project.business.domain.KgStockRecord;
import com.cl.project.business.service.IKgItemService;
import com.cl.project.business.service.IKgStockRecordService;

/**
 * 物品信息Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgItemServiceImpl implements IKgItemService
{
    @Autowired
    private KgItemMapper kgItemMapper;

    @Autowired
    private IKgStockRecordService kgStockRecordService;

    /**
     * 查询物品信息
     * 
     * @param itemId 物品信息ID
     * @return 物品信息
     */
    @Override
    public KgItem selectKgItemById(Long itemId)
    {
        return kgItemMapper.selectKgItemById(itemId);
    }

    /**
     * 查询物品信息列表
     * 
     * @param kgItem 物品信息
     * @return 物品信息
     */
    @Override
    public List<KgItem> selectKgItemList(KgItem kgItem)
    {
        return kgItemMapper.selectKgItemList(kgItem);
    }

    /**
     * 新增物品信息
     *
     * @param kgItem 物品信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int insertKgItem(KgItem kgItem)
    {
        kgItem.setCreateTime(DateUtils.getNowDate());
        int result = kgItemMapper.insertKgItem(kgItem);

        // 如果设置了初始库存，插入库存变动记录
        if (result > 0 && kgItem.getCurrentStock() != null && kgItem.getCurrentStock() > 0) {
            insertStockRecord(kgItem.getItemId(), "in", 0L, kgItem.getCurrentStock(),
                            kgItem.getUnitPrice(), "初始库存", null, null, "系统初始化");
        }

        return result;
    }

    /**
     * 修改物品信息
     *
     * @param kgItem 物品信息
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateKgItem(KgItem kgItem)
    {
        // 获取修改前的物品信息
        KgItem oldItem = kgItemMapper.selectKgItemById(kgItem.getItemId());
        if (oldItem == null) {
            return 0;
        }

        kgItem.setUpdateTime(DateUtils.getNowDate());
        int result = kgItemMapper.updateKgItem(kgItem);

        // 如果库存发生变化，插入库存变动记录
        if (result > 0 && kgItem.getCurrentStock() != null &&
            !kgItem.getCurrentStock().equals(oldItem.getCurrentStock())) {

            Long beforeStock = oldItem.getCurrentStock() != null ? oldItem.getCurrentStock() : 0L;
            Long afterStock = kgItem.getCurrentStock();
            Long changeQuantity = afterStock - beforeStock;

            String recordType = changeQuantity > 0 ? "in" : "out";
            String purpose = changeQuantity > 0 ? "库存调增" : "库存调减";

            insertStockRecord(kgItem.getItemId(), recordType, beforeStock, afterStock,
                            kgItem.getUnitPrice(), purpose, null, null, "库存调整");
        }

        return result;
    }

    /**
     * 批量删除物品信息
     * 
     * @param itemIds 需要删除的物品信息ID
     * @return 结果
     */
    @Override
    public int deleteKgItemByIds(Long[] itemIds)
    {
        return kgItemMapper.deleteKgItemByIds(itemIds);
    }

    /**
     * 删除物品信息信息
     *
     * @param itemId 物品信息ID
     * @return 结果
     */
    @Override
    public int deleteKgItemById(Long itemId)
    {
        return kgItemMapper.deleteKgItemById(itemId);
    }

    /**
     * 插入库存变动记录
     *
     * @param itemId 物品ID
     * @param recordType 变动类型（in入库、out出库、adjust调整）
     * @param beforeStock 变动前库存
     * @param afterStock 变动后库存
     * @param unitPrice 单价
     * @param purpose 用途说明
     * @param supplier 供应商
     * @param recipient 领用人
     * @param remark 备注
     */
    private void insertStockRecord(Long itemId, String recordType, Long beforeStock, Long afterStock,
                                 BigDecimal unitPrice, String purpose, String supplier, String recipient, String remark) {
        KgStockRecord stockRecord = new KgStockRecord();
        stockRecord.setItemId(itemId);
        stockRecord.setRecordType(recordType);
        stockRecord.setRecordDate(DateUtils.getNowDate());

        // 计算变动数量
        Long quantity = Math.abs(afterStock - beforeStock);
        stockRecord.setQuantity(quantity);

        stockRecord.setUnitPrice(unitPrice != null ? unitPrice : BigDecimal.ZERO);

        // 计算总金额
        BigDecimal totalAmount = stockRecord.getUnitPrice().multiply(new BigDecimal(quantity));
        stockRecord.setTotalAmount(totalAmount);

        stockRecord.setBeforeStock(beforeStock);
        stockRecord.setAfterStock(afterStock);
        stockRecord.setSupplier(supplier);
        stockRecord.setRecipient(recipient);
        stockRecord.setPurpose(purpose);
        stockRecord.setOperatorId(SecurityUtils.getCurrUserId());
        stockRecord.setComId(SecurityUtils.getCurrComId());
        stockRecord.setRemark(remark);

        kgStockRecordService.insertKgStockRecord(stockRecord);
    }
}

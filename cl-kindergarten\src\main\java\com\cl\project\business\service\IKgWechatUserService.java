package com.cl.project.business.service;

import java.util.List;
import com.cl.project.business.domain.KgWechatUser;

/**
 * 微信用户Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IKgWechatUserService 
{
    /**
     * 查询微信用户
     * 
     * @param wechatUserId 微信用户ID
     * @return 微信用户
     */
    public KgWechatUser selectKgWechatUserById(Long wechatUserId);

    /**
     * 查询微信用户列表
     * 
     * @param kgWechatUser 微信用户
     * @return 微信用户集合
     */
    public List<KgWechatUser> selectKgWechatUserList(KgWechatUser kgWechatUser);

    /**
     * 新增微信用户
     * 
     * @param kgWechatUser 微信用户
     * @return 结果
     */
    public int insertKgWechatUser(KgWechatUser kgWechatUser);

    /**
     * 修改微信用户
     * 
     * @param kgWechatUser 微信用户
     * @return 结果
     */
    public int updateKgWechatUser(KgWechatUser kgWechatUser);

    /**
     * 批量删除微信用户
     * 
     * @param wechatUserIds 需要删除的微信用户ID
     * @return 结果
     */
    public int deleteKgWechatUserByIds(Long[] wechatUserIds);

    /**
     * 删除微信用户信息
     * 
     * @param wechatUserId 微信用户ID
     * @return 结果
     */
    public int deleteKgWechatUserById(Long wechatUserId);
}

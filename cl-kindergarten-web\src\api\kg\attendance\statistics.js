import request from '@/utils/request'

// 获取学生考勤统计
export function getStudentAttendanceStatistics(query) {
  return request({
    url: '/business/attendance-statistics/student',
    method: 'get',
    params: query
  })
}

// 获取教师考勤统计
export function getTeacherAttendanceStatistics(query) {
  return request({
    url: '/business/attendance-statistics/teacher',
    method: 'get',
    params: query
  })
}

// 获取班级考勤统计
export function getClassAttendanceStatistics(query) {
  return request({
    url: '/business/attendance-statistics/class',
    method: 'get',
    params: query
  })
}

// 获取考勤汇总报表
export function getAttendanceSummary(query) {
  return request({
    url: '/business/attendance-statistics/summary',
    method: 'get',
    params: query
  })
}

// 批量确认考勤记录
export function confirmAttendanceBatch(attendanceIds) {
  return request({
    url: '/business/attendance-statistics/confirm-batch',
    method: 'post',
    params: { attendanceIds }
  })
}

// 生成月度考勤报表
export function generateMonthlyReport(year, month) {
  return request({
    url: '/business/attendance-statistics/monthly-report',
    method: 'post',
    params: { year, month }
  })
}

// 考勤异常处理
export function handleAttendanceException(attendanceId, exceptionType, reason) {
  return request({
    url: '/business/attendance-statistics/handle-exception',
    method: 'post',
    params: { attendanceId, exceptionType, reason }
  })
}

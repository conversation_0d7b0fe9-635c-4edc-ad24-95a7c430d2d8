package com.cl.project.business.service;

import java.util.List;
import java.util.Map;

/**
 * 园费计算Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface IKgTuitionCalculationService 
{
    /**
     * 计算学生月度园费
     * 
     * @param studentId 学生ID
     * @param year 年份
     * @param month 月份
     * @return 计算结果
     */
    Map<String, Object> calculateMonthlyTuition(Long studentId, Integer year, Integer month);

    /**
     * 批量计算班级园费
     * 
     * @param classId 班级ID
     * @param year 年份
     * @param month 月份
     * @return 计算结果列表
     */
    List<Map<String, Object>> calculateClassTuitionBatch(Long classId, Integer year, Integer month);

    /**
     * 批量计算全园园费
     * 
     * @param year 年份
     * @param month 月份
     * @return 计算汇总结果
     */
    Map<String, Object> calculateAllTuitionBatch(Integer year, Integer month);

    /**
     * 预览园费计算结果
     * 
     * @param studentId 学生ID
     * @param year 年份
     * @param month 月份
     * @return 预览结果
     */
    Map<String, Object> previewTuitionCalculation(Long studentId, Integer year, Integer month);

    /**
     * 获取园费计算规则
     * 
     * @param classId 班级ID
     * @return 计算规则
     */
    Map<String, Object> getTuitionCalculationRules(Long classId);

    /**
     * 生成园费账单
     * 
     * @param calculations 计算结果列表
     * @return 生成的账单数量
     */
    int generateTuitionBills(List<Map<String, Object>> calculations);

    /**
     * 重新计算园费
     * 
     * @param billId 账单ID
     * @param reason 重新计算原因
     * @return 重新计算结果
     */
    Map<String, Object> recalculateTuition(Long billId, String reason);

    /**
     * 获取费用明细
     * 
     * @param studentId 学生ID
     * @param year 年份
     * @param month 月份
     * @return 费用明细
     */
    Map<String, Object> getFeeDetails(Long studentId, Integer year, Integer month);

    /**
     * 费用调整
     * 
     * @param billId 账单ID
     * @param feeType 费用类型
     * @param adjustAmount 调整金额
     * @param reason 调整原因
     * @return 调整结果
     */
    int adjustFee(Long billId, String feeType, Double adjustAmount, String reason);

    /**
     * 获取费用统计
     * 
     * @param year 年份
     * @param month 月份
     * @param classId 班级ID（可选）
     * @return 费用统计
     */
    Map<String, Object> getTuitionStatistics(Integer year, Integer month, Long classId);

    /**
     * 计算餐费
     * 
     * @param studentId 学生ID
     * @param attendanceDays 出勤天数
     * @param mealPrice 餐费单价
     * @return 餐费金额
     */
    Double calculateMealFee(Long studentId, Integer attendanceDays, Double mealPrice);

    /**
     * 计算保教费
     * 
     * @param studentId 学生ID
     * @param attendanceRate 出勤率
     * @param baseFee 基础保教费
     * @return 保教费金额
     */
    Double calculateEducationFee(Long studentId, Double attendanceRate, Double baseFee);

    /**
     * 计算余额结转
     * 
     * @param studentId 学生ID
     * @param currentMonth 当前月份
     * @return 余额结转金额
     */
    Double calculateBalanceCarryover(Long studentId, String currentMonth);

    /**
     * 计算下月预交金额
     * 
     * @param studentId 学生ID
     * @param nextMonth 下月
     * @return 预交金额
     */
    Double calculateAdvancePayment(Long studentId, String nextMonth);
}

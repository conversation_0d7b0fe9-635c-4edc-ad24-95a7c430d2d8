<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" :inline="true" label-width="68px">
      <el-form-item label="教师姓名" prop="teacherName">
        <el-input
          v-model="queryParams.teacherName"
          placeholder="请输入教师姓名"
          clearable
          size="small"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="工资月份" prop="salaryMonth">
        <el-date-picker clearable size="small" style="width: 200px"
          v-model="queryParams.salaryMonth"
          type="month"
          value-format="yyyy-MM"
          placeholder="选择工资月份">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="发放状态" prop="paymentStatus">
        <el-select v-model="queryParams.paymentStatus" placeholder="请选择发放状态" clearable size="small">
          <el-option label="未发放" value="0" />
          <el-option label="已发放" value="1" />
          <el-option label="部分发放" value="2" />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="warning"
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
        >导出</el-button>
      </el-col>
    </el-row>

    <el-table v-loading="loading" :data="salaryList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="教师姓名" align="center" prop="teacherName" />
      <el-table-column label="工资月份" align="center" prop="salaryMonth" />
      <el-table-column label="基本工资" align="center" prop="baseSalary">
        <template slot-scope="scope">
          <span>¥{{ scope.row.baseSalary }}</span>
        </template>
      </el-table-column>
      <el-table-column label="课时费" align="center" prop="classFee">
        <template slot-scope="scope">
          <span>¥{{ scope.row.classFee }}</span>
        </template>
      </el-table-column>
      <el-table-column label="奖金" align="center" prop="bonus">
        <template slot-scope="scope">
          <span>¥{{ scope.row.bonus }}</span>
        </template>
      </el-table-column>
      <el-table-column label="扣款" align="center" prop="deduction">
        <template slot-scope="scope">
          <span style="color: #F56C6C;">¥{{ scope.row.deduction }}</span>
        </template>
      </el-table-column>
      <el-table-column label="应发工资" align="center" prop="totalSalary">
        <template slot-scope="scope">
          <span style="color: #E6A23C; font-weight: bold;">¥{{ scope.row.totalSalary }}</span>
        </template>
      </el-table-column>
      <el-table-column label="实发工资" align="center" prop="actualSalary">
        <template slot-scope="scope">
          <span style="color: #67C23A; font-weight: bold;">¥{{ scope.row.actualSalary }}</span>
        </template>
      </el-table-column>
      <el-table-column label="工作天数" align="center" prop="workDays" />
      <el-table-column label="发放状态" align="center" prop="paymentStatus">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.kg_payment_status" :value="scope.row.paymentStatus"/>
        </template>
      </el-table-column>
      <el-table-column label="发放时间" align="center" prop="paymentTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.paymentTime, '{y}-{m}-{d}') }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleView(scope.row)"
          >查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>
    
    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 工资详情对话框 -->
    <el-dialog title="工资详情" :visible.sync="detailOpen" width="700px" append-to-body>
      <el-descriptions :column="2" border>
        <el-descriptions-item label="教师姓名">{{ detailData.teacherName }}</el-descriptions-item>
        <el-descriptions-item label="工资月份">{{ detailData.salaryMonth }}</el-descriptions-item>
        <el-descriptions-item label="基本工资">¥{{ detailData.baseSalary }}</el-descriptions-item>
        <el-descriptions-item label="课时费">¥{{ detailData.classFee }}</el-descriptions-item>
        <el-descriptions-item label="奖金">¥{{ detailData.bonus }}</el-descriptions-item>
        <el-descriptions-item label="扣款">¥{{ detailData.deduction }}</el-descriptions-item>
        <el-descriptions-item label="应发工资">
          <span style="color: #E6A23C; font-weight: bold;">¥{{ detailData.totalSalary }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="实发工资">
          <span style="color: #67C23A; font-weight: bold;">¥{{ detailData.actualSalary }}</span>
        </el-descriptions-item>
        <el-descriptions-item label="工作天数">{{ detailData.workDays }}天</el-descriptions-item>
        <el-descriptions-item label="课时数">{{ detailData.classHours }}小时</el-descriptions-item>
        <el-descriptions-item label="发放状态">
          <dict-tag :options="dict.type.kg_payment_status" :value="detailData.paymentStatus"/>
        </el-descriptions-item>
        <el-descriptions-item label="发放时间">{{ parseTime(detailData.paymentTime, '{y}-{m}-{d}') }}</el-descriptions-item>
      </el-descriptions>
      
      <div style="margin-top: 20px;">
        <h4>工资明细</h4>
        <el-table :data="detailData.salaryDetails" border style="width: 100%">
          <el-table-column prop="itemName" label="项目名称" />
          <el-table-column prop="itemType" label="类型">
            <template slot-scope="scope">
              <el-tag :type="scope.row.itemType === '1' ? 'success' : 'danger'">
                {{ scope.row.itemType === '1' ? '收入' : '扣款' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="amount" label="金额">
            <template slot-scope="scope">
              <span :style="{ color: scope.row.itemType === '1' ? '#67C23A' : '#F56C6C' }">
                ¥{{ scope.row.amount }}
              </span>
            </template>
          </el-table-column>
          <el-table-column prop="remark" label="备注" />
        </el-table>
      </div>
      
      <div slot="footer" class="dialog-footer">
        <el-button @click="detailOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listSalary, getSalary, exportSalary } from "@/api/kg/salary/query";

export default {
  name: "SalaryQuery",
  dicts: ['kg_payment_status'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 总条数
      total: 0,
      // 工资表格数据
      salaryList: [],
      // 是否显示详情对话框
      detailOpen: false,
      // 详情数据
      detailData: {},
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        teacherName: undefined,
        salaryMonth: undefined,
        paymentStatus: undefined,
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询工资列表 */
    getList() {
      this.loading = true;
      listSalary(this.queryParams).then(response => {
        this.salaryList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.salaryId)
      this.single = selection.length!=1
      this.multiple = !selection.length
    },
    /** 查看详情 */
    handleView(row) {
      getSalary(row.salaryId).then(response => {
        this.detailData = response.data;
        this.detailOpen = true;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      const queryParams = this.queryParams;
      this.$confirm('是否确认导出所有工资数据项?', "警告", {
          confirmButtonText: "确定",
          cancelButtonText: "取消",
          type: "warning"
        }).then(function() {
          return exportSalary(queryParams);
        }).then(response => {
          this.download(response.msg);
        }).catch(function() {});
    }
  }
};
</script>

// 首页JavaScript功能

class IndexPage {
    constructor() {
        this.selectedRole = null;
        this.init();
    }

    init() {
        this.bindEvents();
        this.checkAutoLogin();
    }

    // 绑定事件
    bindEvents() {
        // 角色卡片点击事件
        const roleCards = document.querySelectorAll('.role-card');
        roleCards.forEach(card => {
            card.addEventListener('click', (e) => {
                this.selectRole(e.currentTarget);
            });
        });

        // 登录按钮点击事件
        const loginBtn = document.getElementById('loginBtn');
        if (loginBtn) {
            loginBtn.addEventListener('click', () => {
                this.handleLogin();
            });
        }

        // 回车键登录
        const passwordInput = document.getElementById('password');
        if (passwordInput) {
            passwordInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.handleLogin();
                }
            });
        }

        // 忘记密码链接
        const forgotPasswordLink = document.querySelector('.login-options .link');
        if (forgotPasswordLink) {
            forgotPasswordLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleForgotPassword();
            });
        }

        // 微信登录链接
        const wechatLoginLink = document.querySelectorAll('.login-options .link')[1];
        if (wechatLoginLink) {
            wechatLoginLink.addEventListener('click', (e) => {
                e.preventDefault();
                this.handleWechatLogin();
            });
        }
    }

    // 选择角色
    selectRole(cardElement) {
        // 移除其他卡片的选中状态
        document.querySelectorAll('.role-card').forEach(card => {
            card.classList.remove('selected');
        });

        // 添加选中状态
        cardElement.classList.add('selected');

        // 获取角色类型
        this.selectedRole = cardElement.dataset.role;

        // 显示登录表单
        this.showLoginForm();
    }

    // 显示登录表单
    showLoginForm() {
        const loginSection = document.getElementById('loginSection');
        const loginTitle = document.getElementById('loginTitle');

        if (loginSection && loginTitle) {
            // 更新标题
            const roleNames = {
                employee: '员工登录',
                admin: '管理员登录'
            };
            loginTitle.textContent = roleNames[this.selectedRole] || '登录';

            // 显示登录区域
            loginSection.style.display = 'block';

            // 滚动到登录区域
            loginSection.scrollIntoView({ behavior: 'smooth' });

            // 聚焦到用户名输入框
            setTimeout(() => {
                const usernameInput = document.getElementById('username');
                if (usernameInput) {
                    usernameInput.focus();
                }
            }, 300);
        }
    }

    // 处理登录
    async handleLogin() {
        if (!this.selectedRole) {
            Toast.warning('请先选择身份');
            return;
        }

        const username = document.getElementById('username').value.trim();
        const password = document.getElementById('password').value.trim();

        // 表单验证
        if (!username) {
            Toast.warning('请输入工号或手机号');
            document.getElementById('username').focus();
            return;
        }

        if (!password) {
            Toast.warning('请输入密码');
            document.getElementById('password').focus();
            return;
        }

        // 显示加载状态
        this.setLoginLoading(true);

        try {
            // 模拟登录请求（实际项目中替换为真实API）
            const response = await this.mockLogin(username, password, this.selectedRole);

            if (response.success) {
                Toast.success('登录成功');
                
                // 保存用户信息
                Utils.setStorage(CONFIG.TOKEN_KEY, response.data.token);
                Utils.setStorage(CONFIG.USER_KEY, response.data.user);
                Utils.setStorage(CONFIG.ROLE_KEY, this.selectedRole);

                // 跳转到对应页面
                setTimeout(() => {
                    this.redirectToRolePage();
                }, 1000);
            } else {
                Toast.error(response.message || '登录失败');
            }
        } catch (error) {
            Toast.error(error.message || '登录失败，请重试');
        } finally {
            this.setLoginLoading(false);
        }
    }

    // 模拟登录API（实际项目中删除此方法）
    async mockLogin(username, password, role) {
        return new Promise((resolve) => {
            setTimeout(() => {
                // 模拟登录验证
                const mockUsers = {
                    employee: {
                        'emp001': { password: '123456', name: '张老师', position: '班主任' },
                        'emp002': { password: '123456', name: '李老师', position: '副班主任' },
                        '13800138001': { password: '123456', name: '王老师', position: '托管教师' }
                    },
                    admin: {
                        'admin': { password: 'admin123', name: '园长', position: '园长' },
                        'finance': { password: '123456', name: '财务主管', position: '财务' },
                        '13900139001': { password: '123456', name: '教务主任', position: '教务主任' }
                    }
                };

                const roleUsers = mockUsers[role];
                const user = roleUsers && roleUsers[username];

                if (user && user.password === password) {
                    resolve({
                        success: true,
                        data: {
                            token: 'mock_token_' + Date.now(),
                            user: {
                                username,
                                name: user.name,
                                position: user.position,
                                role
                            }
                        }
                    });
                } else {
                    resolve({
                        success: false,
                        message: '用户名或密码错误'
                    });
                }
            }, 1000);
        });
    }

    // 设置登录按钮加载状态
    setLoginLoading(loading) {
        const loginBtn = document.getElementById('loginBtn');
        if (loginBtn) {
            if (loading) {
                loginBtn.disabled = true;
                loginBtn.innerHTML = '<span class="loading"></span> 登录中...';
            } else {
                loginBtn.disabled = false;
                loginBtn.innerHTML = '登录';
            }
        }
    }

    // 跳转到角色对应页面
    redirectToRolePage() {
        const rolePages = {
            employee: '/pages/employee/dashboard.html',
            admin: '/pages/admin/dashboard.html'
        };

        const targetPage = rolePages[this.selectedRole];
        if (targetPage) {
            window.location.href = targetPage;
        }
    }

    // 处理忘记密码
    handleForgotPassword() {
        Toast.info('请联系管理员重置密码');
    }

    // 处理微信登录
    handleWechatLogin() {
        Toast.info('微信登录功能开发中...');
    }

    // 检查自动登录
    checkAutoLogin() {
        if (Auth.isLoggedIn()) {
            const role = Auth.getCurrentRole();
            const user = Auth.getCurrentUser();
            
            if (role && user) {
                Toast.info(`欢迎回来，${user.name}`);
                setTimeout(() => {
                    this.selectedRole = role;
                    this.redirectToRolePage();
                }, 1500);
            }
        }
    }
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    new IndexPage();
});

package com.cl.project.business.domain.dto;

import java.util.List;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

/**
 * 学生批量签到DTO
 * 
 * <AUTHOR>
 * @date 2025-08-01
 */
public class BatchStudentCheckinDto 
{
    /** 学生ID列表 */
    @NotEmpty(message = "学生ID列表不能为空")
    private List<Long> studentIds;
    
    /** 考勤日期 */
    @NotNull(message = "考勤日期不能为空")
    private String attendanceDate;
    
    /** 考勤状态 (1出勤 2迟到 3缺勤 4请假 5早退 6病假 8休假) */
    @NotNull(message = "考勤状态不能为空")
    private String attendanceStatus;
    
    /** 备注 */
    private String remark;

    public List<Long> getStudentIds() {
        return studentIds;
    }

    public void setStudentIds(List<Long> studentIds) {
        this.studentIds = studentIds;
    }

    public String getAttendanceDate() {
        return attendanceDate;
    }

    public void setAttendanceDate(String attendanceDate) {
        this.attendanceDate = attendanceDate;
    }

    public String getAttendanceStatus() {
        return attendanceStatus;
    }

    public void setAttendanceStatus(String attendanceStatus) {
        this.attendanceStatus = attendanceStatus;
    }

    public String getRemark() {
        return remark;
    }

    public void setRemark(String remark) {
        this.remark = remark;
    }

    @Override
    public String toString() {
        return "BatchStudentCheckinDto{" +
                "studentIds=" + studentIds +
                ", attendanceDate='" + attendanceDate + '\'' +
                ", attendanceStatus='" + attendanceStatus + '\'' +
                ", remark='" + remark + '\'' +
                '}';
    }
}

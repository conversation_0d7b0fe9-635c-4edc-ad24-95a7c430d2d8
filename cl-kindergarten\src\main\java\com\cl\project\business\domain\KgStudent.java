package com.cl.project.business.domain;

import java.util.Date;

import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 幼儿信息对象 kg_student
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgStudent extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 幼儿ID */
    private Long studentId;

    /** 幼儿编号 */
    @Excel(name = "幼儿编号")
    private String studentCode;

    /** 幼儿姓名 */
    @Excel(name = "幼儿姓名")
    private String studentName;

    /** 性别（0男 1女） */
    @Excel(name = "性别", readConverterExp = "0=男,1=女")
    private String gender;

    /** 出生日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "出生日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date birthDate;

    /** 身份证号 */
    @Excel(name = "身份证号")
    private String idCard;

    /** 联系电话 */
    @Excel(name = "联系电话")
    private String phone;

    /** 家长姓名 */
    @Excel(name = "家长姓名")
    private String parentName;

    /** 家长电话 */
    @Excel(name = "家长电话")
    private String parentPhone;

    /** 紧急联系人 */
    @Excel(name = "紧急联系人")
    private String emergencyContact;

    /** 紧急联系电话 */
    @Excel(name = "紧急联系电话")
    private String emergencyPhone;

    /** 家庭住址 */
    @Excel(name = "家庭住址")
    private String address;

    /** 班级ID，关联kg_class.class_id */
    @Excel(name = "班级ID，关联kg_class.class_id")
    private Long classId;

    /** 入园日期 */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "入园日期", width = 30, dateFormat = "yyyy-MM-dd")
    private Date enrollmentDate;

    /** 状态（0在园 1退园 2请假） */
    @Excel(name = "状态", readConverterExp = "0=在园,1=退园,2=请假")
    private String status;

    /** 人脸识别ID，用于刷脸打卡 */
    @Excel(name = "人脸识别ID，用于刷脸打卡")
    private String faceId;

    /** 微信openid，关联kg_wechat_user.openid */
    @Excel(name = "微信openid，关联kg_wechat_user.openid")
    private String wechatOpenid;

    /** 钉钉用户ID */
    @Excel(name = "钉钉用户ID")
    private String dingtalkUserId;

    /** 头像URL */
    @Excel(name = "头像URL")
    private String avatar;

    /** 邮箱 */
    @Excel(name = "邮箱")
    private String email;

    /** 学号 */
    @Excel(name = "学号")
    private String studentNumber;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;
    
    /** 班级名称（临时字段，用于返回查询结果） */
    private String className;

    public void setStudentId(Long studentId) 
    {
        this.studentId = studentId;
    }

    public Long getStudentId() 
    {
        return studentId;
    }
    public void setStudentCode(String studentCode) 
    {
        this.studentCode = studentCode;
    }

    public String getStudentCode() 
    {
        return studentCode;
    }
    public void setStudentName(String studentName) 
    {
        this.studentName = studentName;
    }

    public String getStudentName() 
    {
        return studentName;
    }
    public void setGender(String gender) 
    {
        this.gender = gender;
    }

    public String getGender() 
    {
        return gender;
    }
    public void setBirthDate(Date birthDate) 
    {
        this.birthDate = birthDate;
    }

    public Date getBirthDate() 
    {
        return birthDate;
    }
    public void setIdCard(String idCard) 
    {
        this.idCard = idCard;
    }

    public String getIdCard() 
    {
        return idCard;
    }
    public void setPhone(String phone) 
    {
        this.phone = phone;
    }

    public String getPhone() 
    {
        return phone;
    }
    public void setParentName(String parentName) 
    {
        this.parentName = parentName;
    }

    public String getParentName() 
    {
        return parentName;
    }
    public void setParentPhone(String parentPhone) 
    {
        this.parentPhone = parentPhone;
    }

    public String getParentPhone() 
    {
        return parentPhone;
    }
    public void setEmergencyContact(String emergencyContact) 
    {
        this.emergencyContact = emergencyContact;
    }

    public String getEmergencyContact() 
    {
        return emergencyContact;
    }
    public void setEmergencyPhone(String emergencyPhone) 
    {
        this.emergencyPhone = emergencyPhone;
    }

    public String getEmergencyPhone() 
    {
        return emergencyPhone;
    }
    public void setAddress(String address) 
    {
        this.address = address;
    }

    public String getAddress() 
    {
        return address;
    }
    public void setClassId(Long classId) 
    {
        this.classId = classId;
    }

    public Long getClassId() 
    {
        return classId;
    }
    public void setEnrollmentDate(Date enrollmentDate) 
    {
        this.enrollmentDate = enrollmentDate;
    }

    public Date getEnrollmentDate() 
    {
        return enrollmentDate;
    }
    
    public void setStatus(String status) 
    {
        this.status = status;
    }

    public String getStatus() 
    {
        return status;
    }
    public void setFaceId(String faceId) 
    {
        this.faceId = faceId;
    }

    public String getFaceId() 
    {
        return faceId;
    }
    public void setWechatOpenid(String wechatOpenid) 
    {
        this.wechatOpenid = wechatOpenid;
    }

    public String getWechatOpenid() 
    {
        return wechatOpenid;
    }
    
    public void setDingtalkUserId(String dingtalkUserId) 
    {
        this.dingtalkUserId = dingtalkUserId;
    }

    public String getDingtalkUserId() 
    {
        return dingtalkUserId;
    }
    
    public void setAvatar(String avatar) 
    {
        this.avatar = avatar;
    }

    public String getAvatar() 
    {
        return avatar;
    }
    
    public void setEmail(String email) 
    {
        this.email = email;
    }

    public String getEmail() 
    {
        return email;
    }
    
    public void setStudentNumber(String studentNumber) 
    {
        this.studentNumber = studentNumber;
    }

    public String getStudentNumber() 
    {
        return studentNumber;
    }
    
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }
    
    public void setClassName(String className) 
    {
        this.className = className;
    }

    public String getClassName() 
    {
        return className;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("studentId", getStudentId())
            .append("studentCode", getStudentCode())
            .append("studentName", getStudentName())
            .append("gender", getGender())
            .append("birthDate", getBirthDate())
            .append("idCard", getIdCard())
            .append("phone", getPhone())
            .append("parentName", getParentName())
            .append("parentPhone", getParentPhone())
            .append("emergencyContact", getEmergencyContact())
            .append("emergencyPhone", getEmergencyPhone())
            .append("address", getAddress())
            .append("classId", getClassId())
            .append("enrollmentDate", getEnrollmentDate())
            .append("status", getStatus())
            .append("faceId", getFaceId())
            .append("wechatOpenid", getWechatOpenid())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

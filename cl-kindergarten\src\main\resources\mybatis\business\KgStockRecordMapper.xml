<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.cl.project.business.mapper.KgStockRecordMapper">
    
    <resultMap type="KgStockRecord" id="KgStockRecordResult">
        <result property="recordId"    column="record_id"    />
        <result property="itemId"    column="item_id"    />
        <result property="recordType"    column="record_type"    />
        <result property="recordDate"    column="record_date"    />
        <result property="quantity"    column="quantity"    />
        <result property="unitPrice"    column="unit_price"    />
        <result property="totalAmount"    column="total_amount"    />
        <result property="beforeStock"    column="before_stock"    />
        <result property="afterStock"    column="after_stock"    />
        <result property="supplier"    column="supplier"    />
        <result property="recipient"    column="recipient"    />
        <result property="purpose"    column="purpose"    />
        <result property="operatorId"    column="operator_id"    />
        <result property="comId"    column="com_id"    />
        <result property="createBy"    column="create_by"    />
        <result property="createTime"    column="create_time"    />
        <result property="updateBy"    column="update_by"    />
        <result property="updateTime"    column="update_time"    />
        <result property="remark"    column="remark"    />
    </resultMap>

    <sql id="selectKgStockRecordVo">
        select record_id, item_id, record_type, record_date, quantity, unit_price, total_amount, before_stock, after_stock, supplier, recipient, purpose, operator_id, com_id, create_by, create_time, update_by, update_time, remark from kg_stock_record
    </sql>

    <select id="selectKgStockRecordList" parameterType="KgStockRecord" resultMap="KgStockRecordResult">
        <include refid="selectKgStockRecordVo"/>
        <where>  
            <if test="itemId != null "> and item_id = #{itemId}</if>
            <if test="recordType != null  and recordType != ''"> and record_type = #{recordType}</if>
            <if test="recordDate != null "> and record_date = #{recordDate}</if>
            <if test="quantity != null "> and quantity = #{quantity}</if>
            <if test="unitPrice != null "> and unit_price = #{unitPrice}</if>
            <if test="totalAmount != null "> and total_amount = #{totalAmount}</if>
            <if test="beforeStock != null "> and before_stock = #{beforeStock}</if>
            <if test="afterStock != null "> and after_stock = #{afterStock}</if>
            <if test="supplier != null  and supplier != ''"> and supplier = #{supplier}</if>
            <if test="recipient != null  and recipient != ''"> and recipient = #{recipient}</if>
            <if test="purpose != null  and purpose != ''"> and purpose = #{purpose}</if>
            <if test="operatorId != null "> and operator_id = #{operatorId}</if>
            <if test="comId != null  and comId != ''"> and com_id = #{comId}</if>
        </where>
    </select>
    
    <select id="selectKgStockRecordById" parameterType="Long" resultMap="KgStockRecordResult">
        <include refid="selectKgStockRecordVo"/>
        where record_id = #{recordId}
    </select>
        
    <insert id="insertKgStockRecord" parameterType="KgStockRecord" useGeneratedKeys="true" keyProperty="recordId">
        insert into kg_stock_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="itemId != null">item_id,</if>
            <if test="recordType != null and recordType != ''">record_type,</if>
            <if test="recordDate != null">record_date,</if>
            <if test="quantity != null">quantity,</if>
            <if test="unitPrice != null">unit_price,</if>
            <if test="totalAmount != null">total_amount,</if>
            <if test="beforeStock != null">before_stock,</if>
            <if test="afterStock != null">after_stock,</if>
            <if test="supplier != null">supplier,</if>
            <if test="recipient != null">recipient,</if>
            <if test="purpose != null">purpose,</if>
            <if test="operatorId != null">operator_id,</if>
            <if test="comId != null">com_id,</if>
            <if test="createBy != null">create_by,</if>
            <if test="createTime != null">create_time,</if>
            <if test="updateBy != null">update_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="remark != null">remark,</if>
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="itemId != null">#{itemId},</if>
            <if test="recordType != null and recordType != ''">#{recordType},</if>
            <if test="recordDate != null">#{recordDate},</if>
            <if test="quantity != null">#{quantity},</if>
            <if test="unitPrice != null">#{unitPrice},</if>
            <if test="totalAmount != null">#{totalAmount},</if>
            <if test="beforeStock != null">#{beforeStock},</if>
            <if test="afterStock != null">#{afterStock},</if>
            <if test="supplier != null">#{supplier},</if>
            <if test="recipient != null">#{recipient},</if>
            <if test="purpose != null">#{purpose},</if>
            <if test="operatorId != null">#{operatorId},</if>
            <if test="comId != null">#{comId},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="createTime != null">#{createTime},</if>
            <if test="updateBy != null">#{updateBy},</if>
            <if test="updateTime != null">#{updateTime},</if>
            <if test="remark != null">#{remark},</if>
         </trim>
    </insert>

    <update id="updateKgStockRecord" parameterType="KgStockRecord">
        update kg_stock_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="itemId != null">item_id = #{itemId},</if>
            <if test="recordType != null and recordType != ''">record_type = #{recordType},</if>
            <if test="recordDate != null">record_date = #{recordDate},</if>
            <if test="quantity != null">quantity = #{quantity},</if>
            <if test="unitPrice != null">unit_price = #{unitPrice},</if>
            <if test="totalAmount != null">total_amount = #{totalAmount},</if>
            <if test="beforeStock != null">before_stock = #{beforeStock},</if>
            <if test="afterStock != null">after_stock = #{afterStock},</if>
            <if test="supplier != null">supplier = #{supplier},</if>
            <if test="recipient != null">recipient = #{recipient},</if>
            <if test="purpose != null">purpose = #{purpose},</if>
            <if test="operatorId != null">operator_id = #{operatorId},</if>
            <if test="comId != null">com_id = #{comId},</if>
            <if test="createBy != null">create_by = #{createBy},</if>
            <if test="createTime != null">create_time = #{createTime},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="updateTime != null">update_time = #{updateTime},</if>
            <if test="remark != null">remark = #{remark},</if>
        </trim>
        where record_id = #{recordId}
    </update>

    <delete id="deleteKgStockRecordById" parameterType="Long">
        delete from kg_stock_record where record_id = #{recordId}
    </delete>

    <delete id="deleteKgStockRecordByIds" parameterType="String">
        delete from kg_stock_record where record_id in 
        <foreach item="recordId" collection="array" open="(" separator="," close=")">
            #{recordId}
        </foreach>
    </delete>
    
</mapper>
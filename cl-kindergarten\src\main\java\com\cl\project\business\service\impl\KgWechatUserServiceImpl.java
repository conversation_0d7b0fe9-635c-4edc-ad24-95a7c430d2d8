package com.cl.project.business.service.impl;

import java.util.List;
import com.cl.common.utils.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import com.cl.project.business.mapper.KgWechatUserMapper;
import com.cl.project.business.domain.KgWechatUser;
import com.cl.project.business.service.IKgWechatUserService;

/**
 * 微信用户Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@Service
public class KgWechatUserServiceImpl implements IKgWechatUserService 
{
    @Autowired
    private KgWechatUserMapper kgWechatUserMapper;

    /**
     * 查询微信用户
     * 
     * @param wechatUserId 微信用户ID
     * @return 微信用户
     */
    @Override
    public KgWechatUser selectKgWechatUserById(Long wechatUserId)
    {
        return kgWechatUserMapper.selectKgWechatUserById(wechatUserId);
    }

    /**
     * 查询微信用户列表
     * 
     * @param kgWechatUser 微信用户
     * @return 微信用户
     */
    @Override
    public List<KgWechatUser> selectKgWechatUserList(KgWechatUser kgWechatUser)
    {
        return kgWechatUserMapper.selectKgWechatUserList(kgWechatUser);
    }

    /**
     * 新增微信用户
     * 
     * @param kgWechatUser 微信用户
     * @return 结果
     */
    @Override
    public int insertKgWechatUser(KgWechatUser kgWechatUser)
    {
        kgWechatUser.setCreateTime(DateUtils.getNowDate());
        return kgWechatUserMapper.insertKgWechatUser(kgWechatUser);
    }

    /**
     * 修改微信用户
     * 
     * @param kgWechatUser 微信用户
     * @return 结果
     */
    @Override
    public int updateKgWechatUser(KgWechatUser kgWechatUser)
    {
        kgWechatUser.setUpdateTime(DateUtils.getNowDate());
        return kgWechatUserMapper.updateKgWechatUser(kgWechatUser);
    }

    /**
     * 批量删除微信用户
     * 
     * @param wechatUserIds 需要删除的微信用户ID
     * @return 结果
     */
    @Override
    public int deleteKgWechatUserByIds(Long[] wechatUserIds)
    {
        return kgWechatUserMapper.deleteKgWechatUserByIds(wechatUserIds);
    }

    /**
     * 删除微信用户信息
     * 
     * @param wechatUserId 微信用户ID
     * @return 结果
     */
    @Override
    public int deleteKgWechatUserById(Long wechatUserId)
    {
        return kgWechatUserMapper.deleteKgWechatUserById(wechatUserId);
    }
}

import request from '@/utils/request'

// 查询收入记录列表
export function listIncome(query) {
  return request({
    url: '/business/income/list',
    method: 'get',
    params: query
  })
}

// 查询收入记录详细
export function getIncome(incomeId) {
  return request({
    url: '/business/income/' + incomeId,
    method: 'get'
  })
}

// 新增收入记录
export function addIncome(data) {
  return request({
    url: '/business/income',
    method: 'post',
    data: data
  })
}

// 修改收入记录
export function updateIncome(data) {
  return request({
    url: '/business/income',
    method: 'put',
    data: data
  })
}

// 删除收入记录
export function delIncome(incomeId) {
  return request({
    url: '/business/income/' + incomeId,
    method: 'delete'
  })
}

// 导出收入记录
export function exportIncome(query) {
  return request({
    url: '/business/income/export',
    method: 'get',
    params: query
  })
}

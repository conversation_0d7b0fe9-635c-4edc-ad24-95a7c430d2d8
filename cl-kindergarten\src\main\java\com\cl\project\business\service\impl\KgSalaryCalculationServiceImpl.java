package com.cl.project.business.service.impl;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.*;
import java.util.stream.Collectors;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.cl.project.business.domain.*;
import com.cl.project.business.mapper.*;
import com.cl.project.business.service.IKgSalaryCalculationService;
import com.cl.project.business.service.IKgAttendanceStatisticsService;
import com.cl.common.utils.DateUtils;
import com.cl.common.utils.SecurityUtils;

/**
 * 工资计算Service业务层处理
 * 
 * <AUTHOR>
 * @date 2025-07-29
 */
@Service
public class KgSalaryCalculationServiceImpl implements IKgSalaryCalculationService 
{
    @Autowired
    private KgTeacherMapper teacherMapper;
    
    @Autowired
    private KgTeacherAttendanceMapper teacherAttendanceMapper;
    
    @Autowired
    private KgTeacherSalaryMapper teacherSalaryMapper;
    
    @Autowired
    private KgClassMapper classMapper;
    
    @Autowired
    private KgStudentMapper studentMapper;
    
    @Autowired
    private IKgAttendanceStatisticsService attendanceStatisticsService;

    /**
     * 计算教师月度工资
     */
    @Override
    public Map<String, Object> calculateMonthlySalary(Long teacherId, Integer year, Integer month) 
    {
        Map<String, Object> result = new HashMap<>();
        
        // 获取教师信息
        KgTeacher teacher = teacherMapper.selectKgTeacherById(teacherId);
        if (teacher == null) {
            throw new RuntimeException("教师不存在");
        }
        
        // 计算考勤统计
        LocalDate startDate = LocalDate.of(year, month, 1);
        LocalDate endDate = startDate.withDayOfMonth(startDate.lengthOfMonth());
        
        // 获取教师考勤统计
        Map<String, Object> attendanceStats = getTeacherAttendanceStats(teacherId, startDate, endDate);
        Integer attendanceDays = (Integer) attendanceStats.get("attendanceDays");
        Integer workDays = (Integer) attendanceStats.get("workDays");
        Double attendanceRate = (Double) attendanceStats.get("attendanceRate");
        
        // 计算各项工资组成
        Double baseSalary = calculateBaseSalary(teacherId, attendanceDays, workDays);
        Double attendanceBonus = calculateAttendanceBonus(teacherId, attendanceRate);
        Double courseFee = calculateCourseFee(teacherId, year, month);
        Double enrollmentBonus = calculateEnrollmentBonus(teacherId, year, month);
        Double attendanceRateBonus = calculateAttendanceRateBonus(teacherId, year, month);
        Double newStudentBonus = calculateNewStudentBonus(teacherId, year, month);
        Double withdrawalDeduction = calculateWithdrawalDeduction(teacherId, year, month);
        
        // 计算税前工资
        Double grossSalary = baseSalary + attendanceBonus + courseFee + enrollmentBonus 
                           + attendanceRateBonus + newStudentBonus - withdrawalDeduction;
        
        // 计算社保代扣
        Double socialInsuranceDeduction = calculateSocialInsuranceDeduction(teacherId, grossSalary);
        
        // 计算实发工资
        Double netSalary = grossSalary - socialInsuranceDeduction;
        
        // 组装工资明细
        Map<String, Object> salaryBreakdown = new HashMap<>();
        salaryBreakdown.put("baseSalary", baseSalary);
        salaryBreakdown.put("attendanceBonus", attendanceBonus);
        salaryBreakdown.put("courseFee", courseFee);
        salaryBreakdown.put("enrollmentBonus", enrollmentBonus);
        salaryBreakdown.put("attendanceRateBonus", attendanceRateBonus);
        salaryBreakdown.put("newStudentBonus", newStudentBonus);
        salaryBreakdown.put("withdrawalDeduction", withdrawalDeduction);
        salaryBreakdown.put("grossSalary", grossSalary);
        salaryBreakdown.put("socialInsuranceDeduction", socialInsuranceDeduction);
        salaryBreakdown.put("netSalary", netSalary);
        
        // 组装结果
        result.put("teacherInfo", teacher);
        result.put("attendanceStats", attendanceStats);
        result.put("salaryBreakdown", salaryBreakdown);
        result.put("calculationDate", DateUtils.getNowDate());
        
        return result;
    }

    /**
     * 批量计算全体教师工资
     */
    @Override
    public Map<String, Object> calculateAllSalaryBatch(Integer year, Integer month) 
    {
        Map<String, Object> result = new HashMap<>();
        
        // 获取所有教师
        List<KgTeacher> teachers = teacherMapper.selectKgTeacherList(new KgTeacher());
        
        List<Map<String, Object>> teacherResults = new ArrayList<>();
        double totalGrossSalary = 0;
        double totalNetSalary = 0;
        int successCount = 0;
        int errorCount = 0;
        
        for (KgTeacher teacher : teachers) {
            try {
                Map<String, Object> teacherResult = calculateMonthlySalary(teacher.getTeacherId(), year, month);
                Map<String, Object> salaryBreakdown = (Map<String, Object>) teacherResult.get("salaryBreakdown");
                
                teacherResult.put("teacherName", teacher.getTeacherName());
                teacherResult.put("position", teacher.getPosition());
                teacherResults.add(teacherResult);
                
                totalGrossSalary += (Double) salaryBreakdown.get("grossSalary");
                totalNetSalary += (Double) salaryBreakdown.get("netSalary");
                successCount++;
            } catch (Exception e) {
                Map<String, Object> errorResult = new HashMap<>();
                errorResult.put("teacherId", teacher.getTeacherId());
                errorResult.put("teacherName", teacher.getTeacherName());
                errorResult.put("error", e.getMessage());
                teacherResults.add(errorResult);
                errorCount++;
            }
        }
        
        Map<String, Object> summary = new HashMap<>();
        summary.put("totalTeachers", teachers.size());
        summary.put("successCount", successCount);
        summary.put("errorCount", errorCount);
        summary.put("totalGrossSalary", totalGrossSalary);
        summary.put("totalNetSalary", totalNetSalary);
        summary.put("averageGrossSalary", successCount > 0 ? totalGrossSalary / successCount : 0);
        summary.put("averageNetSalary", successCount > 0 ? totalNetSalary / successCount : 0);
        
        result.put("year", year);
        result.put("month", month);
        result.put("teacherResults", teacherResults);
        result.put("summary", summary);
        result.put("calculationTime", DateUtils.getNowDate());
        
        return result;
    }

    /**
     * 预览工资计算结果
     */
    @Override
    public Map<String, Object> previewSalaryCalculation(Long teacherId, Integer year, Integer month) 
    {
        // 预览与实际计算逻辑相同，但不保存到数据库
        return calculateMonthlySalary(teacherId, year, month);
    }

    /**
     * 获取工资计算规则
     */
    @Override
    public Map<String, Object> getSalaryCalculationRules(Long teacherId) 
    {
        Map<String, Object> rules = new HashMap<>();
        
        KgTeacher teacher = teacherMapper.selectKgTeacherById(teacherId);
        if (teacher == null) {
            throw new RuntimeException("教师不存在");
        }
        
        Map<String, String> calculationRules = new HashMap<>();
        calculationRules.put("baseSalaryRule", "基本工资 = 月基本工资 × (出勤天数 / 应出勤天数)");
        calculationRules.put("attendanceBonusRule", "满勤奖 = 出勤率 ≥ 95% ? 满勤奖金额 : 0");
        calculationRules.put("courseFeeRule", "课时费 = 课时数 × 课时单价");
        calculationRules.put("enrollmentBonusRule", "报名奖励 = 新报名学生数 × 报名奖励单价");
        calculationRules.put("attendanceRateBonusRule", "出勤率奖励 = 班级平均出勤率 ≥ 90% ? 奖励金额 : 0");
        calculationRules.put("newStudentBonusRule", "新生奖励 = 新生数量 × 新生奖励单价");
        calculationRules.put("withdrawalDeductionRule", "退园扣款 = 退园学生数 × 扣款单价");
        calculationRules.put("socialInsuranceRule", "社保代扣 = 税前工资 × 社保比例");
        
        rules.put("teacherInfo", teacher);
        rules.put("calculationRules", calculationRules);
        
        return rules;
    }

    /**
     * 生成工资单
     */
    @Override
    public int generatePayslips(List<Map<String, Object>> calculations) 
    {
        int count = 0;
        String currentUser = SecurityUtils.getUsername();
        
        for (Map<String, Object> calculation : calculations) {
            try {
                Map<String, Object> teacherInfo = (Map<String, Object>) calculation.get("teacherInfo");
                Map<String, Object> salaryBreakdown = (Map<String, Object>) calculation.get("salaryBreakdown");
                
                KgTeacherSalary salary = new KgTeacherSalary();
                salary.setTeacherId(((KgTeacher) teacherInfo).getTeacherId());
                salary.setSalaryMonth(Long.valueOf(String.format("%d%02d", 
                    Calendar.getInstance().get(Calendar.YEAR),
                    Calendar.getInstance().get(Calendar.MONTH) + 1)));
                salary.setBaseSalary(BigDecimal.valueOf((Double) salaryBreakdown.get("baseSalary")));
                salary.setAttendanceBonus(BigDecimal.valueOf((Double) salaryBreakdown.get("attendanceBonus")));
                salary.setCourseBonus(BigDecimal.valueOf((Double) salaryBreakdown.get("courseFee")));
                salary.setEnrollmentBonus(BigDecimal.valueOf((Double) salaryBreakdown.get("enrollmentBonus")));
                salary.setGrossSalary(BigDecimal.valueOf((Double) salaryBreakdown.get("grossSalary")));
                salary.setSocialInsurance(BigDecimal.valueOf((Double) salaryBreakdown.get("socialInsuranceDeduction")));
                salary.setNetSalary(BigDecimal.valueOf((Double) salaryBreakdown.get("netSalary")));
                salary.setSalaryStatus("pending");
                salary.setCreateBy(currentUser);
                salary.setCreateTime(DateUtils.getNowDate());
                
                teacherSalaryMapper.insertKgTeacherSalary(salary);
                count++;
            } catch (Exception e) {
                // 记录错误但继续处理其他记录
                continue;
            }
        }
        
        return count;
    }

    /**
     * 重新计算工资
     */
    @Override
    public Map<String, Object> recalculateSalary(Long salaryId, String reason) 
    {
        KgTeacherSalary originalSalary = teacherSalaryMapper.selectKgTeacherSalaryById(salaryId);
        if (originalSalary == null) {
            throw new RuntimeException("工资记录不存在");
        }
        
        // 解析工资月份
        Long salaryMonth = originalSalary.getSalaryMonth();
        Integer year = (int) (salaryMonth / 100);
        Integer month = (int) (salaryMonth % 100);
        
        // 重新计算
        Map<String, Object> newCalculation = calculateMonthlySalary(originalSalary.getTeacherId(), year, month);
        Map<String, Object> salaryBreakdown = (Map<String, Object>) newCalculation.get("salaryBreakdown");
        
        // 更新工资记录
        originalSalary.setBaseSalary(BigDecimal.valueOf((Double) salaryBreakdown.get("baseSalary")));
        originalSalary.setAttendanceBonus(BigDecimal.valueOf((Double) salaryBreakdown.get("attendanceBonus")));
        originalSalary.setCourseBonus(BigDecimal.valueOf((Double) salaryBreakdown.get("courseFee")));
        originalSalary.setEnrollmentBonus(BigDecimal.valueOf((Double) salaryBreakdown.get("enrollmentBonus")));
        originalSalary.setGrossSalary(BigDecimal.valueOf((Double) salaryBreakdown.get("grossSalary")));
        originalSalary.setSocialInsurance(BigDecimal.valueOf((Double) salaryBreakdown.get("socialInsuranceDeduction")));
        originalSalary.setNetSalary(BigDecimal.valueOf((Double) salaryBreakdown.get("netSalary")));
        originalSalary.setUpdateBy(SecurityUtils.getUsername());
        originalSalary.setUpdateTime(DateUtils.getNowDate());
        originalSalary.setRemark(reason);
        
        teacherSalaryMapper.updateKgTeacherSalary(originalSalary);
        
        return newCalculation;
    }

    /**
     * 工资调整
     */
    @Override
    public int adjustSalary(Long salaryId, String adjustType, Double adjustAmount, String reason) 
    {
        KgTeacherSalary salary = teacherSalaryMapper.selectKgTeacherSalaryById(salaryId);
        if (salary == null) {
            throw new RuntimeException("工资记录不存在");
        }
        
        BigDecimal adjustment = BigDecimal.valueOf(adjustAmount);
        
        switch (adjustType) {
            case "base_salary":
                salary.setBaseSalary(salary.getBaseSalary().add(adjustment));
                break;
            case "attendance_bonus":
                salary.setAttendanceBonus(salary.getAttendanceBonus().add(adjustment));
                break;
            case "course_fee":
                salary.setCourseBonus(salary.getCourseBonus().add(adjustment));
                break;
            case "enrollment_bonus":
                salary.setEnrollmentBonus(salary.getEnrollmentBonus().add(adjustment));
                break;
            default:
                throw new RuntimeException("不支持的调整类型: " + adjustType);
        }
        
        // 重新计算总工资
        BigDecimal newGrossSalary = salary.getBaseSalary()
                .add(salary.getAttendanceBonus())
                .add(salary.getCourseBonus())
                .add(salary.getEnrollmentBonus());
        salary.setGrossSalary(newGrossSalary);
        
        // 重新计算实发工资
        BigDecimal newNetSalary = newGrossSalary.subtract(salary.getSocialInsurance());
        salary.setNetSalary(newNetSalary);
        
        salary.setUpdateBy(SecurityUtils.getUsername());
        salary.setUpdateTime(DateUtils.getNowDate());
        salary.setRemark(reason);
        
        return teacherSalaryMapper.updateKgTeacherSalary(salary);
    }

    /**
     * 获取工资统计
     */
    @Override
    public Map<String, Object> getSalaryStatistics(Integer year, Integer month) 
    {
        Map<String, Object> statistics = new HashMap<>();
        
        // 构建查询条件
        KgTeacherSalary queryParams = new KgTeacherSalary();
        queryParams.setSalaryMonth(Long.valueOf(String.format("%d%02d", year, month)));
        
        List<KgTeacherSalary> salaries = teacherSalaryMapper.selectKgTeacherSalaryList(queryParams);
        
        if (salaries.isEmpty()) {
            statistics.put("totalRecords", 0);
            statistics.put("message", "暂无工资数据");
            return statistics;
        }
        
        // 统计各项数据
        BigDecimal totalBaseSalary = BigDecimal.ZERO;
        BigDecimal totalGrossSalary = BigDecimal.ZERO;
        BigDecimal totalNetSalary = BigDecimal.ZERO;
        BigDecimal totalDeductions = BigDecimal.ZERO;
        
        Map<String, Long> statusCount = salaries.stream()
                .collect(Collectors.groupingBy(
                    salary -> salary.getSalaryStatus(),
                    Collectors.counting()
                ));
        
        for (KgTeacherSalary salary : salaries) {
            totalBaseSalary = totalBaseSalary.add(salary.getBaseSalary());
            totalGrossSalary = totalGrossSalary.add(salary.getGrossSalary());
            totalNetSalary = totalNetSalary.add(salary.getNetSalary());
            totalDeductions = totalDeductions.add(salary.getSocialInsurance());
        }
        
        Map<String, Object> salaryBreakdown = new HashMap<>();
        salaryBreakdown.put("totalBaseSalary", totalBaseSalary);
        salaryBreakdown.put("totalGrossSalary", totalGrossSalary);
        salaryBreakdown.put("totalNetSalary", totalNetSalary);
        salaryBreakdown.put("totalDeductions", totalDeductions);
        salaryBreakdown.put("averageGrossSalary", totalGrossSalary.divide(BigDecimal.valueOf(salaries.size()), 2, RoundingMode.HALF_UP));
        salaryBreakdown.put("averageNetSalary", totalNetSalary.divide(BigDecimal.valueOf(salaries.size()), 2, RoundingMode.HALF_UP));
        
        statistics.put("year", year);
        statistics.put("month", month);
        statistics.put("totalRecords", salaries.size());
        statistics.put("salaryBreakdown", salaryBreakdown);
        statistics.put("statusStatistics", statusCount);
        
        return statistics;
    }

    /**
     * 获取教师工资明细
     */
    @Override
    public Map<String, Object> getSalaryDetails(Long teacherId, Integer year, Integer month) 
    {
        Map<String, Object> details = new HashMap<>();
        
        KgTeacher teacher = teacherMapper.selectKgTeacherById(teacherId);
        if (teacher == null) {
            throw new RuntimeException("教师不存在");
        }
        
        // 查询工资记录
        KgTeacherSalary queryParams = new KgTeacherSalary();
        queryParams.setTeacherId(teacherId);
        queryParams.setSalaryMonth(Long.valueOf(String.format("%d%02d", year, month)));
        
        List<KgTeacherSalary> salaries = teacherSalaryMapper.selectKgTeacherSalaryList(queryParams);
        
        details.put("teacherInfo", teacher);
        details.put("year", year);
        details.put("month", month);
        details.put("salaryRecords", salaries);
        
        return details;
    }

    /**
     * 工资发放确认
     */
    @Override
    public int confirmSalaryPayment(List<Long> salaryIds) 
    {
        int count = 0;
        String currentUser = SecurityUtils.getUsername();
        
        for (Long salaryId : salaryIds) {
            KgTeacherSalary salary = teacherSalaryMapper.selectKgTeacherSalaryById(salaryId);
            if (salary != null && !"paid".equals(salary.getSalaryStatus())) {
                salary.setSalaryStatus("paid");
                salary.setPaidTime(DateUtils.getNowDate());
                salary.setUpdateBy(currentUser);
                salary.setUpdateTime(DateUtils.getNowDate());
                
                teacherSalaryMapper.updateKgTeacherSalary(salary);
                count++;
            }
        }
        
        return count;
    }

    /**
     * 生成工资报表
     */
    @Override
    public String generateSalaryReport(Integer year, Integer month) 
    {
        Map<String, Object> statistics = getSalaryStatistics(year, month);
        
        // 这里可以集成报表生成工具（如JasperReports、POI等）
        // 现在简单返回一个文件路径
        String fileName = String.format("salary_report_%d_%02d.xlsx", year, month);
        String filePath = "/reports/" + fileName;
        
        // TODO: 实际的报表生成逻辑
        
        return filePath;
    }

    /**
     * 计算课时费统计
     */
    @Override
    public Map<String, Object> getCourseFeeStatistics(Long teacherId, Integer year, Integer month) 
    {
        Map<String, Object> statistics = new HashMap<>();
        
        // TODO: 实现课时费统计逻辑
        // 这里需要根据实际的课程安排和课时记录来计算
        
        statistics.put("teacherId", teacherId);
        statistics.put("year", year);
        statistics.put("month", month);
        statistics.put("totalCourseHours", 0);
        statistics.put("totalCourseFee", 0.0);
        statistics.put("courseDetails", new ArrayList<>());
        
        return statistics;
    }

    // ========== 私有辅助方法 ==========

    /**
     * 获取教师考勤统计
     */
    private Map<String, Object> getTeacherAttendanceStats(Long teacherId, LocalDate startDate, LocalDate endDate) 
    {
        Map<String, Object> stats = new HashMap<>();
        
        // 查询教师考勤记录
        KgTeacherAttendance queryParams = new KgTeacherAttendance();
        queryParams.setTeacherId(teacherId);
        List<KgTeacherAttendance> attendanceList = teacherAttendanceMapper.selectKgTeacherAttendanceList(queryParams);
        
        // 按日期过滤
        List<KgTeacherAttendance> filteredList = attendanceList.stream()
                .filter(attendance -> {
                    LocalDate attendanceDate = attendance.getAttendanceDate().toInstant()
                            .atZone(ZoneId.systemDefault())
                            .toLocalDate();
                    return !attendanceDate.isBefore(startDate) && !attendanceDate.isAfter(endDate);
                })
                .collect(Collectors.toList());
        
        // 统计工作日数（排除周末）
        int workDays = calculateWorkDays(startDate, endDate);
        int attendanceDays = filteredList.size();
        int absentDays = workDays - attendanceDays;
        
        // 计算出勤率
        double attendanceRate = workDays > 0 ? (double) attendanceDays / workDays * 100 : 0;
        
        stats.put("workDays", workDays);
        stats.put("attendanceDays", attendanceDays);
        stats.put("absentDays", absentDays);
        stats.put("attendanceRate", Math.round(attendanceRate * 100.0) / 100.0);
        stats.put("attendanceDetails", filteredList);
        
        return stats;
    }

    /**
     * 计算工作日数量（排除周末）
     */
    private int calculateWorkDays(LocalDate startDate, LocalDate endDate) 
    {
        int workDays = 0;
        LocalDate current = startDate;
        
        while (!current.isAfter(endDate)) {
            if (current.getDayOfWeek().getValue() < 6) { // 周一到周五
                workDays++;
            }
            current = current.plusDays(1);
        }
        
        return workDays;
    }

    // ========== 工资计算具体实现方法 ==========

    /**
     * 计算基本工资
     */
    @Override
    public Double calculateBaseSalary(Long teacherId, Integer attendanceDays, Integer workDays) 
    {
        KgTeacher teacher = teacherMapper.selectKgTeacherById(teacherId);
        if (teacher == null || teacher.getBaseSalary() == null) {
            return 0.0;
        }
        
        double baseSalaryAmount = teacher.getBaseSalary().doubleValue();
        if (workDays == 0) {
            return 0.0;
        }
        
        // 基本工资 = 月基本工资 × (出勤天数 / 应出勤天数)
        return baseSalaryAmount * attendanceDays / workDays;
    }

    /**
     * 计算满勤奖
     */
    @Override
    public Double calculateAttendanceBonus(Long teacherId, Double attendanceRate) 
    {
        KgTeacher teacher = teacherMapper.selectKgTeacherById(teacherId);
        if (teacher == null) {
            return 0.0;
        }
        
        // 出勤率 ≥ 95% 才有满勤奖，固定金额200元
        if (attendanceRate >= 95.0) {
            return 200.0;
        }
        
        return 0.0;
    }

    /**
     * 计算课时费
     */
    @Override
    public Double calculateCourseFee(Long teacherId, Integer year, Integer month) 
    {
        // TODO: 根据实际课程安排计算课时费
        // 这里需要查询教师的课程安排和课时记录
        return 0.0;
    }

    /**
     * 计算报名奖励
     */
    @Override
    public Double calculateEnrollmentBonus(Long teacherId, Integer year, Integer month) 
    {
        // TODO: 根据新报名学生数计算奖励
        // 这里需要查询该月新报名的学生数量
        return 0.0;
    }

    /**
     * 计算出勤率奖励
     */
    @Override
    public Double calculateAttendanceRateBonus(Long teacherId, Integer year, Integer month) 
    {
        // TODO: 根据班级平均出勤率计算奖励
        return 0.0;
    }

    /**
     * 计算新生奖励
     */
    @Override
    public Double calculateNewStudentBonus(Long teacherId, Integer year, Integer month) 
    {
        // TODO: 根据新生数量计算奖励
        return 0.0;
    }

    /**
     * 计算退园扣款
     */
    @Override
    public Double calculateWithdrawalDeduction(Long teacherId, Integer year, Integer month) 
    {
        // TODO: 根据退园学生数计算扣款
        return 0.0;
    }

    /**
     * 计算社保代扣
     */
    @Override
    public Double calculateSocialInsuranceDeduction(Long teacherId, Double grossSalary) 
    {
        // TODO: 根据社保比例计算代扣金额
        // 一般社保个人承担比例约为8-11%
        double socialInsuranceRate = 0.105; // 10.5%
        return grossSalary * socialInsuranceRate;
    }
}

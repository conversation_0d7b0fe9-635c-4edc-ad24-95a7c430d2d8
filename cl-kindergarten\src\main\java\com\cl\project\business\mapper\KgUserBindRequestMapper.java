package com.cl.project.business.mapper;

import java.util.List;
import com.cl.project.business.domain.KgUserBindRequest;

/**
 * 用户绑定申请记录Mapper接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface KgUserBindRequestMapper 
{
    /**
     * 查询用户绑定申请记录
     * 
     * @param requestId 用户绑定申请记录ID
     * @return 用户绑定申请记录
     */
    public KgUserBindRequest selectKgUserBindRequestById(Long requestId);

    /**
     * 查询用户绑定申请记录列表
     * 
     * @param kgUserBindRequest 用户绑定申请记录
     * @return 用户绑定申请记录集合
     */
    public List<KgUserBindRequest> selectKgUserBindRequestList(KgUserBindRequest kgUserBindRequest);

    /**
     * 新增用户绑定申请记录
     * 
     * @param kgUserBindRequest 用户绑定申请记录
     * @return 结果
     */
    public int insertKgUserBindRequest(KgUserBindRequest kgUserBindRequest);

    /**
     * 修改用户绑定申请记录
     * 
     * @param kgUserBindRequest 用户绑定申请记录
     * @return 结果
     */
    public int updateKgUserBindRequest(KgUserBindRequest kgUserBindRequest);

    /**
     * 删除用户绑定申请记录
     * 
     * @param requestId 用户绑定申请记录ID
     * @return 结果
     */
    public int deleteKgUserBindRequestById(Long requestId);

    /**
     * 批量删除用户绑定申请记录
     * 
     * @param requestIds 需要删除的数据ID
     * @return 结果
     */
    public int deleteKgUserBindRequestByIds(Long[] requestIds);
}

package com.cl.project.business.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 教师考勤记录对象 kg_teacher_attendance
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgTeacherAttendance extends BaseEntity
{
    /** 教师姓名 */
    @Excel(name = "教师姓名")
    private String teacherName;

    private static final long serialVersionUID = 1L;

    /** 数据来源（dingtalk/钉钉，manual/手动，mixed/混合） */
    @Excel(name = "数据来源")
    private String dataSource;

    /** 考勤ID */
    private Long attendanceId;

    /** 教师ID，关联kg_teacher.teacher_id */
    @Excel(name = "教师ID，关联kg_teacher.teacher_id")
    private Long teacherId;

    /** 考勤日期 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "考勤日期", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date attendanceDate;

    /** 签到时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "签到时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date checkInTime;

    /** 考勤状态（1-出勤，3-缺勤，4-请假） */
    @Excel(name = "考勤状态", readConverterExp = "1=出勤,3=缺勤,4=请假")
    private String attendanceStatus;

    /** 工作时长 */
    @Excel(name = "工作时长")
    private BigDecimal workHours;

    /** 签到方式（face人脸、manual手动） */
    @Excel(name = "签到方式", readConverterExp = "f=ace人脸、manual手动")
    private String checkInMethod;

    /** 是否确认（0未确认 1已确认） */
    @Excel(name = "是否确认", readConverterExp = "0=未确认,1=已确认")
    private Long isConfirmed;

    /** 确认人ID，关联kg_teacher.teacher_id */
    @Excel(name = "确认人ID，关联kg_teacher.teacher_id")
    private Long confirmedBy;

    /** 确认时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "确认时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date confirmedTime;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    public void setAttendanceId(Long attendanceId) 
    {
        this.attendanceId = attendanceId;
    }

    public Long getAttendanceId() 
    {
        return attendanceId;
    }
    public void setTeacherId(Long teacherId) 
    {
        this.teacherId = teacherId;
    }

    public Long getTeacherId() 
    {
        return teacherId;
    }
    public void setAttendanceDate(Date attendanceDate) 
    {
        this.attendanceDate = attendanceDate;
    }

    public Date getAttendanceDate() 
    {
        return attendanceDate;
    }
    public void setCheckInTime(Date checkInTime) 
    {
        this.checkInTime = checkInTime;
    }

    public Date getCheckInTime() 
    {
        return checkInTime;
    }
    public void setAttendanceStatus(String attendanceStatus) 
    {
        this.attendanceStatus = attendanceStatus;
    }

    public String getAttendanceStatus() 
    {
        return attendanceStatus;
    }
    public void setWorkHours(BigDecimal workHours) 
    {
        this.workHours = workHours;
    }

    public BigDecimal getWorkHours() 
    {
        return workHours;
    }
    public void setCheckInMethod(String checkInMethod) 
    {
        this.checkInMethod = checkInMethod;
    }

    public String getCheckInMethod() 
    {
        return checkInMethod;
    }
    public void setIsConfirmed(Long isConfirmed) 
    {
        this.isConfirmed = isConfirmed;
    }

    public Long getIsConfirmed() 
    {
        return isConfirmed;
    }
    public void setConfirmedBy(Long confirmedBy) 
    {
        this.confirmedBy = confirmedBy;
    }

    public Long getConfirmedBy() 
    {
        return confirmedBy;
    }
    public void setConfirmedTime(Date confirmedTime) 
    {
        this.confirmedTime = confirmedTime;
    }

    public Date getConfirmedTime() 
    {
        return confirmedTime;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    public String getTeacherName() {
        return teacherName;
    }

    public void setTeacherName(String teacherName) {
        this.teacherName = teacherName;
    }

    public String getDataSource() {
        return dataSource;
    }

    public void setDataSource(String dataSource) {
        this.dataSource = dataSource;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("attendanceId", getAttendanceId())
            .append("teacherId", getTeacherId())
            .append("attendanceDate", getAttendanceDate())
            .append("checkInTime", getCheckInTime())
            .append("attendanceStatus", getAttendanceStatus())
            .append("workHours", getWorkHours())
            .append("checkInMethod", getCheckInMethod())
            .append("isConfirmed", getIsConfirmed())
            .append("confirmedBy", getConfirmedBy())
            .append("confirmedTime", getConfirmedTime())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

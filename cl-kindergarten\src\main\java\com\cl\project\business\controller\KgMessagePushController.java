package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgMessagePush;
import com.cl.project.business.service.IKgMessagePushService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 消息推送记录Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/push")
public class KgMessagePushController extends BaseController
{
    @Autowired
    private IKgMessagePushService kgMessagePushService;

    /**
     * 查询消息推送记录列表
     */
    @SaCheckPermission("business:push:list")
    @GetMapping("/list")
    public TableDataInfo list(KgMessagePush kgMessagePush)
    {
        startPage();
        List<KgMessagePush> list = kgMessagePushService.selectKgMessagePushList(kgMessagePush);
        return getDataTable(list);
    }

    /**
     * 导出消息推送记录列表
     */
    @SaCheckPermission("business:push:export")
    @Log(title = "消息推送记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgMessagePush kgMessagePush)
    {
        List<KgMessagePush> list = kgMessagePushService.selectKgMessagePushList(kgMessagePush);
        ExcelUtil<KgMessagePush> util = new ExcelUtil<KgMessagePush>(KgMessagePush.class);
        return util.exportExcel(list, "push");
    }

    /**
     * 获取消息推送记录详细信息
     */
    @SaCheckPermission("business:push:query")
    @GetMapping(value = "/{pushId}")
    public AjaxResult getInfo(@PathVariable("pushId") Long pushId)
    {
        return AjaxResult.success(kgMessagePushService.selectKgMessagePushById(pushId));
    }

    /**
     * 新增消息推送记录
     */
    @SaCheckPermission("business:push:add")
    @Log(title = "消息推送记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgMessagePush kgMessagePush)
    {
        return toAjax(kgMessagePushService.insertKgMessagePush(kgMessagePush));
    }

    /**
     * 修改消息推送记录
     */
    @SaCheckPermission("business:push:edit")
    @Log(title = "消息推送记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgMessagePush kgMessagePush)
    {
        return toAjax(kgMessagePushService.updateKgMessagePush(kgMessagePush));
    }

    /**
     * 删除消息推送记录
     */
    @SaCheckPermission("business:push:remove")
    @Log(title = "消息推送记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{pushIds}")
    public AjaxResult remove(@PathVariable Long[] pushIds)
    {
        return toAjax(kgMessagePushService.deleteKgMessagePushByIds(pushIds));
    }
}

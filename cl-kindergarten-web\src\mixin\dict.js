import { getDicts } from '@/api/system/dict/data'

export default {
  data() {
    return {
      dict: {
        type: {}
      }
    }
  },
  created() {
    // 自动加载页面声明的 dicts
    if (this.$options.dicts && Array.isArray(this.$options.dicts)) {
      this.$options.dicts.forEach(dictType => {
        getDicts(dictType).then(res => {
          this.$set(this.dict.type, dictType, res.data)
        })
      })
    }
  }
}

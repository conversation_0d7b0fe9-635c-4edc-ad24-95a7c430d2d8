package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgExpenseType;
import com.cl.project.business.service.IKgExpenseTypeService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 支出类型Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/type")
public class KgExpenseTypeController extends BaseController
{
    @Autowired
    private IKgExpenseTypeService kgExpenseTypeService;

    /**
     * 查询支出类型列表
     */
    @SaCheckPermission("business:type:list")
    @GetMapping("/list")
    public TableDataInfo list(KgExpenseType kgExpenseType)
    {
        startPage();
        List<KgExpenseType> list = kgExpenseTypeService.selectKgExpenseTypeList(kgExpenseType);
        return getDataTable(list);
    }

    /**
     * 导出支出类型列表
     */
    @SaCheckPermission("business:type:export")
    @Log(title = "支出类型", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgExpenseType kgExpenseType)
    {
        List<KgExpenseType> list = kgExpenseTypeService.selectKgExpenseTypeList(kgExpenseType);
        ExcelUtil<KgExpenseType> util = new ExcelUtil<KgExpenseType>(KgExpenseType.class);
        return util.exportExcel(list, "type");
    }

    /**
     * 获取支出类型详细信息
     */
    @SaCheckPermission("business:type:query")
    @GetMapping(value = "/{typeId}")
    public AjaxResult getInfo(@PathVariable("typeId") Long typeId)
    {
        return AjaxResult.success(kgExpenseTypeService.selectKgExpenseTypeById(typeId));
    }

    /**
     * 新增支出类型
     */
    @SaCheckPermission("business:type:add")
    @Log(title = "支出类型", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgExpenseType kgExpenseType)
    {
        return toAjax(kgExpenseTypeService.insertKgExpenseType(kgExpenseType));
    }

    /**
     * 修改支出类型
     */
    @SaCheckPermission("business:type:edit")
    @Log(title = "支出类型", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgExpenseType kgExpenseType)
    {
        return toAjax(kgExpenseTypeService.updateKgExpenseType(kgExpenseType));
    }

    /**
     * 删除支出类型
     */
    @SaCheckPermission("business:type:remove")
    @Log(title = "支出类型", businessType = BusinessType.DELETE)
	@DeleteMapping("/{typeIds}")
    public AjaxResult remove(@PathVariable Long[] typeIds)
    {
        return toAjax(kgExpenseTypeService.deleteKgExpenseTypeByIds(typeIds));
    }
}

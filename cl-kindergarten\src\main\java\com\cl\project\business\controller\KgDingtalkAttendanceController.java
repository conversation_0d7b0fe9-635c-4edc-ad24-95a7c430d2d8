package com.cl.project.business.controller;

import java.util.List;
import cn.dev33.satoken.annotation.SaCheckPermission;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.cl.framework.aspectj.lang.annotation.Log;
import com.cl.framework.aspectj.lang.enums.BusinessType;
import com.cl.project.business.domain.KgDingtalkAttendance;
import com.cl.project.business.service.IKgDingtalkAttendanceService;
import com.cl.framework.web.controller.BaseController;
import com.cl.framework.web.domain.AjaxResult;
import com.cl.common.utils.poi.ExcelUtil;
import com.cl.framework.web.page.TableDataInfo;

/**
 * 钉钉打卡记录Controller
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
@RestController
@RequestMapping("/business/dingtalk-attendance")
public class KgDingtalkAttendanceController extends BaseController
{
    @Autowired
    private IKgDingtalkAttendanceService kgDingtalkAttendanceService;

    /**
     * 查询钉钉打卡记录列表
     */
//    @SaCheckPermission("business:attendance:list")
    @GetMapping("/list")
    public TableDataInfo list(KgDingtalkAttendance kgDingtalkAttendance)
    {
        startPage();
        List<KgDingtalkAttendance> list = kgDingtalkAttendanceService.selectKgDingtalkAttendanceList(kgDingtalkAttendance);
        return getDataTable(list);
    }

    /**
     * 导出钉钉打卡记录列表
     */
    @SaCheckPermission("business:attendance:export")
    @Log(title = "钉钉打卡记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(KgDingtalkAttendance kgDingtalkAttendance)
    {
        List<KgDingtalkAttendance> list = kgDingtalkAttendanceService.selectKgDingtalkAttendanceList(kgDingtalkAttendance);
        ExcelUtil<KgDingtalkAttendance> util = new ExcelUtil<KgDingtalkAttendance>(KgDingtalkAttendance.class);
        return util.exportExcel(list, "attendance");
    }

    /**
     * 获取钉钉打卡记录详细信息
     */
    @SaCheckPermission("business:attendance:query")
    @GetMapping(value = "/{recordId}")
    public AjaxResult getInfo(@PathVariable("recordId") Long recordId)
    {
        return AjaxResult.success(kgDingtalkAttendanceService.selectKgDingtalkAttendanceById(recordId));
    }

    /**
     * 新增钉钉打卡记录
     */
    @SaCheckPermission("business:attendance:add")
    @Log(title = "钉钉打卡记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody KgDingtalkAttendance kgDingtalkAttendance)
    {
        return toAjax(kgDingtalkAttendanceService.insertKgDingtalkAttendance(kgDingtalkAttendance));
    }

    /**
     * 修改钉钉打卡记录
     */
    @SaCheckPermission("business:attendance:edit")
    @Log(title = "钉钉打卡记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody KgDingtalkAttendance kgDingtalkAttendance)
    {
        return toAjax(kgDingtalkAttendanceService.updateKgDingtalkAttendance(kgDingtalkAttendance));
    }

    /**
     * 删除钉钉打卡记录
     */
    @SaCheckPermission("business:attendance:remove")
    @Log(title = "钉钉打卡记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{recordIds}")
    public AjaxResult remove(@PathVariable Long[] recordIds)
    {
        return toAjax(kgDingtalkAttendanceService.deleteKgDingtalkAttendanceByIds(recordIds));
    }
}

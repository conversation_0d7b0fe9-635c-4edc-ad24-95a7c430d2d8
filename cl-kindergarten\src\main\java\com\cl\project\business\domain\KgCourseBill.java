package com.cl.project.business.domain;

import java.math.BigDecimal;
import java.util.Date;

import com.cl.framework.web.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.cl.framework.aspectj.lang.annotation.Excel;

/**
 * 托管费账单对象 kg_course_bill
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public class KgCourseBill extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 账单ID */
    private Long billId;

    /** 幼儿ID，关联kg_student.student_id */
    @Excel(name = "幼儿ID，关联kg_student.student_id")
    private Long studentId;

    /** 账单年份 */
    @Excel(name = "账单年份")
    private Long billYear;

    /** 账单月份 */
    @Excel(name = "账单月份")
    private Long billMonth;

    /** 总金额 */
    @Excel(name = "总金额")
    private BigDecimal totalAmount;

    /** 赠送课时数 */
    @Excel(name = "赠送课时数")
    private Long giftSessions;

    /** 赠送课程名称 */
    @Excel(name = "赠送课程名称")
    private String giftCourseName;

    /** 账单状态（generated已生成、sent已发送、paid已支付） */
    @Excel(name = "账单状态", readConverterExp = "g=enerated已生成、sent已发送、paid已支付")
    private String billStatus;

    /** 发送时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发送时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date sentTime;

    /** 支付时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "支付时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date paidTime;

    /** 公司ID，多租户隔离 */
    @Excel(name = "公司ID，多租户隔离")
    private String comId;

    public void setBillId(Long billId) 
    {
        this.billId = billId;
    }

    public Long getBillId() 
    {
        return billId;
    }
    public void setStudentId(Long studentId) 
    {
        this.studentId = studentId;
    }

    public Long getStudentId() 
    {
        return studentId;
    }
    public void setBillYear(Long billYear) 
    {
        this.billYear = billYear;
    }

    public Long getBillYear() 
    {
        return billYear;
    }
    public void setBillMonth(Long billMonth) 
    {
        this.billMonth = billMonth;
    }

    public Long getBillMonth() 
    {
        return billMonth;
    }
    public void setTotalAmount(BigDecimal totalAmount) 
    {
        this.totalAmount = totalAmount;
    }

    public BigDecimal getTotalAmount() 
    {
        return totalAmount;
    }
    public void setGiftSessions(Long giftSessions) 
    {
        this.giftSessions = giftSessions;
    }

    public Long getGiftSessions() 
    {
        return giftSessions;
    }
    public void setGiftCourseName(String giftCourseName) 
    {
        this.giftCourseName = giftCourseName;
    }

    public String getGiftCourseName() 
    {
        return giftCourseName;
    }
    public void setBillStatus(String billStatus) 
    {
        this.billStatus = billStatus;
    }

    public String getBillStatus() 
    {
        return billStatus;
    }
    public void setSentTime(Date sentTime) 
    {
        this.sentTime = sentTime;
    }

    public Date getSentTime() 
    {
        return sentTime;
    }
    public void setPaidTime(Date paidTime) 
    {
        this.paidTime = paidTime;
    }

    public Date getPaidTime() 
    {
        return paidTime;
    }
    public void setComId(String comId) 
    {
        this.comId = comId;
    }

    public String getComId() 
    {
        return comId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("billId", getBillId())
            .append("studentId", getStudentId())
            .append("billYear", getBillYear())
            .append("billMonth", getBillMonth())
            .append("totalAmount", getTotalAmount())
            .append("giftSessions", getGiftSessions())
            .append("giftCourseName", getGiftCourseName())
            .append("billStatus", getBillStatus())
            .append("sentTime", getSentTime())
            .append("paidTime", getPaidTime())
            .append("comId", getComId())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}

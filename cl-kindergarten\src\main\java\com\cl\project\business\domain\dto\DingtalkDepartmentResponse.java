package com.cl.project.business.domain.dto;

import java.util.List;

/**
 * 钉钉获取部门列表响应
 * 
 * <AUTHOR>
 * @date 2025-07-30
 */
public class DingtalkDepartmentResponse 
{
    private Integer errcode;
    private String errmsg;
    private String request_id;
    private Result result;
    
    public static class Result 
    {
        private List<Department> parent_list;
        
        public List<Department> getParent_list() 
        {
            return parent_list;
        }
        
        public void setParent_list(List<Department> parent_list) 
        {
            this.parent_list = parent_list;
        }
    }
    
    public static class Department 
    {
        private Long dept_id;
        private String name;
        private Long parent_id;
        private Boolean create_dept_group;
        private Boolean auto_add_user;
        private String from_union_org;
        private String tags;
        private Integer order;
        private String dept_group_chat_id;
        private String source_identifier;
        private Boolean ext;
        private String org_dept_owner;
        private Integer dept_permits;
        private Integer user_permits;
        private Boolean outer_dept;
        private String outer_permit_depts;
        private String outer_permit_users;
        private Boolean hide_dept;
        private String dept_manager_userid_list;
        private Boolean group_contain_sub_dept;
        private String org_group_chat_id;
        private Boolean group_contain_outer_user;
        private Boolean group_contain_hidden_user;
        
        public Long getDept_id() 
        {
            return dept_id;
        }
        
        public void setDept_id(Long dept_id) 
        {
            this.dept_id = dept_id;
        }
        
        public String getName() 
        {
            return name;
        }
        
        public void setName(String name) 
        {
            this.name = name;
        }
        
        public Long getParent_id() 
        {
            return parent_id;
        }
        
        public void setParent_id(Long parent_id) 
        {
            this.parent_id = parent_id;
        }
        
        public Boolean getCreate_dept_group() 
        {
            return create_dept_group;
        }
        
        public void setCreate_dept_group(Boolean create_dept_group) 
        {
            this.create_dept_group = create_dept_group;
        }
        
        public Boolean getAuto_add_user() 
        {
            return auto_add_user;
        }
        
        public void setAuto_add_user(Boolean auto_add_user) 
        {
            this.auto_add_user = auto_add_user;
        }
        
        public String getFrom_union_org() 
        {
            return from_union_org;
        }
        
        public void setFrom_union_org(String from_union_org) 
        {
            this.from_union_org = from_union_org;
        }
        
        public String getTags() 
        {
            return tags;
        }
        
        public void setTags(String tags) 
        {
            this.tags = tags;
        }
        
        public Integer getOrder() 
        {
            return order;
        }
        
        public void setOrder(Integer order) 
        {
            this.order = order;
        }
        
        public String getDept_group_chat_id() 
        {
            return dept_group_chat_id;
        }
        
        public void setDept_group_chat_id(String dept_group_chat_id) 
        {
            this.dept_group_chat_id = dept_group_chat_id;
        }
        
        public String getSource_identifier() 
        {
            return source_identifier;
        }
        
        public void setSource_identifier(String source_identifier) 
        {
            this.source_identifier = source_identifier;
        }
        
        public Boolean getExt() 
        {
            return ext;
        }
        
        public void setExt(Boolean ext) 
        {
            this.ext = ext;
        }
        
        public String getOrg_dept_owner() 
        {
            return org_dept_owner;
        }
        
        public void setOrg_dept_owner(String org_dept_owner) 
        {
            this.org_dept_owner = org_dept_owner;
        }
        
        public Integer getDept_permits() 
        {
            return dept_permits;
        }
        
        public void setDept_permits(Integer dept_permits) 
        {
            this.dept_permits = dept_permits;
        }
        
        public Integer getUser_permits() 
        {
            return user_permits;
        }
        
        public void setUser_permits(Integer user_permits) 
        {
            this.user_permits = user_permits;
        }
        
        public Boolean getOuter_dept() 
        {
            return outer_dept;
        }
        
        public void setOuter_dept(Boolean outer_dept) 
        {
            this.outer_dept = outer_dept;
        }
        
        public String getOuter_permit_depts() 
        {
            return outer_permit_depts;
        }
        
        public void setOuter_permit_depts(String outer_permit_depts) 
        {
            this.outer_permit_depts = outer_permit_depts;
        }
        
        public String getOuter_permit_users() 
        {
            return outer_permit_users;
        }
        
        public void setOuter_permit_users(String outer_permit_users) 
        {
            this.outer_permit_users = outer_permit_users;
        }
        
        public Boolean getHide_dept() 
        {
            return hide_dept;
        }
        
        public void setHide_dept(Boolean hide_dept) 
        {
            this.hide_dept = hide_dept;
        }
        
        public String getDept_manager_userid_list() 
        {
            return dept_manager_userid_list;
        }
        
        public void setDept_manager_userid_list(String dept_manager_userid_list) 
        {
            this.dept_manager_userid_list = dept_manager_userid_list;
        }
        
        public Boolean getGroup_contain_sub_dept() 
        {
            return group_contain_sub_dept;
        }
        
        public void setGroup_contain_sub_dept(Boolean group_contain_sub_dept) 
        {
            this.group_contain_sub_dept = group_contain_sub_dept;
        }
        
        public String getOrg_group_chat_id() 
        {
            return org_group_chat_id;
        }
        
        public void setOrg_group_chat_id(String org_group_chat_id) 
        {
            this.org_group_chat_id = org_group_chat_id;
        }
        
        public Boolean getGroup_contain_outer_user() 
        {
            return group_contain_outer_user;
        }
        
        public void setGroup_contain_outer_user(Boolean group_contain_outer_user) 
        {
            this.group_contain_outer_user = group_contain_outer_user;
        }
        
        public Boolean getGroup_contain_hidden_user() 
        {
            return group_contain_hidden_user;
        }
        
        public void setGroup_contain_hidden_user(Boolean group_contain_hidden_user) 
        {
            this.group_contain_hidden_user = group_contain_hidden_user;
        }
    }
    
    public Integer getErrcode() 
    {
        return errcode;
    }
    
    public void setErrcode(Integer errcode) 
    {
        this.errcode = errcode;
    }
    
    public String getErrmsg() 
    {
        return errmsg;
    }
    
    public void setErrmsg(String errmsg) 
    {
        this.errmsg = errmsg;
    }
    
    public String getRequest_id() 
    {
        return request_id;
    }
    
    public void setRequest_id(String request_id) 
    {
        this.request_id = request_id;
    }
    
    public Result getResult() 
    {
        return result;
    }
    
    public void setResult(Result result) 
    {
        this.result = result;
    }
    
    /**
     * 判断请求是否成功
     */
    public boolean isSuccess() 
    {
        return errcode != null && errcode == 0;
    }
}

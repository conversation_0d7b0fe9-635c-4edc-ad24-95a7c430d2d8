import request from '@/utils/request'

// 查询手机号验证码列表
export function listVerification(query) {
  return request({
    url: '/business/verification/list',
    method: 'get',
    params: query
  })
}

// 查询手机号验证码详细
export function getVerification(verificationId) {
  return request({
    url: '/business/verification/' + verificationId,
    method: 'get'
  })
}

// 新增手机号验证码
export function addVerification(data) {
  return request({
    url: '/business/verification',
    method: 'post',
    data: data
  })
}

// 修改手机号验证码
export function updateVerification(data) {
  return request({
    url: '/business/verification',
    method: 'put',
    data: data
  })
}

// 删除手机号验证码
export function delVerification(verificationId) {
  return request({
    url: '/business/verification/' + verificationId,
    method: 'delete'
  })
}

// 导出手机号验证码
export function exportVerification(query) {
  return request({
    url: '/business/verification/export',
    method: 'get',
    params: query
  })
}

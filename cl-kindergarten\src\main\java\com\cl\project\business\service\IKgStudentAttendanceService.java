package com.cl.project.business.service;

import java.util.List;
import java.util.Date;
import com.cl.project.business.domain.KgStudentAttendance;
import com.cl.project.business.domain.dto.BatchConfirmAttendanceDto;
import com.cl.project.business.domain.dto.StudentAttendanceOverviewDto;
import com.cl.project.business.domain.dto.BatchStudentCheckinDto;

/**
 * 学生考勤记录Service接口
 * 
 * <AUTHOR>
 * @date 2025-07-28
 */
public interface IKgStudentAttendanceService 
{
    /**
     * 查询学生考勤记录
     * 
     * @param attendanceId 学生考勤记录ID
     * @return 学生考勤记录
     */
    public KgStudentAttendance selectKgStudentAttendanceById(Long attendanceId);

    /**
     * 查询学生考勤记录列表
     * 
     * @param kgStudentAttendance 学生考勤记录
     * @return 学生考勤记录集合
     */
    public List<KgStudentAttendance> selectKgStudentAttendanceList(KgStudentAttendance kgStudentAttendance);

    /**
     * 新增学生考勤记录
     * 
     * @param kgStudentAttendance 学生考勤记录
     * @return 结果
     */
    public int insertKgStudentAttendance(KgStudentAttendance kgStudentAttendance);

    /**
     * 修改学生考勤记录
     * 
     * @param kgStudentAttendance 学生考勤记录
     * @return 结果
     */
    public int updateKgStudentAttendance(KgStudentAttendance kgStudentAttendance);

    /**
     * 批量删除学生考勤记录
     * 
     * @param attendanceIds 需要删除的学生考勤记录ID
     * @return 结果
     */
    public int deleteKgStudentAttendanceByIds(Long[] attendanceIds);

    /**
     * 删除学生考勤记录信息
     * 
     * @param attendanceId 学生考勤记录ID
     * @return 结果
     */
    public int deleteKgStudentAttendanceById(Long attendanceId);

    /**
     * 查询学生考勤概览列表
     * 展示所有学生及其指定日期的考勤状态
     * 
     * @param attendanceDate 考勤日期，为null时查询当日
     * @param studentName 学生姓名
     * @param classId 班级ID
     * @param attendanceStatus 考勤状态
     * @param dataSource 数据来源
     * @return 学生考勤概览集合
     */
    public List<StudentAttendanceOverviewDto> selectStudentAttendanceOverview(Date attendanceDate, String studentName, Long classId, String attendanceStatus, String dataSource);

    /**
     * 学生签到
     * 
     * @param kgStudentAttendance 学生考勤记录
     * @return 结果
     */
    public int studentCheckin(KgStudentAttendance kgStudentAttendance);

    /**
     * 批量签到学生考勤
     * 
     * @param batchDto 批量导入数据
     * @return 影响行数
     */
    public int batchStudentCheckin(BatchStudentCheckinDto batchDto);

    /**
     * 单个确认学生考勤
     * 
     * @param attendanceId 考勤记录ID
     * @return 结果
     */
    public int confirmStudentAttendance(Long attendanceId);

    /**
     * 批量确认学生考勤
     * 
     * @param batchDto 批量确认数据
     * @return 影响行数
     */
    public int batchConfirmAttendance(BatchConfirmAttendanceDto batchDto);

    /**
     * 缺勤登记
     * 
     * @param kgStudentAttendance 学生考勤记录
     * @return 结果
     */
    public int registerAbsence(KgStudentAttendance kgStudentAttendance);
}
